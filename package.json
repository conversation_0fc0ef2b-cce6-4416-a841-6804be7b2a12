{"name": "dx-desktop", "version": "7.14.0", "description": "大象PC架构升级版", "main": "./backend/dist/main", "author": "北京三快在线科技有限公司", "private": true, "scripts": {"new": "plop", "preinstall": "pnpm clean:tsc-cache && node ./scripts/node/preinstall.js", "postinstall": "npx zx ./deploy-scripts/zx/update-mtd-react-css.mjs", "clean": "turbo run clean --force", "build": "turbo run build", "build:prod": "cross-env DX_ENV=production pnpm build --force", "build:dev": "cross-env DX_ENV=test pnpm build --force", "build:st": "cross-env DX_ENV=staging pnpm build --force", "prepare": "husky install", "test": "npm run test:unit && pnpm -F @xm/js-native test", "test:unit": "jest", "start": "cross-env NODE_ENV='development' electron . --inspect=5959 --remote-debugging-port=9333 --trace-warnings --trace-deprecation", "start:prod": "cross-env NODE_ENV='production' DX_ENV='production' electron . --inspect=5959 --remote-debugging-port=9333 --trace-warnings --trace-deprecation", "start:clear": "cross-env NODE_ENV='development' electron . --clear-cache --inspect=5858 --remote-debugging-port=9333 --trace-warnings --trace-deprecation", "start:debugger": "cross-env NODE_ENV='development' electron ./backend/dist/main/debugger.js --inspect=5858 --remote-debugging-port=9333", "start:brk": "cross-env NODE_ENV='development' electron . --inspect-brk=5858 --remote-debugging-port=9333", "dev": "turbo run dev --no-cache --continue --parallel", "dev:prod": "cross-env DX_ENV='production' NODE_ENV='production' turbo run dev --parallel --no-cache", "format": "prettier --write  \"**/*.{js,jsx,json,md,ts,tsx,json5,cjs,mjs}\" --config ./.prettierrc.cjs", "lint": "eslint --cache --quiet --ignore-path .eslintignore", "lint:fix": "eslint --cache --fix --quiet --ignore-path .eslintignore", "lint:style": "stylelint \"./**/*.{css,less,scss}\" --fix", "lint:i18n": "eslint --cache --ignore-path .eslintignore ./packages/@shared/locales/ --fix && npx prettier --write packages/@shared/locales/**/*", "lint:i18n-tmp": "eslint --cache --ignore-path .eslintignore ./temp/locales/**/*.json --fix", "build:debug": "cross-env LOCAL_DEBUG=true DX_ENV=test sh deploy-scripts/shell/build.sh", "pack-debug": "cross-env USER=\"liubeijing\" RECEIVER=\"liubeijing\" ASAR=false LOCAL_DEBUG=true SKIP_SIGN=true SKIP_NOTARIZE=true TARGET_PLATFORM=$TARGET_PLATFORM TARGET_ARCH=$TARGET_ARCH DX_ENV=development sh deploy-scripts/shell/pack.sh", "pack-debug:x64": "cross-env TARGET_PLATFORM=darwin TARGET_ARCH=x64 npm run pack-debug", "pack-debug:arm64": "cross-env TARGET_PLATFORM=darwin TARGET_ARCH=arm64 npm run pack-debug", "pack-debug:win32": "cross-env TARGET_PLATFORM=win32 TARGET_ARCH=ia32 npm run pack-debug", "pack-dev": "cross-env DX_ENV='test' NODE_ENV='development' SKIP_SIGN=true SKIP_NOTARIZE=true pnpm dx pack", "pack-dev:x64": "npm run pack-dev -- --target=x64 --arch=x64 --platform=darwin", "pack-dev:arm64": "npm run pack-dev -- --target=arm64 --arch=arm64 --platform=darwin", "pack-dev:win32": "npm run pack-dev -- --target=ia32 --arch=ia32 --platform=win32 --asar", "pack-prod": "cross-env DX_ENV='production' NODE_ENV='production' SKIP_SIGN=true SKIP_NOTARIZE=true pnpm dx pack --asar", "pack-prod:x64": "npm run pack-prod -- --target=x64 --arch=x64 --platform=darwin", "pack-prod:arm64": "npm run pack-prod -- --target=arm64 --arch=arm64 --platform=darwin", "pack-prod:win32": "npm run pack-prod -- --target=ia32 --arch=ia32 --platform=win32", "pack-st": "cross-env DX_ENV='staging' NODE_ENV='staging' SKIP_SIGN=true SKIP_NOTARIZE=true pnpm dx pack", "pack-st:x64": "npm run pack-st -- --target=x64 --arch=x64 --platform=darwin", "pack-st:arm64": "npm run pack-st -- --target=arm64 --arch=arm64 --platform=darwin", "pack-st:win32": "npm run pack-st -- --target=ia32 --arch=ia32 --platform=win32", "rebuild-native": "DEBUG=* node ./scripts/node/electron-rebuild.mjs", "rebuild-native:node-gyp": "DEBUG=* node ./scripts/node/node-gyp-rebuild.js --target=electron", "clean:node_modules": "find . -name 'node_modules' -type d -not -path './release/*' -prune -print -exec rm -rf '{}' +", "clean:deps": "pnpm clean:node_modules & pnpm clean:tsc-cache", "clean:dist": "find . \\( -name 'dist' -o -name '*.module.scss.d.ts' -o -name '.turbo' -o -name '.wireit' \\) -type d -not -path './release/*' -not -path '*/node_modules/*' -prune -print -exec rm -rf '{}' +", "clean:all": "pnpm clean:dist && pnpm clean:deps", "reinstall": "pnpm clean:all && pnpm i", "i18n:sync": "i18n sync -c i18n.config.json && pnpm lint:i18n && pnpm --filter=@shared/locales clean && rm -rf frontend/node_modules/.vite && pnpm --filter=@shared/locales build", "i18n": "rm -rf temp/locales && npx i18next -c i18next-parser.backend.config.js && npx i18next -c i18next-parser.frontend.config.js", "i18n:validate-keys": "zx scripts/zx/validate-i18n-keys.mjs", "i18n:excel": "pnpm dx i18n-excel -f temp/locales/zh/pc_backend.json && pnpm dx i18n-excel -f temp/locales/zh/pc_frontend.json", "i18n:dx-vpn": "pnpm --filter=@xm/dx-vpn i18n", "i18n:elefanto": "pnpm --filter=@xm/elefanto i18n", "i18n:vpn-excel": "pnpm --filter=@xm/dx-vpn i18n:excel", "i18n:elefanto-excel": "pnpm --filter=@xm/elefanto i18n:excel", "i18n:split": "sh ./deploy-scripts/shell/parse-i18n-local.sh", "e2e": "playwright test --ui", "install:arm": "npm_config_arch=arm64 pnpm i", "install:x64": "npm_config_arch=x64 pnpm i", "count-test": "node ./deploy-scripts/node/test-line-counter.js", "electron:fix": "pnpm store prune && pnpm clean:deps && pnpm i", "test-summary": "node ./deploy-scripts/node/test-summary.js", "tsc-check": "node ./scripts/node/tsc-check-runner", "tsc-check:trace": "TRACE=true pnpm tsc-check", "tsc-check:debug": "DEBUG=true pnpm tsc-check", "tsc-check:keepTmp": "KEEP_TMP=true pnpm tsc-check", "clean:tsc-cache": "find . -name '*.tsbuildinfo' -not -path './release/*' -prune -print -exec rm -rf '{}' +", "clean:module.css": "find . -name '*.module.scss.d.ts' -not -path './release/*' -prune -print -exec rm -rf '{}' +", "update:deps": "node ./scripts/node/update-deps.js", "fast-pack:x64": "zx ./scripts/zx/fast-pack.mjs --platform=x64", "fast-pack:arm64": "zx ./scripts/zx/fast-pack.mjs --platform=arm64", "install:windows": "set npm_config_arch=ia32 && pnpm i", "dx": "pnpm --filter=@xm/dx-cli dx:debug", "upload:win32": "pnpm dx upload --dest=pkg --arch=ia32 --platform=win32", "clean:app-data-dev": "rm -rf ~/Library/Application\\ Support/大象Next-development", "clean:app-data": "rm -rf ~/Library/Application\\ Support/大象Next", "bytecode": "zx ./deploy-scripts/zx/tasks/bytecode.mjs --files=backend/dist/main/*.js", "install:pkg": "zx ./scripts/install-local.mjs", "link-zx": "sh deploy-scripts/shell/link-zx.sh", "test-zx": "sh deploy-scripts/shell/test.sh", "install:local": "open release/*.dmg", "test-npm-env": "set npm_config_arch=ia32 && node test-npm-env.js", "test:cov": "pnpm -r test:cov"}, "engines": {"npm": ">=7.0.0", "node": ">=16.15.0", "pnpm": ">=8.15.4"}, "dependencies": {"@shared/common": "workspace:*", "stylelint-scss": "^6.4.1"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/eslint-parser": "^7.22.15", "@babel/eslint-plugin": "^7.22.10", "@babel/preset-env": "^7.16.11", "@block/eslint-config": "0.7.25", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@playwright/test": "^1.33.0", "@sailor/i18n-resource-sync": "^0.2.0", "@types/fs-extra": "^11.0.1", "@types/jest": "^29.5.0", "@types/node": "20.16.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/yargs": "^17.0.22", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitest/coverage-v8": "3.0.9", "@vitest/coverage-istanbul": "3.0.9", "@vitest/ui": "3.0.9", "@xm/bytecode-builder": "0.0.1", "@xm/dx-cli": "workspace:*", "@xm/eslint-plugin": "workspace:*", "@xm/git-cz-pro": "0.0.2", "babel-jest": "^29.5.0", "consola": "^3.0.1", "cross-env": "^7.0.3", "dmg-license": "^1.0.11", "electron": "30.5.1", "eslint": "^8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-plugin-babel": "5.3.1", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-no-unsanitized": "latest", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.2", "eslint-plugin-react-hooks": "4.6.2", "find-up": "^7.0.0", "glob": "^10.3.10", "husky": "^8.0.3", "i18next-parser": "^8.0.0", "jest": "^29.5.0", "lint-staged": "^15.0.2", "minimatch": "~9.0.0", "node-gyp": "10.0.1", "npm": "^10.2.0", "plop": "^4.0.0", "postcss": "^8.4.29", "prettier": "latest", "stylelint": "^15.11.0", "stylelint-config-css-modules": "^4.4.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-standard": "^34.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "tsc-files": "^1.1.4", "tsup": "8.0.2", "turbo": "1.13.2", "typescript": "5.8.3", "vitest": "3.0.9", "yargs": "^17.7.1"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "pnpm": {"overrides": {"es5-ext": "0.10.53", "rollup": "4.14.1", "cheerio": "1.0.0-rc.12", "@mtfe/xm-rust-adapter": "1.20.65"}}, "config": {"commitizen": {"path": "cz-conventional-changelog", "skipQuestions": ["scope"]}}, "json-comments": {"tips": "Please install the JsonComments plugin to enable commenting functionality for JSON files, see: https://github.com/zhangfisher/json_comments_extension", "package.json": {"scripts.pack-dev": "下面是dev环境下的打包命令，其中pack-dev是提取的公共命令,注意先pnpm link @xm/dx-cli", "scripts.pack-prod": "生产环境下的打包命令，pack-prod是公共部分", "scripts.install:windows": "windows下安装依赖", "scripts.tsc-check": "ts增量检测相关命令", "scripts.format": "lint格式化", "scripts.i18n:sync": "拉取远端语言包", "scripts.clean:deps": "用来清除所有依赖", "scripts.start": "本地启动主进程相关", "scripts.dev": "本地启动渲染进程,注意不能使用--parallel，否则会忽略构建依赖顺序", "scripts.i18n": "提取i18nkey", "scripts.clean:art": "清除所有產物", "scripts.fast-pack:x64": "快速打包，适合本地调试的时候", "scripts.pack-debug": "使用本地dx-cli打包", "scripts.dx": "用来本地跑dx命令", "scripts.upload:win32": "手动上传包到s3", "scripts.bytecode": "主进程加密", "scripts.install:pkg": "本地打包后，快速安装", "scripts.clean:app-data-dev": "快速清除用户数据", "scripts.update:deps": "批量更新依赖", "scripts.link-zx": "本地开发时，需要将全局的zx链接到本地", "scripts.install:local": "快速安装本地打出来的包"}}}