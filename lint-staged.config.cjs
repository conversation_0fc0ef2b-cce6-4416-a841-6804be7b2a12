// @ts-nocheck
const path = require('path');
const fs = require('fs');

const {glob} = require('glob');
const {minimatch} = require('minimatch');
const yaml = require('js-yaml');

const {check} = require('./scripts/node/tsc-check');

const workspaceYamlPath = path.join(__dirname, 'pnpm-workspace.yaml');

const getIgnoredFilesFromPnpmWorkspace = () => {
    try {
        const workspaceYamlContent = fs.readFileSync(workspaceYamlPath, 'utf-8');
        const data = yaml.load(workspaceYamlContent);
        if (Array.isArray(data.packages)) {
            const ignoredPkgs = data.packages
                .filter(i => i.startsWith('!'))
                .map(i => {
                    // 去掉！号 拼成glob
                    return `${i.replace('!', '')}/**`;
                });

            return ignoredPkgs || [];
        }
        console.error('data.packages is not an array');
        return [];
    } catch (error) {
        console.error(error);
        return [];
    }
};

// !禁止随意忽略文件，影响tsc检查
const ignoredTscCheckFiles = [
    'packages/@xm/uni-bridge/test-web/**',
    // 'packages/@xm/dx-process-manager/**',
    'packages/@xm/dx-cast/**',
    'packages/@xm/dx-devtools-panel/**'
];
// 不在workspace中，说明已经发布了，不需要ts检查
const ignoredFilesFromWorkspace = getIgnoredFilesFromPnpmWorkspace();

if (ignoredFilesFromWorkspace.length > 0) {
    ignoredTscCheckFiles.push(...ignoredFilesFromWorkspace);
}

const eslintignorePath = path.join(__dirname, '.eslintignore');

const groupFilesByPackageName = async files => {
    const result = {};
    const {findUpSync} = await import('find-up');
    files.forEach(filename => {
        const {dir} = path.parse(filename);
        const packageJsonPath = findUpSync('package.json', {cwd: dir});
        const {name} = require(packageJsonPath);
        if (!result[name]) {
            result[name] = {
                files: [],
                packageJsonPath
            };
        }
        // 统计每个包下修改的文件
        result[name].files.push(filename);
    });

    return result;
};
// 每个包名下面 修改的ts文件，查找对应的单测文件
const findTestFiles = async (files, packageJsonPath) => {
    const packageDir = path.dirname(packageJsonPath);
    const testFiles = [];
    for (const file of files) {
        const basename = path.basename(file, path.extname(file));
        // 开始查找basename
        const result = glob.sync(`**/${basename}.{test,spec}.{js,jsx,ts,tsx}`, {
            ignore: 'node_modules/**',
            cwd: packageDir,
            absolute: true
        });

        if (result.length > 0) {
            testFiles.push(...result);
        }
    }

    return testFiles;
};

const testRunnerPath = path.join(__dirname, 'scripts/node/test-runner.js');
const genTestCommands = async files => {
    // 找到新增或者修改的测试文件对应的包名，然后执行测试命令
    const groupedFilesByPackage = await groupFilesByPackageName(files);
    const commands = [];

    for (const packageName in groupedFilesByPackage) {
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const {files, packageJsonPath} = groupedFilesByPackage[packageName];
        // 查找对应的files中有没有对应的单测文件
        const testFiles = await findTestFiles(files, packageJsonPath);
        if (testFiles.length > 0) {
            commands.push(`node ${testRunnerPath} ${packageName} ${testFiles.join(' ')}`);
        }
    }
    return commands;
};

const genCheckTscCommands = async files => {
    try {
        const ignoredFiles = [];
        const filesToCheck =
            files.filter(file => {
                let shouldIgnore = false;
                ignoredTscCheckFiles.forEach(pattern => {
                    const realPattern = path.join(__dirname, pattern);
                    if (minimatch(file, realPattern)) {
                        shouldIgnore = true;
                        ignoredFiles.push(file);
                        return;
                    }
                });

                return !shouldIgnore;
            }) || [];
        console.log('Tsc ignoredFiles:', ignoredFiles);
        if (filesToCheck.length > 0) {
            const {commands = []} = await check({filenames: filesToCheck, quiet: true});
            console.log('Tsc commands:', commands);
            return commands;
        }

        return [];
    } catch (error) {
        console.error(error);
        throw error;
    }
};

module.exports = {
    'packages/@shared/locales/**/*': ['pnpm lint:i18n'],
    '**/*.{json,json5}': ['prettier --write'],
    '**/*.{js,mjs,cjs,jsx}': async files => [
        `prettier ${files.join(' ')} --write`,
        `eslint --ignore-path ${eslintignorePath} --fix --quiet`,
        ...(await genTestCommands(files))
    ],
    '**/*.{css,less,scss}': [`stylelint --ignore-path ${eslintignorePath} --fix --allow-empty-input`],
    '**/*.{ts,tsx}': async files => [
        ...(await genCheckTscCommands(files)),
        // 'pnpm i18n:validate-keys',
        `prettier ${files.join(' ')} --write`,
        `eslint --ignore-path ${eslintignorePath} ${files.join(' ')} --fix --quiet --cache`,
        // ...(await genTestCommands(files))
    ]
};
