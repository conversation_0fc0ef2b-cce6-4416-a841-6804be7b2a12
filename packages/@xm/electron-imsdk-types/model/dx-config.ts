type IBooleanString = 'true' | 'false';

/**
 * - close-only, close-all: 均为不提醒
 * - open-only: 仅开启消息提醒的群中的 at所有人 消息提醒
 * - open-all: 所有群中的 at所有人 消息都提醒
 */
export type AtAllNotifyConfig = 'open-only' | 'open-all' | 'close-all' | 'close-only';

/** 稍后处理列表排序规则 */
export type DealLaterSortType =
    /** 最新消息优先 */
    | 'msg'
    /** 最近添加优先 */
    | 'mark';

export type SingleLevelConfig =
    // 旧配置
    {
        allNotifyPc: IBooleanString;
        soundPc: IBooleanString;
    } & Record<
        | 'screenshotShortcut'
        | 'soundPc'
        | 'unreadData'
        | 'screenRecorder'
        | 'isFrequentlyReplyEnabled'
        | 'importantSession'
        | 'quick_to_top_session'
        | 'start_chat'
        | 'focus_search'
        | 'quick_to_input'
        | 'open_setting'
        | 'showDaxiang'
        | 'labSwitch'
        | 'NewMsgNotice'
        | 'NewMsgNoticeShowMsgDetail'
        | 'showAssistant'
        | 'login_info_rd',
        string
    > & {
            attachEmotionOrder: string[];
            keyNotice: string[];
            send_message: 'enterSend' | 'enterWrap';
            priorityMsg: 'OPEN' | 'CLOSE';
        } & {
            atAllNotify: AtAllNotifyConfig;
            handleLaterOrder: DealLaterSortType;
            globalAutoTranslate: 'OPEN' | 'CLOSE';
            globalInputTranslate: TranslateLang;
            globalInputTranslateOperation: TranslateActionConfig;
        } & Record<
            // 新配置
            | 'PersonalLeaveSync'
            | 'BaikeSwitch'
            | 'QRCodeSwitch'
            | 'markdownSupport'
            | 'pcNewScreenshot'
            | 'atAllNotify'
            | 'starPersonPriority'
            | 'leaderPersonPriority'
            | 'keywordNotify'
            | 'msgattach_emotion'
            | 'blacklist'
            | 'androidI18nLa'
            | 'iOSI18nLa'
            | 'padI18nLa'
            | 'PCI18nLa'
            | 'showTimeZone'
            | 'globalAutoTranslate'
            | 'globalInputTranslate'
            | 'globalInputTranslateOperation',
            string
        >;

export type SingleLevelDXConfigKeys = keyof SingleLevelConfig;
export enum DoubleLevelConfigKeys {
    // 旧配置
    DX_MSGHELPER_NEW = 'DXMSGHELPERNEW', // 消息助手 (二级配置)
    SESSION_TIME = 'sessionTime', // ? (二级配置)
    DEVICEID = 'deviceId', // (二级配置)
    LABSWITCH_MAP = 'labSwitchMap', // 实验室 (二级配置)
    NOTIFY = 'notify', // 会话消息提醒 (二级配置)
    NOTIFY_APP = 'notify_app', // 收进消息助手且不提醒 (二级配置)
    MSGHELPER_LASTMSGDATE = 'msgHelperLastMsgDate', // 消息助手的最新已读时间 (二级配置)
    DRIVE = 'drive', // // 大象猜你在开车(移动端？) (二级配置)
    COMMON_ORG = 'common-org', // 常用部门 (现状没有多端同步通知) (二级配置)
    CUSTOM_EMOJI = 'customEmoji', // 自定义表情  (二级配置)

    // 新配置
    INPUT_TRANSLATE = 'inputTranslate', // 翻译目标语言
    INPUT_TRANSLATE_OPERATION = 'inputTranslateOperation',
    ATALL_GROUP_NOTIFY = 'atAllGroupNotify', // “at所有人”群开关 (二级配置)
    EMOTION_PKG = 'emotion_pkg', // 固定表情包  (二级配置)
    SESSION_AUTO_TRANSLATE = 'sessionAutoTranslate'
}
/**
 * 二级配置表中的一个条目，包含主键和嵌套键
 */
export interface DoubleLevelConfigEntry {
    /** 二级配置的主键 */
    key: DoubleLevelConfigKeys;
    /** 二级配置的嵌套键数组，如果不需要特定的嵌套键，可以传空数组 */
    nestedKeys: string[];
}

export type DoubleLevelConfig = Record<DoubleLevelConfigKeys, Record<string, string | undefined>>;

/**
 * @see https://km.sankuai.com/collabpage/2131258577
 */
export interface DxNavigationTab {
    /**
     * 唯一标识
     */
    tabKey: string;
    /**
     * 中文兜底名称
     */
    name: string;
    /**
     * tab定位地址
     */
    link: string;
    /**
     * 未选中图片
     */
    unselectedImg: string;
    /**
     * 选中图片
     */
    selectedImg: string;
    /**
     * 标识 PC 端是否设置应用展示在「更多」中
     */
    select: 0 | 1;
    /**
     * 「更多」里的icon
     *
     * @deprecated 使用 `unselectedImg` 代替，同时需要渲染颜色为 `#111925`
     */
    moreImg: string;
    /**
     * PC 端更多中展开的 icon 激活态
     *
     * @deprecated 使用 `unselectedImg` 代替，同时需要渲染颜色为 `#166FF7`
     */
    moreActiveImg: string;
    /**
     * 通知开关状态：0-关闭，1-开启
     */
    notify: 0 | 1;
    /**
     * 通知是否展示：0-关闭，1-开启
     */
    noticeDisplay: 0 | 1;
    /**
     * 红点通知数量
     */
    noticeCount: number;
    /**
     * 通知版本号，建议业务方传时间，但是没有强校验一定是数字，
     *
     * 但话又说回来顺序出了问题业务方自己负责，我这里只要做好异常处理就行
     */
    noticeVersion: string;
    /**
     * 应用是否下线
     *
     * @since 1.10.62 2024-12-04
     */
    isOffline: boolean;
    /**
     * 应用是否可删除
     *
     * @since 1.10.62 2024-12-04
     */
    isDeletable: boolean;
    /**
     * 开放应用appId，只有开放应用有，否则没有该字段
     *
     * @since 1.10.62 2024-12-04
     */
    appId?: string;
    /**
     * 中文名称
     *
     * @since 1.10.62 2024-12-04
     */
    zhName: string;
    /**
     * 英文名称
     *
     * @since 1.10.62 2024-12-04
     */
    enName: string;
    /**
     * 繁体名称
     *
     * @since 1.10.62 2024-12-04
     */
    hkName: string;
    /**
     * 对于新下发的应用，存在红点和气泡提示
     *
     * @since 1.14.4 2025-03-05
     */
    tip?: {
        /**
         * 对于新下发应用的红点提醒内容
         */
        content: string;
        /**
         * 对于新下发的应用，当前是否需要使用气泡提示
         */
        showBubble: 0 | 1;
    };
    /**
     * 应用允许自定义图标和名称，导航栏应当支持透传对应数据，实时变更;
     */
    custom?: {
        /**
         * 应用自定义名称
         */
        name: string;
        /**
         * 应用在「更多」菜单中的图标，仅移动端使用，PC 端不使用
         */
        moreIcon: string;
        /**
         * 选中态图标
         */
        selectIcon: string;
        /**
         * 未选中态图标
         */
        unSelectIcon: string;
    };
    /**
     * 应用柔性下发位置
     *
     * v7.9.0 本期仅会下发 area 为 `bottom` 且 index 为 `0` 的「AI 助理」应用
     *
     * update v7.12.0: 添加优先级、index指定为导航栏位置。
     */
    SoftDisplayRule?: {
        /**
         * 应用下发的区域
         *
         * - `bottom` 表示下发到导航栏
         * - `more` 表示下发到「更多」菜单
         */
        area: string;
        /**
         * 应用的下发位置下标，目前无准确定义
         */
        index: number;
        /**
         * 发布策略
         * 0 表示弱发布，1 表示强发布
         */
        rule: number;
        /**
         * 多个横幅下发时 优先展示最早发布的（值最小的）
         */
        priority: number;
        /**
         * 不同语言的弹窗、文案
         */
        multiLangBubbleMap: Record<string, {title: string; picture: string; desc: string}>;
    };
    //  应用名称国际化展示
    nameLang?: {
        en: string;
        zh: string;
        'pt-BR': string;
        'zh-HK': string;
    };
    tabConfig?: string;
}

export const enum DxNavigationTabTipType {
    RedDot = 1,
    Bubble = 2
}

export enum TranslateActionConfig {
    REPLACE = 'replace',
    INSERT = 'insert'
}

export enum TranslateLang {
    EN = 'en',
    ZH = 'zh',
    ZH_HK = 'zh-HK',
    PT_BR = 'pt-BR'
}
