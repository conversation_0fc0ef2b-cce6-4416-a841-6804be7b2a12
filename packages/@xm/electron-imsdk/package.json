{"name": "@xm/electron-imsdk", "version": "1.0.0", "description": "", "main": "dist/main/index.cjs", "module": "dist/main/index.js", "type": "module", "exports": {"./types": {"require": "./dist/types/index.cjs", "import": "./dist/types/index.js"}, "./client": {"require": "./dist/client/index.cjs", "import": "./dist/client/index.js"}, "./client/tools": {"require": "./dist/client/tools.cjs", "import": "./dist/client/tools.js"}, "./server": {"require": "./dist/server/index.cjs", "import": "./dist/server/index.js"}}, "scripts": {"prepare": "npm run build", "clean": "rm -rf dist .wireit .turbo", "build": "wireit", "dev": "wireit"}, "wireit": {"dev": {"command": "tsup --config tsup.config.dev.ts", "dependencies": ["../../@shared/common:dev", "../../@xm/electron-imsdk-types:dev", "../../@shared/rpc:dev"], "files": ["./base/*.ts", "./channel/*.ts", "./util/*.ts", "./client/*.ts", "./client/channel/*.ts", "./client/imsdk/*.ts", "./client/imsdk/extra/*.ts", "./client/imsdk/extra/managers/*.ts", "./client/imsdk/util/*.ts", "./client/util/*.ts", "./main/*.ts", "./preload/*.ts", "./server/*.ts", "./server/channel/*.ts", "./server/imsdk/*.ts", "./server/imsdk/util/*.ts", "./server/util/*.ts", "*.ts"], "output": ["dist/**"]}, "build": {"command": "tsup", "dependencies": ["../../@shared/common:build", "../../@xm/electron-imsdk-types:build", "../../@shared/rpc:build"], "files": ["./base/*.ts", "./client/*.ts", "./main/*.ts", "./preload/*.ts", "./server/*.ts", "*.ts"], "output": ["dist/**"]}}, "sideEffects": false, "dependencies": {"@shared/common": "workspace:*", "@shared/rpc": "workspace:*", "@mtfe/xm-rust-adapter": "1.20.65", "@mtfe/xm-web-sdk": "5.2.39", "@xm/electron-imsdk-types": "workspace:*"}, "peerDependencies": {"electron": "30.5.1"}, "devDependencies": {"@shared/types": "workspace:*", "@xm/dx-build-utils": "0.0.6", "tsconfig": "workspace:*", "tsup": "^8.0.2", "wireit": "^0.14.4"}, "keywords": [], "author": "wangyongqing02", "license": "ISC"}