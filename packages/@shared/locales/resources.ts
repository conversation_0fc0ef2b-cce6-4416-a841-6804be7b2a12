// This file was auto generated by resource-sync tools.

import en_pc_frontend from './en/pc_frontend.json';
import ptBR_pc_frontend from './pt-BR/pc_frontend.json';
import zh_pc_frontend from './zh/pc_frontend.json';
import zhHK_pc_frontend from './zh-HK/pc_frontend.json';
import en_pc_backend from './en/pc_backend.json';
import ptBR_pc_backend from './pt-BR/pc_backend.json';
import zh_pc_backend from './zh/pc_backend.json';
import zhHK_pc_backend from './zh-HK/pc_backend.json';

const resources = {
    namespaces: [
        {
            localeInfos: [
                {locale: 'en', version: 740},
                {locale: 'pt-BR', version: 442},
                {locale: 'zh', version: 676},
                {locale: 'zh-HK', version: 680}
            ],
            i18nConfig: {
                apiKey: '52f1d763cc4440f4ba7f2e08281cd1e7',
                projectId: 14,
                namespaceId: 'xpom4kguvv',
                namespaceName: 'pc_frontend'
            }
        },
        {
            localeInfos: [
                {locale: 'en', version: 58},
                {locale: 'pt-BR', version: 13},
                {locale: 'zh', version: 57},
                {locale: 'zh-HK', version: 60}
            ],
            i18nConfig: {
                apiKey: '52f1d763cc4440f4ba7f2e08281cd1e7',
                projectId: 14,
                namespaceId: '1ah2wp0ign',
                namespaceName: 'pc_backend'
            }
        }
    ],
    en: {
        pc_frontend: en_pc_frontend,
        pc_backend: en_pc_backend
    },
    'pt-BR': {
        pc_frontend: ptBR_pc_frontend,
        pc_backend: ptBR_pc_backend
    },
    zh: {
        pc_frontend: zh_pc_frontend,
        pc_backend: zh_pc_backend
    },
    'zh-HK': {
        pc_frontend: zhHK_pc_frontend,
        pc_backend: zhHK_pc_backend
    }
};

export default resources;
