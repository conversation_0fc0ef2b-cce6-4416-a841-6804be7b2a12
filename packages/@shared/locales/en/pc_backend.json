{"Container_un_support_api": "This feature is not supported in the current version, please try on the other side", "DXMP_app_forward_link_default_description": "Click to View Details", "FrameVM_contextMenu_close_n_tab": "Close {{n}} tab(s)", "FrameVM_contextMenu_close_other_tab": "Close other tabs", "FrameVM_contextMenu_close_right_tab": "Close tabs to the right", "FrameVM_contextMenu_close_tab": "Close", "Preview_download_complete": "Download Done", "Preview_download_fail": "File download failed", "Preview_download_file": "Downloading", "Preview_download_file_start": "Download started", "Preview_download_file_wenshu": "Using Wenshu to download", "Preview_download_success": "Downloaded", "Preview_download_success_wenshu": "Downloaded to Wenshu", "Preview_forward_fail": "Failed", "Preview_forward_success": "Forwarded", "Preview_git_can_not_copy": "GIFs cannot be copied", "Preview_save_file": "Save", "Preview_switch_to_this_session": "Switch to view", "Workbench_add_my_app_fail": "Failed to add my application", "Workbench_add_my_app_success": "Add my application successfully", "Workbench_delete_my_app_fail": "Failed to delete my application", "Workbench_delete_my_app_success": "Delete my application successfully", "Workbench_main_nav_no_permission": "No permission to access", "app_is_unUseable": "<PERSON><PERSON><PERSON> unavailable. Fix now.", "backend_diagnose_title": "Diagnostic Tool", "check_mac_arch_button_continue_download_new_version": "Download", "check_mac_arch_button_continue_use": "Continued Use", "check_mac_arch_detail": "Your Mac useing the {{systemArch}} processor, but running the {{currentArch}} version of {{appName}}. \\n\\n It is recommended to install the native version of Apple Silicon.", "check_mac_arch_msg": "Currently running the Intel version app", "check_mac_launch_button_continue_move": "Move", "check_mac_launch_button_continue_msg": "Recommend moving {{appName}} to the \"Applications\" folder", "check_mac_launch_button_continue_use": "Continued Use", "check_mac_launch_detail": "This will make the app runs more stable.", "check_single_instance_detail": "Please check the Task Manager for any running Daxiang programs and end this task", "check_single_instance_msg": "Daxiang program running in the background", "comb_cancel": "Cancel", "comb_choose_files": "Select Image", "comb_choose_files_other": "Choose up to {{maxNum}} files", "comb_choose_media": "Select Media", "comb_choose_video": "Select Video", "comb_confirm": "Confirm", "comb_log_link_copied": "<PERSON>pied", "comb_pan_no_permission_operation": "Unauthorized operation", "comb_pan_rename_file_not_found": "File doesn‘t exist", "comb_quit": "Exit", "comb_relaunch": "<PERSON><PERSON>", "comb_uni_router_dxmp_not_support_open_here": "Please open the link in the chat", "comb_uni_router_not_match_any_route": "Unsupported operation, please try on the other side", "comb_uni_router_oa_personmark_disabled": "Feature has been retired", "comb_uni_router_openchat_warning": "No corresponding chat found", "dx_app_ask_for_record_your_screen": "\"{{product}}\" wants to record the screen of this computer.", "dx_screen_shot": "Screenshot", "emsGroup_addMember_groupRobotMembersFull": "Failed to add. The number of bots exceeds the group limit (max: 30). Please remove some and try again.", "emsGroup_joinGroup_groupRobotMembersOverLimit": "You can add up to 30 bots. Please remove some and try again.", "image_preview": "Image Preview", "locale": "en", "main_window_change_lng_tip": "Your language switch will take effect after restart Daxiang", "main_window_relaunch_on_error": "Some errors occurred, please restart Daxiang", "main_window_title": "Daxiang", "markdown_window_already_create": "Markdown editor is already open in this session. Continue editing there.", "markdown_window_limit": "Markdown editor window limit reached. Close unused windows and try again.", "menu_about": "About {{productName}}", "menu_about_cast": "Wireless screen mirroring", "menu_about_check_preference": "Preferences", "menu_about_check_quit": "Exit {{productName}}", "menu_about_check_update": "Check for Updates", "menu_about_diagnose": "Network Diagnosis", "menu_about_log_tool": "Log Tool", "menu_about_relaunch": "Restart {{productName}}", "menu_about_services": "Service", "menu_about_transcribe": "小美听写", "menu_close": "Close", "menu_copy": "Copy", "menu_cut": "Cut", "menu_developer_tool": "Developer Tools", "menu_hide": "Hide {{productName}}", "menu_hide_other": "Hide others", "menu_minimize": "Minimize", "menu_paste": "Paste", "menu_paste_and_match_style": "Paste as text", "menu_process_manager": "Process Manager", "menu_redo": "Redo", "menu_select_all": "Select All", "menu_show_all": "Show all", "menu_show_as_Shadow_window": "Show the shadow window", "menu_undo": "Revoke", "menu_window": "Window", "microapp_notification_content_lock": "You have received a new notification.", "microapp_notification_title_lock": "Daxiang", "msg_card_data_error": "Template message data fetch failure", "msg_helper1": "Collapsed Chats", "msg_unicard_confirm_error": "Forwarding not confirmed by user.", "msg_unicard_data_error": "Failed to retrieve message data.", "msg_unicard_network_error": "Network error", "mtdaxiang_group_info_error": "Failed to obtain group information", "mtdaxiang_image_url_error": "Image URL error", "mtdaxiang_main_window_error": "The main window doesn't exist", "openapi_getsessioninfo_invalid_session": "Current session does not exist.", "openapi_sendmsgcard_confirm_error": "User didn't confirm forwarding", "openapi_sendmsgcard_confirm_errorsuo": "User has not confirmed forwarding.", "openapi_sendmsgcard_data_error": "Template message data retrieval failed.", "openapi_sendtextmessage_error": "Message sending failed.", "openapi_sendtextmessage_invalid_session": "Current session does not exist.", "openapi_sendunicard_confirm_error": "User has not confirmed forwarding.", "openapi_sendunicard_data_error": "Card message data retrieval failed.", "openapi_sendunicard_network_error": "Network exception.", "router_openapp_mrn_warning": "Please open this in Daxiang app on your phone.", "router_openchat_warning": "No corresponding chat found", "screen_shot": "Screen Recording", "screen_shot_anyway": "Continue", "screen_shot_app_ask_for_record_your_screen": "\"Daxiang\" wants to record your computer screen.", "screen_shot_cancel": "Cancel", "screen_shot_enable_app_access_in_system_setting": "Allow access in \"Settings\" under \"Privacy and Security\".", "screen_shot_open_system_setting": "Go Settings", "screen_shot_reject": "Decline", "screen_shot_system_memory_not_enough": "Reminder: The current system resources are running low, which may affect the normal use of Daxiang Screenshot.", "shadow_window_crashed_title": "The auxiliary window encountered some errors", "uni_router_deprecated": "This route is obsolete", "uni_router_not_match_any_route": "Unsupported operation, please try on the other side", "uni_router_openchat_warning": "No corresponding chat found", "update_app_name": "Daxiang", "update_msg_verify_open_error": "Failed to start Daxiang, please check if it's installed or opt for manual download.", "update_msg_verify_timeout": "Installation of {{latestVersion}} timed out and may have encountered issues. Please choose to manually download or continue using the old version.", "update_msg_verifying": "Update is being installed, please wait...", "update_request_permission_body": "Detected legacy Daxiang Next installation files, administrator privileges are required to delete them in order to upgrade to the latest version", "update_request_permission_title": "Click to request Daxiang update permission", "update_service_no_permission": "Insufficient permissions, please retry on \"Settings-About Daxiang\" page.", "update_service_notification": "New version", "update_service_version_update": "Software Update", "update_title": "Software Update", "version": 59, "windowManager_custom_window_title": "Custom Window", "workbench_add_into_nav_error_exception": "Failed to add. This app cannot be added to navigation from Workplace.", "workbench_add_into_nav_error_maximum": "Failed to add. Maximum 30 apps allowed in navigation.", "workbench_nav_add_into_nav_toast": "\"{{appName}}\" added to navigation", "workbench_nav_remove_from_nav_toast": "\"{{appName}}\" removed from navigation", "workbench_nav_setting_failed": "Save failed, please try again later"}