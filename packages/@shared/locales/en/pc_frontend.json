{"AddFriend_add": "Add", "AddFriend_added": "Added", "Chat_record": "(Chat History)", "Clear_history": "Clear", "DXMP_dialog_box_input_placeholder": "Add text...", "DXMP_menu_copy_link": "Copy link", "DXMP_menu_open_link": "Open in browser", "DXMP_menu_open_robot": "BOT", "DXMP_menu_reload": "Reload", "DXMP_toolbar_forward_link_default_description": "Click to View Details", "FrameWindow_content_crashed": "(￣▽￣)\" Oh, the page crashed", "FrameWindow_content_failed": "╮(╯▽╰)╭,, the page failed to load", "FrameWindow_content_reload": "Reload", "Go_to_a_session_to_view_the_information": "Open Chat", "Go_to_session": "Open Chat", "I_want_to_appeal": "Appeal", "NavView_close": "Close", "NavView_default_load_error_message": "Loading failed. <PERSON><PERSON> to refresh.", "NavView_page_crashed": "Oops, crashed", "NavView_page_load_error": "Page loading error", "NavView_page_load_error_click_reload": "Reload", "NavView_page_loading": "Loading...", "NavView_title": "Navigation", "Nav_view_close": "Close", "Nav_view_title": "Navigation", "No_search_history": "No search history", "Operation_failed_please_try_again_later": "Failed, please try again later!", "PreviewThumbnail_send_image_at": "sent on", "Preview_click_retry": "Retry", "Preview_download_complete": "Download Done", "Preview_image_load_failed": "Image failed to load", "Preview_video_load_failed": "Video failed to load.", "Search_history": "Search history", "Side_panel_webview_crashed": "Oh no, the page crashed...", "Side_panel_webview_crashed_reopen": "Reopen", "Tool_bar_add_workbench": "Add to my Apps", "Tool_bar_contact": "Contact Customer Service", "Tool_bar_copy_img": "Copy", "Tool_bar_download_file": "Download", "Tool_bar_download_img": "Download", "Tool_bar_download_message_img": "Download", "Tool_bar_edit_file_online": "Edit", "Tool_bar_enter_chat": "Find in Chat", "Tool_bar_extract_text": "Extract Text", "Tool_bar_file_last_modify": "Last modified:", "Tool_bar_file_owner": "Sender:", "Tool_bar_forward_img": "Forward", "Tool_bar_forward_page": "Forward", "Tool_bar_go_back": "Go back", "Tool_bar_go_forward": "Go forward", "Tool_bar_image_ocr": "Extract Text", "Tool_bar_open_in_browser": "Open in browser", "Tool_bar_pop_up": "Open in independent window", "Tool_bar_reload": "Reload", "Tool_bar_remove_workbench": "Remove from my Apps", "Tool_bar_rotate": "Rotate", "Tool_bar_zoom_fit": "Fit to window", "Tool_bar_zoom_in": "Zoom in", "Tool_bar_zoom_origin": "Original size", "Tool_bar_zoom_out": "Zoom out", "You_have_not_browsed_the_document_yet": "You haven't viewed any docs yet.", "You_have_not_edited_the_document_yet": "You haven't edited any docs yet.", "_dx_i18n_locale": "locale field reserved", "about_daxiang_desktop": "Daxiang Desktop Client", "add_friend_search_result_default_ename": "<PERSON><PERSON><PERSON>", "advanced_search_group_member": "Members: {{groupNumber}} people", "ai_action_loading": "Loading...", "ai_action_loading_error": "Load failed, click to retry.", "app_fix_dberror": "One-click diagnosis", "app_is_unUseable_1": "<PERSON><PERSON><PERSON> unavailable. Fix now.", "app_is_unuseable_guide": "<PERSON><PERSON><PERSON> encountered a little problem, please click on \"One-Click Diagnosis\", it will automatically check and provide solutions.", "app_name_daxiang": "Daxiang", "app_name_daxiang_im": "Daxiang IM", "appf_NetworkDiagnosis": "Network Diagnosis", "appf_drag_space_tips": "Collapse by moving here", "appf_following_are_new_messages_click": "New messages below, click", "appf_label_O_D_T_N_O_N_M_W_F": "Show Unread Count Only when Collapsed", "appf_label_T_G_R_R_H_B_S_T_D_O": "Label notification set to show unread count only when label collapsed.", "appf_label_T_G_R_R_H_B_S_T_E_U": "Label notification set to show unread conversations when label collapsed.", "appf_label_added_to_group_strings": "Added to \"{strings}\".", "appf_label_expose_unread_C_D_F": "Show Unread Conversations when Collapsed", "appf_label_group_message_R_R": "Label Notification Settings", "appf_label_group_sorting": "Sort Label", "appf_label_newly_created_group_strings": "\"{strings}\" created.", "appf_label_removed_from_group": "Removed", "appf_label_sort": "Sort", "appf_labelthere_are_currently_no_groups_A_F_C": "No labels to collapse.", "appf_select_pic_from_gallery": "Choose from Album", "appf_tab_hrPortal": "MyTuan", "appf_tips_navigation": "Press and hold the icon to adjust the order", "apply_added": "Added", "apply_agree": "Agree", "apply_delete": "Delete", "apply_expired": "Expired", "apply_rejected": "Rejected", "appn_tab_hrPortal": "MyTuan", "audio_error": "Load failed", "avatar_menu_1000_help_desk": "1000 Help Center", "avatar_menu_6000_help_desk": "Help Center", "avatar_menu_employee_help": "Help Center", "avatar_menu_employee_help_desk": "Help Center", "avatar_menu_enterprise_management": "Enterprise Management", "avatar_menu_logout": "Log Out", "avatar_menu_modify_password": "Change Password", "avatar_menu_my_favorites": "Favorites", "avatar_menu_new_version_found": "New Version", "avatar_menu_operation_failed": "Failed", "avatar_menu_ready_to_update_tag": "Update", "avatar_menu_release_log": "Version History", "avatar_menu_setting_status": "Status", "avatar_menu_settings": "Settings", "avatar_menu_status_auto_update_hint": "Automatic update during the leave period after leave approval", "avatar_menu_status_more_status": "— More status features on the way, please stay tuned —", "avatar_menu_status_my_status": "Status", "avatar_menu_status_on_vacation": "On Leave", "bubble_locate_important_message": "Priority", "calendar_Friday": "<PERSON><PERSON>", "calendar_Monday": "Mon", "calendar_Saturday": "Sat", "calendar_Sunday": "Sun", "calendar_Thursday": "<PERSON>hu", "calendar_Tuesday": "<PERSON><PERSON>", "calendar_Wednesday": "Wed", "calendar_no_schedule": "No events", "calendar_underway": "Ongoing", "card_msg_mini_prefix": "[Card] ", "card_msg_network_anomaly": "Network disconnected", "card_msg_parser_chart": "[Chart]", "card_msg_parser_fallback": "[Card] Please upgrade to the new version to view this message", "card_msg_parser_img": "[Image]", "card_msg_prefix": "[Card]", "card_msg_unrecognized_message": "Message unrecognized.", "chat_record_link_from": "From:  {{form}}", "chat_record_link_no_intro": "No description", "check_update_auto_install_available_updates": "Automatically install updates", "check_update_auto_install_available_updates_popover": "Once turned on, Daxiang will automatically install updates on exit or restart", "check_update_button": "Check for updates", "check_update_continue_to_use": "Continue", "check_update_debug_mock": "Debugging Mock", "check_update_download_manual": "Download", "check_update_download_now": "Download", "check_update_downloading": "Downloading", "check_update_downloading_update": "Downloading", "check_update_error": "Error when checking for updates", "check_update_extracting": "Extracting...", "check_update_fix": "Fix", "check_update_forward_to_resolve_latest_client": "Latest Daxiang configuration", "check_update_install_and_restart": "Install and restart the app", "check_update_is_latest": "The current version is up to date", "check_update_new_version_downloaded": "The latest version has been downloaded.", "check_update_new_version_found": "New Version:", "check_update_update_details": "Update Details", "check_update_update_now": "Update", "check_update_verify_perm_fail": "The new {{latestVersion}} will install after reboot, but permissions are insufficient. Click \"Fix\" for the next update to take effect", "check_update_verify_timeout": "{{latestVersion}} update install timed out and may have issues. Download manually or use the old version", "check_update_verifying": "Installing updates, please wait...", "code_block_preview_title": "Preview Code Block", "comb_not_support_operation": "Unsupported operation", "comf_OK": "Confirm", "comf__message_cannot_be_identified": "Message unrecognized.", "comf__message_cannot_be_identified_unicard": "Message unrecognized. Please update <PERSON><PERSON><PERSON> to view.", "comf_add": "Add", "comf_add_at": "Added at", "comf_add_at_yesterday": "Added yesterday", "comf_add_on": "Added on", "comf_administrator": "Admin(s)", "comf_agree": "Agree", "comf_all": "All", "comf_and_one": "+1", "comf_application": "Application", "comf_can_edit": "Edit", "comf_can_use": "Use", "comf_cancel": "Cancel", "comf_chat_record_detail_num": "[{{num}} messages]", "comf_chat_record_me": "Me", "comf_chat_record_people_num": "({{size}} people)", "comf_clear": "Clear", "comf_click_to_try_again": "click to retry", "comf_collapse_filtering": "Collapse filtering", "comf_confirm": "Confirm", "comf_confirm_title_notice": "Tip", "comf_continue_send": "Continue", "comf_copy": "Copy", "comf_copy_error_authentication_failure": "Authentication failed", "comf_copy_fail": "<PERSON><PERSON> failed, please try again later.", "comf_copy_successfully": "<PERSON>pied", "comf_delete": "Delete", "comf_determine": "Confirm", "comf_disable": "Close", "comf_done": "Done", "comf_download": "Download", "comf_download_start": "Downloading...", "comf_download_successfully": "Download Successfully", "comf_draft": "Draft", "comf_earlier": "Earlier", "comf_edit": "Edit", "comf_enable": "Enable", "comf_end_date": "End", "comf_end_time": "To", "comf_expand": "Show More", "comf_expand_filtering": "Expand filtering", "comf_external": "External", "comf_external_group": "external group", "comf_external_user": "external contact", "comf_failed": "Failure", "comf_failed_to_load": "Loading failed", "comf_file_error_be_withdrawn": "Message has been recalled", "comf_file_not_found_in_disk_tips": "File not found, please re-download.", "comf_file_preview_fail": "Unable to preview this file", "comf_file_preview_not_support": "Preview not supported. Please download to view.", "comf_file_upload_cancel": "Canceled", "comf_file_upload_fail": "Failed to send", "comf_file_upload_wait": "Pending", "comf_forward": "Forward", "comf_forward_failed": "Failed", "comf_forward_successfully": "Forwarded", "comf_get": "Noted", "comf_go_setting": "Go to Settings", "comf_group": "Groups", "comf_group_chat_members": "Members", "comf_has_shown_all": "That's all for now", "comf_invalid_link": "Invalid link", "comf_invite": "invitation", "comf_link_text": "Text", "comf_link_url": "Link", "comf_load_fail": "Request failed,", "comf_load_more": "View More", "comf_load_retry": "Retry", "comf_loaded_fail": "Loading failed. ", "comf_loading": "Loading...", "comf_loading_now": "Loading...", "comf_markdown_code_block": "Code Block", "comf_modAvtr_choose_avatar": "Select Avatar", "comf_modAvtr_limit": "Please select an image that is not larger than 5MB", "comf_modAvtr_modify": "Edit avatar", "comf_modAvtr_no_avatar": "Please select an avatar.", "comf_modAvtr_no_selected": "Please select an area of the avatar", "comf_modAvtr_photo_standard": "Avatar upload standards", "comf_modAvtr_reselect": "Select again", "comf_modAvtr_save": "Save avatar", "comf_modAvtr_save_failed": "Failed to change avatar, please try again later!", "comf_modAvtr_success": "Avatar updated successfully!", "comf_modAvtr_upload_error": "Failed to upload avatar, please try again later!", "comf_month": "Month", "comf_more": "More", "comf_new": "New", "comf_next_step": "Next step", "comf_no_content": "No results found", "comf_no_more": "No more", "comf_no_more_content": "No more", "comf_no_network": "Network disconnected", "comf_no_notify": "Don't Remind Again", "comf_not_support_copying": "Image format can't be copied. Please download it to use.\n", "comf_not_support_operation": "Unsupported operation", "comf_number_of_messages": "[{{num}} messages]", "comf_people": "People", "comf_people_or_groups": "People/Groups", "comf_permission_denied": "No Access", "comf_placeholder": "Enter content here", "comf_please_enter_the_content": "Please enter the content of the query", "comf_please_note": "Reminder", "comf_preview": "Preview", "comf_preview_with_standalone_window": "Open in Window", "comf_public": "Public", "comf_public_account": "Official Accounts", "comf_quit": "Exit", "comf_quote_hide_message_count": "Hidden {{num}} message", "comf_quote_hide_message_counts": "Hidden {{num}} messages", "comf_read_already": "Read", "comf_record_context_enter_conversation": "Enter the session", "comf_record_customer_service_contact_person": "Customer Service Contact:", "comf_record_details_introduction": "Details:", "comf_record_search_group": "Group", "comf_red_packet_not_support": "Red packet is not available on desktop. Please check on your mobile.", "comf_reedit": "Re-edit", "comf_refuse": "Reject", "comf_relaunch": "<PERSON><PERSON>", "comf_relogin": "Log In", "comf_reply_to": "Reply to {{target}}", "comf_request_fail": "Request failed, please try again later.", "comf_reset": "Reset", "comf_retry": "Retry", "comf_risk_warning": "Risk Warning", "comf_save": "Save", "comf_say_something": "Leave a message", "comf_search": "Search", "comf_search_fail_tip": "Search failed, possibly due to network disconnection", "comf_search_file_from": "From:", "comf_search_no_result": "No results found", "comf_search_record_within_one_day": "Last Day", "comf_search_record_within_one_month": "Last Month", "comf_search_record_within_one_week": "Last Week", "comf_search_record_within_three_month": "Last three months", "comf_search_result_total": "Total ", "comf_search_results": " result(s)", "comf_see_suggest_see_all_search_results": "View all results", "comf_select_group": "Select Group", "comf_select_sender": "From", "comf_send_at": "Sent at", "comf_send_at_yesterday": "Sent yesterday", "comf_send_on": "<PERSON>t on", "comf_show_details": "Details", "comf_show_in_folder": "View", "comf_start_at": "Start", "comf_start_date": "Start", "comf_start_time": "From", "comf_success": "Success", "comf_successfully_copied": "<PERSON>pied", "comf_suggest_administrator": "Admin(s):", "comf_suggest_cannot_forward_folder": "Can‘t forward folder", "comf_suggest_chat_record": "Chat History", "comf_suggest_contain_end": ")", "comf_suggest_contain_start": "(Includes:", "comf_suggest_create_on": "Created on:", "comf_suggest_file": "File/Link", "comf_suggest_fold": "Fold", "comf_suggest_group_introduction": "Group Description:", "comf_suggest_introduction": "Description:", "comf_suggest_last_send": "Last sent", "comf_suggest_no_result": "No results found", "comf_suggest_no_search_result_yet": "No results found", "comf_suggest_record_enter_query": "Please enter the content of the query or filter conditions", "comf_suggest_search_history_title": "Recently input", "comf_suggest_search_no_keyword": "Search failed, please check if the search keyword is empty", "comf_suggest_see_more": "View More", "comf_suggest_superior": "Leader:", "comf_suggest_utils_groups": "Groups", "comf_suggest_utils_open_group": "Public Groups", "comf_suggest_utils_other": "Other", "comf_suggest_utils_personal": "Personal", "comf_suggest_utils_public": "Official Accounts", "comf_switch_to_levi": "Switch to AI Assistant", "comf_switch_to_this_session": "Switch to view", "comf_the_day_before_yesterday": "The day before yesterday", "comf_this_month": "This month", "comf_this_week": "This week", "comf_thumb_down": "Dislike", "comf_thumb_up": "Like", "comf_today": "Today", "comf_uni_router_network_error": "Network or service exception, please try again later", "comf_uni_router_not_match_any_route": "Unsupported operation, please try on the other side", "comf_unread": "Unread", "comf_until": "until", "comf_version": "Version", "comf_video_file_preview_fail": "Unable to preview this video", "comf_video_preview": "Video Preview", "comf_video_preview_fail": "Unable to preview video.", "comf_view": "Click to view", "comf_view_context": "Find in Chat", "comf_vote_creator": "Initiator", "comf_welcome": "Welcome", "comf_whole_day": "All day", "comf_within_a_period": "in the last {{month}} months", "comf_within_a_week": "Past 7 Days", "comf_within_a_year": "Last year", "comf_within_one_month": "Past 30 Days", "comf_within_three_months": "Last three months", "comf_year": "-", "comf_yesterday": "Yesterday", "comf_you": "You", "comf_yunpan_year_month": "{{year}}-{{month}}", "contact_add_common_department_successfully": "Success", "contact_add_contact_success": "Added", "contact_add_contact_to_limit": "Failed. You have reached the limit for adding friends on the same day", "contact_add_custom_group_failed_with_no_reason": "Failed to add group", "contact_add_custom_group_failed_with_reason": "Add failed: {{reason}}", "contact_add_custom_group_success": "Success", "contact_add_friend": "Add Contacts", "contact_add_friend_wait_for_apply": "Send request, wait for approval", "contact_add_group": "Add Groups", "contact_add_member": "Add Members", "contact_add_roster_member": "Add Members", "contact_alreay_in_group": "Joined", "contact_application_has_been_sent": "Contact request sent", "contact_apply_error": "Request failed.", "contact_apply_join_public_group_success": "Request sent successfully. Please wait for admin approval.", "contact_cancel": "Cancel", "contact_cant_add_yourself": "Cannot add oneself.", "contact_common_add_error": "Add failed", "contact_common_fail": "Failed", "contact_common_load_error": "Failed to load", "contact_common_loading": "Loading...", "contact_common_no_data": "No data", "contact_common_no_group": "No groups", "contact_common_no_member": "No members", "contact_common_organization": "Commonly Used Departments", "contact_common_sccess": "Success", "contact_confirm": "Confirm", "contact_confirm_notify_title": "Tips", "contact_confirm_to_delete_contact": "After removing this contact, they will no longer be able to communicate with you. Is that confirmed?", "contact_create": "Create", "contact_create_department_group": "Create Department Group", "contact_create_department_group_introduction_1": "Departmental group members are updated with the organization: new members of a department are automatically added to the group, and members who have left the department are automatically left from the group.", "contact_create_department_group_introduction_2": "The group name is updated with the organization and the group owner is updated with the organization leader.", "contact_create_department_group_introduction_3": "Only group owner and admins can add or delete group members, you cannot leave or disband the group.", "contact_create_department_group_name": "Group Name:", "contact_create_department_group_size": "Group Size:", "contact_create_group": "New Group", "contact_create_group_successfully": "Group created successfully", "contact_custom_group_add_members": "Add member.", "contact_custom_group_add_members_failed_with_no_reason": "Failed to add member", "contact_custom_group_add_members_failed_with_reason": "Add failed: {{reason}}", "contact_custom_group_add_members_success": "Success", "contact_delete_common_org": "Remove departments", "contact_delete_common_org_fail": "Failed to remove", "contact_delete_common_org_success": "Successfully removed", "contact_delete_friend": "Deleted", "contact_delete_my_friend": "Delete", "contact_delete_my_group": "Delete", "contact_delete_roster_group": "Delete", "contact_delete_roster_group_fail": "Failed", "contact_delete_roster_group_success": "Success", "contact_delete_roster_member": "Remove Members ", "contact_delete_roster_member_sucess": "Successfully removed group member", "contact_department_group_information_changes_with_organization": "Departmental group information (group name, group members), changing with organization", "contact_dept_add": "Add Department", "contact_empty_organization": "No departments", "contact_excess_limit": "The group exceeds the maximum limit of participants", "contact_failed_to_add_common_department": "Failed", "contact_failed_to_add_contact": "Adding contacts failed!", "contact_failed_to_get_group_contract_types": "Failed to get departmental group contract type configuration", "contact_full_group": "Full", "contact_group_contracts": "Member Contract Types: ", "contact_group_contracts_note": "Members of the above type, when joining the department, will automatically join the department group.", "contact_group_creation_failed": "Failed to create group", "contact_group_members_are_updated_with_organization": "Departmental group members are updated with the organization and cannot be leave or disband.", "contact_input_account_or_phone": "Enter phone number or Daxiang account", "contact_invalid_gid": "Invalid group number", "contact_invalid_org_id": "Corporate account doesn't match", "contact_join_group": "Join", "contact_join_public_group_success": "Success", "contact_loading_desperately": "Loading...", "contact_myContact_load_error": "Failed", "contact_myContact_star": "Starred Contacts", "contact_my_contact": "My Contacts", "contact_my_group": "My Groups ", "contact_new_contact": "New Contacts", "contact_not_exist_group": "Group does not exist", "contact_not_public_group": "Closed group", "contact_only_leader_can_create_department_group": "Departments group can only be created by nodal leaders and have the \"department\" label", "contact_organization": "Organization", "contact_organization_structure": "Organization", "contact_other_company": "External Enterprises", "contact_people": "people", "contact_person_no_data": "No contacts", "contact_public_group": "Public Groups", "contact_random_group_prefix": "Create a group", "contact_rename_roster_group": "<PERSON><PERSON>", "contact_rosterGroup": "Custom Groups", "contact_rosterGroup_load_error": "Failed.", "contact_rosterGroup_rename_existed_error": "This group name already exists", "contact_rosterGroup_rename_fail": "Failed to rename custom group", "contact_rosterGroup_rename_length_error": "Supports up to 12 characters", "contact_rosterGroup_rename_success": "Renamed custom group successfully", "contact_rosterMember_load_error": "Failed", "contact_search_failed": "Search failed", "contact_select_org": "Select Organization", "contact_selected_organization": "Selected Organization（", "contact_send": "Send", "contact_send_message": "Message", "contact_tab": "Contacts", "contact_update_sccess": "Success", "contact_user_not_exist": "The user does not exist", "contact_verification_message_longer_than_20": "The maximum length of the validation message is 20 characters", "day_n_and_total_n_day": "Day {{current}}/Total {{total}}", "deal_later_guide_go_setting": "You can go to {{target}} to change.", "deal_later_guide_sort_rule": "'Later' list is now sorted by 'Recently Added First'.", "deal_later_list_empty": "No items", "deal_later_message_expired": "The message has expired.", "deal_later_message_from": "From", "deal_later_message_from_chat": "chat with {{person}}", "deal_later_message_from_group_chat": "'{{group}}' group", "deal_later_message_no_permission": "No permission to view this message.", "deal_later_message_tips_group_dismiss": "The group has been disbanded.", "deal_later_message_tips_group_exit": "You are no longer in this group.", "deal_later_one_week_boundary": "7 days ago", "demo_hi": "Hello (<PERSON><PERSON><PERSON>)", "demo_perf_button": "Display time spent", "demo_test": "This text is from: {{i18n}}", "demo_welcome_with_option": "Welcome: {{name}}", "diagnose_IMSDKDBState_confirm_cancel": "Cancel", "diagnose_IMSDKDBState_confirm_msg": "imsdk DB (database) status has been restored, please restart the elephant to continue using.", "diagnose_IMSDKDBState_confirm_ok": "Repair", "diagnose_IMSDKDBState_confirm_title": "Should Daxiang be restarted after the repairs are completed?", "diagnose_IMSDKDBState_error": "Database corruption", "diagnose_IMSDKDBState_fix_fail": "Fixing failed", "diagnose_IMSDKDBState_not_available": "imsdk DB status retrieval failed", "diagnose_arch_error": "Mismatched architecture", "diagnose_base_info": "Basic Information", "diagnose_check_date": "Detection Time", "diagnose_check_fail": "Detection failed.", "diagnose_check_timeout": "Timeout detected", "diagnose_copy_fail": "<PERSON><PERSON> failed, please try again later.", "diagnose_copy_result": "Copy result", "diagnose_copy_success_msg": "<PERSON>pied", "diagnose_core_addon_daemon_status": "Daemon Process Status", "diagnose_daemon_process_memory_over_limit": "Memory usage exception", "diagnose_daemon_process_not_found": "Process not found.", "diagnose_daxaing_version": "Elephant Edition", "diagnose_daxiang_version": "Version", "diagnose_device_ip": "Device IP", "diagnose_device_mac": "Device MAC", "diagnose_dist_error_clean": "Cleanup", "diagnose_dist_error_detail": "Details", "diagnose_dist_error_detail_tips": "Disk cleaning method: <a href=\"{{link}}\" target=\"_blank\">View link</a>", "diagnose_dns_exception": "DNS Exception", "diagnose_dxApi1_ip_node": "Node Layer - Interface 1 IP", "diagnose_dxApi1_ip_rust": "Rust Layer - Interface 1 IP", "diagnose_dxApi1_loss_rate": "Interface 1 Latency/Packet Loss Rate", "diagnose_dxApi2_ip_node": "Node layer - Interface 2 IP", "diagnose_dxApi2_ip_rust": "Rust layer - interface 2 IP", "diagnose_dxApi2_loss_rate": "Interface 2 latency/packet loss rate.", "diagnose_dxImage_loss_rate": "Image service delay/packet loss rate", "diagnose_dx_loss_rate": "Daxiang Latency/Packet Loss Rate", "diagnose_dxfile_ip_node": "Node Layer - Image Service IP", "diagnose_dxfile_ip_rust": "Rust Layer - Image Service IP", "diagnose_fix": "Repair", "diagnose_fix_tip": "Please fix DNS before restarting", "diagnose_free_C_dist_not_enough": "Insufficient disk space on drive C.", "diagnose_free_dist_not_available": "Failed to retrieve disk information.", "diagnose_free_dist_not_enough": "Insufficient disk space.", "diagnose_free_dist_status": "Remaining disk space", "diagnose_gateway_loss_rate": "Gateway delay/packet loss rate", "diagnose_im_page_memory_over_limit": "{{memoryCost}}MB (Excessive).", "diagnose_im_page_memory_over_limit_tips": "High memory usage, try reloading.", "diagnose_im_page_memory_status": "Session Memory Usage", "diagnose_im_page_not_found": "Session process not found.", "diagnose_im_service_not_running": "Exception", "diagnose_im_service_status": "IMSDK Process Status", "diagnose_imsdkDB_status": "DB status in imsdk", "diagnose_imsdk_not_connected": " Not connected.", "diagnose_imsdk_persistent_connection_ip": "IMSDK Connection IP", "diagnose_internalNetwork_loss_rate": "Network Latency/Packet Loss within the network.", "diagnose_is_vpn_connected": "VPN Connected", "diagnose_km_loss_rate": "Citadel Latency/Packet Loss Rate", "diagnose_memory_info": "Memory Information", "diagnose_net_info": "Network Information", "diagnose_net_loss_rate": "Internet Latency/Packet Loss Rate", "diagnose_net_type": "Network Type", "diagnose_net_unconnected": "Network may not be connected.", "diagnose_net_wired": "Wired", "diagnose_net_wireless": "Wireless", "diagnose_no": "No", "diagnose_no_wireless": "SSID retrieval failed", "diagnose_normal_service": "Service is normal.", "diagnose_os_info": "System Information", "diagnose_pack_log": "Packaged Logs", "diagnose_pack_log_err": "Failed to pack logs", "diagnose_popup_not_available": "Exception", "diagnose_popup_rebuild_popup_tips": "Restart popup service.", "diagnose_popup_status": "Popup Status", "diagnose_publicNetwork_loss_rate": "Public network latency/packet loss ratio.", "diagnose_recheck": "Recheck", "diagnose_repair_arch_error_label": "Download the latest version.", "diagnose_repair_arch_error_tips": "The Elephant you have currently installed is not compatible with your system architecture, which may affect its performance. Please visit the official website to download the Elephant software suitable for {{sysArch}} architecture.", "diagnose_repair_daemon_process_memory_over_limit_tips": "Rebuild daemon process.", "diagnose_repair_daemon_process_not_found_label": "Start Process", "diagnose_repair_daemon_process_not_found_tips": "Not started or accidentally destroyed.", "diagnose_repair_daemon_process_too_many_tips": "Destroy excessive  processes.", "diagnose_repair_dns_exception_tips": "Fix DNS", "diagnose_self_test": "Function Self-Check", "diagnose_ssid": "Wireless SSID", "diagnose_time_zone_and_country": "Current Time Zone", "diagnose_too_many_daemon_process": "Multiple processes exist.", "diagnose_user_help": "Help Center", "diagnose_user_help_desk": "Help Center", "diagnose_vpn_loss_rate": "VPN Latency/Packet Loss Rate", "diagnose_vpn_version": "VPN Version", "diagnose_wss_loss_rate": "WSS Latency/Packet Loss Rate", "diagnose_xuecheng_ip_node": "<PERSON><PERSON> Layer - Citadel IP", "diagnose_xuecheng_ip_rust": "R<PERSON> Layer - Xuecheng IP", "diagnose_yes": "Yes", "dxAI_chat_panel_editor_placeholder": "You can ask me questions by sending me private messages.", "dxAI_group_panel_editor_placeholder": "You can ask me questions via group messages.", "dxAI_pub_session_editor_placeholder": "Any questions for me?", "dxAI_title": "Daxiang AI", "dx_ai_message_reaction_replace": "Insert into Editor", "dx_ai_popover_default_title": "Daxiang AI Assist me...", "dx_ai_prompt_detail_textarea_max_length": "Max {{maxlength}} characters.", "dx_ai_prompt_menu_detail_send": "Send to AI", "dx_ai_prompt_menu_placeholder": "Type “/” for AI Assist...", "dx_app_ask_for_record_your_screen": "\"{{product}}\" wants to record the screen of this computer.", "dxmp_copy_url_failure": "<PERSON><PERSON> failed, please try again later.", "dxmp_copy_url_success": "<PERSON>pied", "earlier_time": "Earlier", "editor_context_menu_copy": "Copy", "editor_context_menu_cut": "Cut", "editor_context_menu_delete": "Delete", "editor_context_menu_paste": "Paste", "editor_context_menu_paste_as_text": "Paste as Text", "editor_context_menu_redo": "Redo", "editor_context_menu_select_all": "Select all", "editor_context_menu_undo": "Recall", "editor_enter_fullscreen": "Full screen(⌘S)", "editor_img_text": "[Image]", "editor_link_text": "[Link]", "editor_placeholder": "Enter a message", "editor_quit_fullscreen": "Exit full screen", "editor_quit_fullscreen_mac": "Exit Full Screen (⌘S)", "editor_quit_fullscreen_win": "Exit Full Screen (Ctrl+S)", "editor_tool_upload_file": "Upload File", "editor_tool_upload_image": "Upload Image", "editor_translation_guide_go_setting": "Translations can be inserted directly into the input box. You can go to {{target}} to set the default paste option for all conversations.", "employee_help_desk": "@Help Center", "empty_result_search_use_ai_no_keyword": "Try searching with AI", "err_network_error": "Network unavailable, please try again later.", "example_avatar": "SampleAvatar", "fabric_editor_click_to_scale_100_percent": "Click to view original image", "fabric_editor_copy_fail": "Co<PERSON> failed", "fabric_editor_copy_success": "<PERSON>pied", "fabric_editor_copy_to_clipboard": "Copy to clipboard", "fabric_editor_download_fail": "Download failed", "fabric_editor_download_success": "Downloaded", "fabric_editor_download_to_local": "Download to local", "fabric_editor_font_size_large": "Large", "fabric_editor_font_size_middle": "Medium", "fabric_editor_font_size_small": "Small", "fabric_editor_hot_key_mac_zoom_in": "⌘Scroll or ⌘+", "fabric_editor_hot_key_mac_zoom_out": "⌘Scroll or ⌘-", "fabric_editor_hot_key_win_zoom_in": "Ctrl Scroll or Ctrl+", "fabric_editor_hot_key_win_zoom_out": "Ctrl Scroll or Ctrl-", "fabric_editor_load_error": "Image loading failed", "fabric_editor_menu_arrow": "Arrow", "fabric_editor_menu_circle": "Circle", "fabric_editor_menu_crop": "Crop", "fabric_editor_menu_draw": "Draw", "fabric_editor_menu_label": "Label", "fabric_editor_menu_mosaic": "Mosaic", "fabric_editor_menu_rect": "Rect", "fabric_editor_menu_redo": "Redo", "fabric_editor_menu_reset": "Reset", "fabric_editor_menu_rotate": "Rotate", "fabric_editor_menu_text": "Text", "fabric_editor_menu_undo": "Undo", "fabric_editor_menu_upload": "Upload", "fabric_editor_mosaic_rect": "Selection", "fabric_editor_reset_cancel_text": "Cancel", "fabric_editor_reset_ok_text": "Confirm", "fabric_editor_reset_popconfirm_title": "Are you sure to reset to the original image when it was opened?", "fabric_editor_save": "Save", "fabric_editor_zoom_in": "Zoom in", "fabric_editor_zoom_out": "Zoom out", "fav_panel_item_download": "Download", "fav_panel_item_forward": "Forward", "fav_panel_item_unfavorite": "Unfavorite", "fav_panel_show_message_in_console": "_DEV_ prints the message body on the console", "favorite_card_notingroup": "You're not in the original conversation, so the content isn't visible.", "favorite_chathistory_unforwardablecard_toast": "The chat history contains cards that can't be forwarded. Please return to the original conversation to select and resend the message.", "favorite_panel_all_tab": "Chats", "favorite_panel_file_tab": "Files", "favorite_panel_group": "Groups", "favorite_panel_image_tab": "Images", "favorite_panel_link_tab": "Links", "favorite_panel_message_record_tab": "Chat History", "favorite_panel_my_favorite_title": "Favorites", "favorite_panel_no_data": "No content", "favorite_panel_operation_failed": "Failed", "favorite_panel_search_input_placeholder": "Search", "favorite_panel_sys_msg_cancel": "Message has been recalled", "favorite_panel_text_tab": "Text", "file_all_operations_except_sharing": "Can perform all file operations except for the sharing function", "file_forward_download_and_dump": "Can preview, forward, download, and save", "file_security_below_safety_level": "Lower than recommended level poses a security risk. Choose carefully.", "file_security_data_regulations_doc": "Data Security Management Regulations", "file_security_desc_C1": "C-1 \n1. Data openly available through company channels such as Meituan-Dianping press releases, public announcements, merchant advertisements, and group-buying information.", "file_security_desc_C2": "C-2\n1. Policies and internal resource information available to all staff.\n2. Knowledge sharing and summaries, tutorial materials.\n3. Business cases or public proposals.\n4. Product manuals, FAQs.\n5. Project descriptions and work plans promoted to all staff.\n6. Team-related information, like work guidelines, recruitment info, duty rosters, etc.", "file_security_desc_C3": "C-3\n1. Information shared within the department.\n2. Meeting minutes, weekly reports, project plans.\n3. Business requirements, integration documentation, technical project solutions, PRDs.\n4. Current situation analysis, risk reports, internal system issues, or major security incidents.\n5. Business activity plans, such as sales strategies.", "file_security_desc_C4": "C-4\n1. Data or reports with significant impact on company profits or development.\n2. Core algorithms or code.\n3. Financial data.\n4. User identity information or sensitive personal data.\n5. Real identity markers of merchants & partners, financial information.", "file_security_level": "Security classification", "file_security_level_C1": "Public", "file_security_level_C2": "Internal", "file_security_level_C3": "Confidential", "file_security_level_C4": "Top Secret", "file_security_set_confirm": "Confirmation", "file_security_set_confirm_message": "Changing the classification will affect file permissions. Confirm change?", "file_security_set_level_fail": "Failed", "file_security_set_level_success": "Success", "file_security_yun_pan_doc_security_statement": "According to the company's <a href='#'>{{docName}}</a>, all documents in the cloud drive must be classified. Please select the document's classification level, which cannot be lower than the recommended level:", "file_to_link_cancel": "Not Now", "file_to_link_confirm": "Confirm", "file_to_link_content": "The new document is independent of the original. You can manage it in the cloud drive: Cloud Drive > My Files > Links.", "file_to_link_fail": "Failed", "file_to_link_send_message": "Send here", "file_to_link_title": "Confirm to create a WPS online document?", "forbid_download_forward": "Download and forward are not supported", "forward__send_content_cannot_be_empty": "The sent message can't be empty", "forward_auth_file": "Authentication failed, please try again.", "forward_cancel": "Cancel", "forward_confirm": "Forward", "forward_confirm_modal_file_group_main_title": "{{tips}}, the recipients will be granted view access upon sending. Continue anyway?", "forward_confirm_modal_file_group_tips": "This group may include external members", "forward_confirm_modal_file_main_title_template": "{{tips}}, the recipients will be granted view access upon sending. Continue anyway?", "forward_confirm_modal_file_multi_tips": "The selected chats include external members", "forward_confirm_modal_file_person_tips": "This person is an external employee", "forward_confirm_modal_other_main_title_template": "Send to {{target}}?", "forward_confirm_modal_other_multi_main_title": "The selected chats include external contacts or external groups. Continue anyway?", "forward_confirm_modal_other_secondary_title": "Please pay attention to protecting corporate information security.", "forward_create_new_chat": "Create group", "forward_forwarded": "Forwarded", "forward_img_upload_failed": "Some pictures were not uploaded successfully, please upload again", "forward_img_uploading": "Uploading images. Wait a minute.", "forward_message": "Forward to", "forward_msg_too_long_tip": "The message is too long, please send it in multiple parts", "forward_mute_group_contain": "Partial Forwarding Failed: Some groups allow only the group owner and admins to post.", "forward_mute_group_single": "Forwarding Failed: Only the group owner and admins are allowed to post in this group.", "forward_new_chat": "Create Group and Forward", "forward_no_session_in_label": "No available conversations in the label.", "forward_not_support_input": "Input temporarily not supported", "forward_overdue": "Access has expired", "forward_recent_session": "Recent Chats", "forward_search_placeholder_with_options": "Search \"{{optionString}}\"", "forward_selected_group": "Selected", "forward_selected_limit": "A maximum of {{maxSelectedLimit}} conversations can be selected at a time.", "forward_selected_user": "Selected", "forward_selected_user_group": "Selected", "forward_top_session": "<PERSON><PERSON><PERSON>", "frequent_reply_empty": "No common phrases", "frequent_reply_empty_tips": "Use common phrases to boost efficiency. ", "gif_exceeds_2m": "GIF exceeds 2M", "grayScale_config_tab_approval": "Approval", "grayScale_config_tab_citadel": "Citadel", "grayScale_config_tab_cloudDisk": "Cloud Drive", "grayScale_config_tab_dynamic": "Moments", "grayScale_config_tab_hrPortal": "MyTuan", "grayScale_config_tab_meeting": "Video Meeting", "grayScale_config_tab_memo": "Memo", "grayScale_config_tab_schedule": "Calendar", "grayScale_config_tab_vpn": "VPN", "grouo_info_operation_failed": "Failed", "group_info": "Group Settings", "group_info_add": "Add", "group_info_add_group_application_send": "The application for joining the group has been sent, please wait for the admin's review", "group_info_add_member": "Add Members", "group_info_add_related_contract_type": "Create contract types associated with department groups.", "group_info_add_robot_fail": "Failed to add robot", "group_info_add_success": "Success", "group_info_add_to_group": "Add", "group_info_add_to_msg_helper": "Move to Collapsed Chat", "group_info_add_to_msg_helper_success": "Success", "group_info_advise": "Consultant", "group_info_all_staff": "All staff", "group_info_all_time": "All", "group_info_allow_group_members_at_all": "Everyone Can @All", "group_info_allow_group_members_to_add": "Everyone Can Add Members", "group_info_allow_sharing_group": "Share Group", "group_info_allow_sharing_group_cards": "Share Group", "group_info_are_you_sure_to_add": "Add Members", "group_info_are_you_sure_you_want_to_disband_the_group_with_option": "Are you sure to disband '{{groupName}}'", "group_info_are_you_sure_you_want_to_leave_the_group_with_option": "Are you sure to leave from '{{groupName}}'", "group_info_auto_join_group_on_member_add": "Members will automatically join this group when they join the department", "group_info_auto_translate": "Auto Translate", "group_info_auto_translate_language": "your set language.", "group_info_auto_translate_tips": "Auto-translate incoming messages to   ", "group_info_background": "Custom background color", "group_info_can_view_all_time_message": "all messages", "group_info_can_view_latest_day_message": "chat history from the last day", "group_info_can_view_latest_week_message": "chat history from the last week", "group_info_cancel": "Cancel", "group_info_cancel_admin_failed": "Failed", "group_info_cancel_the_administrator": "Remove <PERSON>", "group_info_cannot_close_at_notification": "Notifications for direct mentions cannot be disabled.", "group_info_comp_suggest_no_more_data": "No more", "group_info_confirm_cancel_administrator_identity": "Confirm removing the admin role for this member", "group_info_confirm_dialog_add_member_with_option": "New members can now view {{visibleTime}}. Are you sure you want to add the selected members?", "group_info_confirm_to_remove_member_with_option": "Are you sure you want to remove {{member}} from this conversation?", "group_info_confirm_to_remove_robot": "Confirm to remove this bot", "group_info_confirm_transfer_group_master_with_option": "Are you sure you want to transfer ownership to {{memberName}}?", "group_info_create_session": "Message", "group_info_data_parse_error": "Failed to parse data", "group_info_delete_fail": "Delete failed", "group_info_delete_success": "Deleted", "group_info_department": "Dept.", "group_info_department_group_management_restrictions_introduction": "Only the group owner and admins can add or remove group members, other people can't leave or disband the group", "group_info_department_group_member_update_introduction": "Departmental group members are updated with the organization: new members of a department are automatically added to the group, and members who have left the department are automatically left from the group.", "group_info_department_group_name_update_introduction": "The group name is updated along with the organization, and the group owner is updated along with the organization leader.", "group_info_department_has_member": "There are departments in the organization.", "group_info_describe": "Bio", "group_info_determine": "Confirm", "group_info_developer": "Developer", "group_info_disbanding_will_remove_all_group_member": "After disbanding the group, all group members will be removed.", "group_info_dissolve": "Disband", "group_info_edit_department_group": "Department Group Settings", "group_info_edit_second_department_group_introduction_delete_1": "Delete contract types associated with department groups", "group_info_edit_second_department_group_introduction_delete_2": "Members will automatically leave the group when they leave the department", "group_info_edit_second_department_group_introduction_then": "Follow-up", "group_info_failed_to_add_a_group_member": "Failed to add group members", "group_info_failed_to_get_group_contract_types": "Failed to get departmental group contract type configuration", "group_info_failed_to_modify_avatar": "Failed to modify avatar, please try again later", "group_info_failed_to_remove_group_member": "Failed to remove group members.", "group_info_failed_to_save": "Failed", "group_info_failed_to_transfer_the_master": "Failed to assign group owner", "group_info_fetch_robot_detail_fail": "Failed to get robot details", "group_info_global_setting": "Global Settings", "group_info_global_setting_notify_after": ".", "group_info_global_setting_notify_before": "You've disabled @All notifications in ", "group_info_group_app_manage": "Group Application Management", "group_info_group_contract": "Member Contract Types", "group_info_group_contracts_note": "Members of the above type, when joining the department, will automatically join the department group.", "group_info_group_forbidden": "Mute Mode", "group_info_group_is_full_and_cannot_join": "Add failed: Group capacity exceeded. Please adjust and retry.", "group_info_group_manage": "Group Settings", "group_info_group_members": "Members", "group_info_group_open_forbidden": "Once enabled, only group owner and admins can post.", "group_info_group_robot": "BOTs", "group_info_group_sticky": "Pin to Top", "group_info_has_added": "Added", "group_info_history_visible_range": "Chat History for New Members", "group_info_history_visible_range_montions": "The change applies only to new members of the group.", "group_info_in_department_members": "Department members", "group_info_internship": "Intern", "group_info_jump_to_robot": "Message Bot", "group_info_labour": "Labor Contract", "group_info_latest_day": "Last Day", "group_info_latest_week": "Last Week", "group_info_loading": "Loading...", "group_info_loading_desperately": "Loading...", "group_info_manage": "Manage", "group_info_max_length_twelve": "Up to 6 words or 12 characters", "group_info_member_count_unit": "position", "group_info_member_not_in_group_add_confirm": "Member is not in this conversation. Would you like to add?", "group_info_member_still_in_group_remove_confirm": "Member is still in this conversation. Remove from conversation", "group_info_members_all": "Search", "group_info_message_no_notification": "Mute Notifications", "group_info_message_notification": "Notifications", "group_info_miaoda_entry": "Create Q&A Assistant", "group_info_modify_avatar_successfully": "Success", "group_info_modify_group_avatar": "Group Avatar", "group_info_more_operation": "More", "group_info_no_group_robots_yet": "No BOTs", "group_info_no_in_department_member": "No \"departmental members\" in this group", "group_info_no_more_data": "No more", "group_info_no_non_department_member": "No \"non-departmental members\" in this group", "group_info_no_permission_for_organization_structure": "No permission to view this organization", "group_info_no_search_for_member_with_option": "No results about \"{{searchKeyword}}\"", "group_info_non_department_members": "Non-departmental members", "group_info_not_add_to_group": "Cancel", "group_info_not_need_to_update_group_contract_types": "Configuration has not changed", "group_info_not_remove_from_group": "Cancel", "group_info_not_visible": "No Access", "group_info_notify_while_at_all": "Notify me about @All mentions", "group_info_notify_while_at_me": "Notify me about @Me mentions", "group_info_official": "Full-time", "group_info_open_addition_review": "Membership Approval", "group_info_operation_failed": "Failed", "group_info_operation_failed_already_in_group": "All members already in group and do not need to be added again.", "group_info_operation_failed_exceed_limit": "Limit: 500 additions at once. Please retry.", "group_info_operation_failed_group_has_exited": "Failed! You have left from the group", "group_info_operation_failed_no_valid_members": "Failed to add some. Check validity.", "group_info_operation_failed_with_admin_amount_limit_exceeded": "Failed! You can add up to 3 admins at most", "group_info_operation_failed_with_group_has_dismissed": "Failed! The group has been disbanded", "group_info_operation_failed_with_group_has_exited": "Failed! You have left from the group", "group_info_operation_failed_with_invalid_group": "Failed! Group number is invalid", "group_info_operation_failed_with_no_permission": "Failed! No permissions", "group_info_other": "Other", "group_info_outsource": "Outsourced", "group_info_placeholder": "Maximum of 9 Chinese characters or 18 characters", "group_info_plaese_go": "If you want to apply this to all groups, please go to ", "group_info_please_go": "If you want to apply this setting to all groups, please go to ", "group_info_please_input_robot_name": "Enter the name of the robot", "group_info_please_select": "Select", "group_info_quit": "Confirm", "group_info_quit_button_dissolve_group": "Disband Group", "group_info_quit_button_exiting_or_dissolving_groups_tip": "Group members are bound to the organization and it is not supported to leave or disband the group.", "group_info_quit_button_exiting_or_dissolving_groups_tip_with_option": "{{groupType}} group members are bound to the organization and do not support leaving or disbanding the group", "group_info_quit_button_quit_group": "Leave Group", "group_info_remove": "Remove", "group_info_remove_from_group": "Remove from conversation", "group_info_remove_from_msg_helper_success": "Removed", "group_info_remove_this_member": "Remove", "group_info_request_failed_and_click_to_try_again": "Request failed. Please click to retry.", "group_info_robot": "BOTs", "group_info_robot_description": "Bio", "group_info_robot_edi": "Intelligent Q&A", "group_info_robot_jumpUrl": "Edit Basic Info", "group_info_role_name": "Role name", "group_info_role_name_desc": "The role will be displayed after the member's name for easy understanding by other members", "group_info_sample_avatar": "Sample Avatar", "group_info_sample_avator": "Sample Avatar", "group_info_save": "Save", "group_info_save_avatar": "Save", "group_info_save_to_group_list": "Save to My Groups", "group_info_select_member_process_way": "Please select member processing method", "group_info_session_all": "All", "group_info_session_setting": "Settings", "group_info_set_admin_failed": "Setting admin failed", "group_info_set_as_administrator": "Set as <PERSON><PERSON>", "group_info_set_department_group_manage": "Department Group Settings", "group_info_set_role": "Assign Role", "group_info_show_more": "View More", "group_info_still_show_message_while_close_message_notification": "When notifications are off, still notify me about the following messages.", "group_info_succeed_to_update_group_contract_types": "Success", "group_info_successful_operation": "Success", "group_info_text": "Custom text", "group_info_this_group_is_an_incognito_group_chat_are_you_sure_you_want_to_add_the_selected_person": "This is an incognito conversation. Are you sure you want to add the selected member?", "group_info_this_user_does_not_exist": "The user does not exist", "group_info_transfer_to_owner": "Transfer Ownership", "group_info_up_to_max_characters_with_options": "Up to {{ChineseCharactersCount}} Chinese characters or {{CharactersCount}} characters", "group_info_view": "Developer", "group_info_view_vcard": "View Profile", "group_info_you_will_no_longer_receive_messages_from_this_group_when_you_log_out": "Once you leave the group, you will no longer be able to receive messages from this group.", "group_notice": "Group Notice", "group_notice_all_read_announcement": "All read", "group_notice_cancel": "Cancel", "group_notice_confirm": "Confirm", "group_notice_confirm_to_delete_group_announcement": "Are you sure to delete this group notice", "group_notice_confirm_to_send_group_announcement": "Are you sure to send this group notice", "group_notice_edit": "Edit", "group_notice_edit_group_notice": "Edit Group Notice", "group_notice_failed_to_load": "Loading failed", "group_notice_failed_to_save": "Failed", "group_notice_fold": "Fold", "group_notice_group_announcement_cannot_exceed_with_option_other": "Group notice can't exceed {{count}} characters", "group_notice_no_announcement": "No group notice", "group_notice_not_read_announcement_with_option": "{{unreadCount}} Unread", "group_notice_operation_failed_with_no_permission": "Failed! No permissions", "group_notice_placeholder_tip": "Please type here", "group_notice_saved_successfully": "Successfully saved", "group_notice_unfold": "Show More", "hotkeys_focus_search": "Focus Search", "hotkeys_open_setting": "Open Settings", "hotkeys_quick_to_input": "Quick Jump to Input Box", "hotkeys_quick_to_top_session": "Locate to the list top", "hotkeys_screenshot": "Screenshot", "hotkeys_send_message": "Send Message", "hotkeys_show_ai_search": "AI Asst", "hotkeys_show_daxiang": "Show/Hide Da<PERSON>ng", "hotkeys_start_chat": "Create Group", "im_AI_summary": "AI Summary", "im_add_custom_emoji": "Add", "im_add_emoji_list_fail": "Failed to add", "im_add_member": "Add Members", "im_add_member_in_chat": "New Group", "im_add_session_to_helper": "Move to Collapsed Chat Without Notification", "im_add_to_custom_smileys": "Add to Stickers", "im_admin_withdraw": "<PERSON><PERSON>", "im_admin_withdraw_message_tips": "After the recall, group members will not be able to see this message.", "im_ai_search_entrance_tooltip": "AI - based Search", "im_ai_staff_vcard": "AI Staff Card", "im_ai_summary_all_text": "AI Summary", "im_all": "All", "im_all_file_permissions": "Full permissions for the file", "im_announcement": "Group Notice", "im_application_account": "Application Account", "im_atMe": "Mentions", "im_at_message_count": "@messages", "im_auto_translate": "Auto-translation.", "im_auto_translate_close": "Turn off", "im_auto_translate_closed_and_detected": "Detected messages in other languages. Try ", "im_auto_translate_opened": "Auto-translation enabled globally. ", "im_batch_collect": "batch favoriting", "im_batch_collect_error_tips": "Only unexpired text, images, links, files, chat history and sent videos can be favorited.", "im_batch_collect_new_error_tips": "Only unexpired text, images, links, files, chat history, sent videos, and completed cards can be batch favorited.", "im_bot": "BOT", "im_bot_vcard": "Bot <PERSON>", "im_busy_now": "Line busy", "im_call_back": "You can redial or callback on your phone.", "im_call_duration": "Duration", "im_can_not_copy_uploading_img": "Images can be copied once successfully uploaded.", "im_can_not_preview_uploading_img": "Images can be previewed once successfully uploaded.", "im_cancel": "Cancel", "im_cancel_mark_comfirm": "Are you sure to unmark this message?", "im_cancel_mark_comfirm_content": "After unmarking, chat members will not be able to see the message in the Mark list", "im_cancel_mark_failed": "Unmark failed", "im_cancel_message": "Cancel", "im_cancel_priority_session": "Remove from Priority Conversations", "im_cancel_stick_sessiono": "<PERSON><PERSON> from Top", "im_canceled_call": "Call canceled by caller, you can callback on mobile", "im_cancelled": "Cancelled", "im_cannot_add_member": "No permission to add group members", "im_chat_calendar": "View Calendar", "im_chat_calender": "View Calendar", "im_chat_file_size_cannot_exceed_1g": "The chat file size cannot exceed 1GB.", "im_chat_image_size_cannot_exceed_20m": "The image size cannot exceed 20M.", "im_chat_info": "Settings", "im_chat_mark": "<PERSON>", "im_chat_record": "Chat History", "im_chat_to_record": "{{fromName}} and {{toName}}'s Chat History", "im_chat_video_duration_cannot_exceed_1g": "Videos longer than 60 minutes are not supported.", "im_chat_video_size_cannot_exceed_1g": "Videos larger than 1GB are not supported.", "im_chat_video_size_or_duration_exceed": "Videos longer than 60 minutes or larger than 1GB are not supported.", "im_check_timezone_update_message": "Restart the app now to update <PERSON><PERSON><PERSON>'s time display immediately. If not restarted, message timestamps will show incorrect times and time zone indicators may be inaccurate.", "im_check_timezone_update_title": "Device Time Zone Changed", "im_click_see_doc": "Click to view", "im_click_to_see_all_content": "Show More", "im_close_message_notification": "Mute Notifications", "im_collect": "Favorite", "im_collect_fail": "Failed", "im_collect_successfully": "Added to Favorites", "im_conbinedforward": "Only unexpired text, images, links, files, chat history, sent videos, and forwardable card messages can be combined forwarded.", "im_confirm": "Confirm", "im_connection_succeeded": "Success", "im_continue_actions": "Continue", "im_continue_collect": "Favorite More", "im_continue_forward": "Forward", "im_convert_file_to_link": "Create WPS Online Doc", "im_copy": "Copy", "im_copy_err": "<PERSON><PERSON> failed, please try again later.", "im_copy_error": "<PERSON><PERSON> failed, please try again later.", "im_copy_error_authentication_failure": "Authentication failed", "im_copy_fail": "<PERSON><PERSON> failed, please try again later.", "im_copy_success": "<PERSON>pied", "im_copy_successfully": "<PERSON>pied", "im_custom_emoji_delete_fail": "Delete failed", "im_custom_emoji_delete_success": "Deleted", "im_custom_smiley_add_success": "Added", "im_custom_smiley_exist": "Sticker already exists", "im_customer_service_account": "Customer Service Account", "im_dealWithLater": "Later", "im_deal_later": "Later", "im_deal_later_done": "Done", "im_deal_later_exceed_limit": "Failed to add to 'Later'. A maximum of 900 items is supported. Please remove some and try again.", "im_default_name_contact": "Contacts", "im_default_name_group": "Groups", "im_default_name_system_account": "System Account", "im_delete_custom_emoji": "Delete", "im_delete_session": "Delete Chat", "im_disconnected_network": "Network disconnected. Check network.", "im_download": "Download", "im_download_choose_save_path": "Choose where to save", "im_download_successfully": "Success", "im_drop_send_to": "Release to send to ", "im_dxAI_guidence": "Daxiang AI has a new reply", "im_dx_AI": "Daxiang AI", "im_dx_notification_title": "Daxiang", "im_edit_err": "Failed", "im_edit_file_level": "Change Security Classification", "im_edit_img_over_8k": "Image resolution exceeds 8K. Previewing or editing may impact system performance.", "im_editor_at_all": "All", "im_editor_at_all_disabled": "@All mentions is not available in this group. Please contact the admin to enable it.", "im_editor_bold_mac": "Bold (⌘B)", "im_editor_bold_mac_md": "Bold (⌘B)\nMarkdown: **Text**Space", "im_editor_bold_win": "Bold (Ctrl+B)", "im_editor_bold_win_md": "Bold (Ctrl+B)\nMarkdown: **Text**Space", "im_editor_bullet_list_mac": "Bulleted list (⇧⌘U or -Space)", "im_editor_bullet_list_mac_md": "Bulleted list (⇧⌘U)\nMarkdown: -Space", "im_editor_bullet_list_win": "Bulleted list (Ctrl+Alt+U or -Space)", "im_editor_bullet_list_win_md": "Bulleted list (Ctrl+Alt+U)\nMarkdown: -Space", "im_editor_in_translation": "Translating", "im_editor_insert_image_pixel_too_much": "Image resolution exceeds 8K. Please send as a file.", "im_editor_italic_mac": "Italic (⌘I)", "im_editor_italic_mac_md": "Italic (⌘I)\nMarkdown: *Text*Space", "im_editor_italic_win": "Italic (Ctrl+I)", "im_editor_italic_win_md": "Italic (Ctrl+I)\nMarkdown: *Text*Space", "im_editor_link_mac": "<PERSON> (⌘K)", "im_editor_link_win": "Link(Ctrl+K)", "im_editor_ordered_list_mac": "Ordered list (⇧⌘O or 1.Space)", "im_editor_ordered_list_mac_md": "Ordered list (⇧⌘O)\nMarkdown: 1.Space", "im_editor_ordered_list_win": "Ordered list (Ctrl+Alt+O or 1.Space)", "im_editor_ordered_list_win_md": "Ordered list (Ctrl+Alt+O)\nMarkdown: 1.Space", "im_editor_tool_fold": "Hide formatting", "im_editor_tool_style": "Show formatting", "im_editor_translate_into": "Translate into", "im_editor_translate_turn_off": "Turn Off Translate", "im_editor_translate_turn_on": "Translate as You Type", "im_editor_translate_usage": "Translation Paste Option", "im_editor_underline_mac": "Underline (⌘U)", "im_editor_underline_mac_md": "Underline (⌘U)\nMarkdown: ~Text~Space", "im_editor_underline_win": "Underline (Ctrl+U)", "im_editor_underline_win_md": "Underline (Ctrl+U)\nMarkdown: ~Text~Space", "im_editor_yunpan_img_err": "Failed", "im_emotion_people_have_made_a_statement": "{count} people replied with a {name}", "im_emotion_people_replied_with_statement": "{{emotionCount}} people replied with '{{emotionName}}'", "im_emotion_people_replied_with_statement_oneperson": "1 person replied with '{{emotionName}}'", "im_enterprise_account": "Corporate Account", "im_err_merge_message_error": "Merge message error", "im_err_parse_image_size": "Error parsing image dimensions", "im_err_read_local_draft_error": "Failed to read local draft", "im_err_update_top_session_error": "Failed to update pinned chat", "im_expired_file_send_fail": "The file has expired and cannot be sent. Please re-upload.", "im_expression": "<PERSON>er", "im_failure_of_authentication": "Authentication failed", "im_file_name_contain_control_characters": "The file name contains invisible special characters. Please rename the file and upload it again.", "im_file_name_contain_emoji": "The file name cannot contain Emoji.", "im_file_name_too_long": "The file name cannot exceed 128 bytes.", "im_files_selected_more_than_9": "A maximum of 9 files can be selected at once.", "im_force_update_title": "Update Notice", "im_forward_mutiple_messages_error_tips": "Only unexpired text, images, links, files, chat history, sent videos, and forwardable card messages can be forwarded.", "im_forward_panel_tips": "Shift+click to select multiple items in batch", "im_get_detail": "Click to View Details", "im_get_emotion_data_error": "Request failed, please try again later.", "im_get_image_fail": "Image retrieval failed", "im_group": "groups", "im_group_account": "Broadcast Account", "im_group_calendar": "Group Calendar", "im_group_cancel_without_from_name": "A group member recalled a message.", "im_group_chat": "Groups", "im_group_creation_authority_failed": "Failed! Cannot create for others.", "im_group_info": "Group Settings", "im_group_is_full_creation_failed": "Failed! Group member limit exceeded.", "im_group_record": "Group Chat History", "im_group_vcard": "Group Card", "im_hidden_file": "Hidden files are not supported for upload.", "im_hr_anniversary": "Happy Anniversary", "im_i_know": "Got it", "im_impMsgTag_atAll": "@All", "im_impMsgTag_atMe": "@Me", "im_impMsgTag_keyword": "Keywords", "im_important_msg": "Priority Message", "im_important_msg_setting_info": "When you receive the following new messages, you'll get priority notifications (this setting applies globally).", "im_important_msg_setting_title": "Priority Messages Settings", "im_important_msg_settings": "Go to Settings", "im_important_session_del_success": "Removed. New messages will revert to the default system alert style.", "im_important_session_know_tips": "Got it", "im_important_session_set_success": "Success. New messages will be notified as important ⚡️ alerts.", "im_important_sessions_count": "Priority Conversation {{importantCount}}", "im_important_sessions_text": "Priority Conversation", "im_in_translation": "Translating...", "im_incompatible_video_send_file": "Special format videos will be sent as files.", "im_inf_click_to_try_again": "click to retry", "im_inf_loading": "Loading...", "im_inf_no_more_data": "No more", "im_initiated_voip_meeting": "has started a voice meeting, you can view it on your mobile phone.", "im_insert_translation_to_editor": "Replace current content with translation", "im_insert_translation_to_editor_tip": "Insert translation after current content", "im_invalid_event_msg": "New version reactions received. Please update to view.", "im_is_marked_by_name": "Marked by {{name}}", "im_keyword_message": "messages with keyword", "im_later": "Later", "im_leave_in": "On Leave", "im_link_cancel": "Unlink", "im_link_card_click_to_send": "Click to Send", "im_link_card_loading": "Converting link to card", "im_link_card_not_found": "Link information not found.", "im_link_edit": "Edit", "im_link_preview": "Visit", "im_load_failed_try_again": "Loading failed. Please try again later.", "im_local_file": "Local Files", "im_local_picture": "Local Images", "im_local_picture_and_video": "Local Images & Videos", "im_login_conflict": "Login Conflict", "im_login_conflict_login_on_another_device": "Login conflict, the account is already logged in other clients.", "im_login_fail": "<PERSON><PERSON> failed", "im_login_prepare_fail": "Server error, please try again.", "im_login_preparing": "Connecting to the server", "im_main_search_recommend_ai_search": "AI Search", "im_main_search_recommend_file_transfer_assistant": "File Transfer", "im_main_search_recommend_my_news": "Mentions", "im_mark_all_read": "<PERSON> <PERSON>", "im_mark_read": "<PERSON> <PERSON>", "im_mark_tips_message": "After marking, you can easily find it in \"Conversation Sidebar — Mark\". Marking messages is visible to all members in the conversation.", "im_mark_tips_ok": "Got it", "im_mark_tips_open_mark": "Open Mark List", "im_mark_tips_title": "Mark the important items", "im_mark_unread": "<PERSON> as Unread", "im_markdown_editor": "Markdown Editor", "im_markdown_editor_cover": "Overwrite", "im_markdown_editor_cover_content": "This will replace all previous content.", "im_markdown_editor_cover_title": "Overwrite existing content?", "im_markdown_tooltip": "Markdown syntax detected. Click to edit for formatted preview before sending.", "im_merge_forward": "Combine and Forward", "im_message": "Messages", "im_message_cant_forward": "The selected items do not support {{info}}. Please select again.", "im_message_deleted": "Recalling", "im_message_forward": "Forward", "im_missed_call": "Missed call, you can callback on mobile", "im_more": "More", "im_more_actions": "More actions", "im_msg_helper": "Collapsed Chats", "im_msg_not_support": "Message unrecognized, please update version.", "im_msg_not_support_content": "Message unrecognized.", "im_msg_preview_and_one": "+1", "im_msg_preview_app": "Application", "im_msg_preview_calendar": "Calendar Message", "im_msg_preview_done": "Done", "im_msg_preview_dynamic": "Application Message", "im_msg_preview_emotion": "<PERSON>er", "im_msg_preview_file": "File", "im_msg_preview_get": "Noted", "im_msg_preview_gps": "Location", "im_msg_preview_group_vcard": "Group Card", "im_msg_preview_image": "Image", "im_msg_preview_link": "Link", "im_msg_preview_merge_forward": "Chat History", "im_msg_preview_not_support": "Unsupported message", "im_msg_preview_not_support_goto_app": "This message is not supported, please view it on mobile", "im_msg_preview_notice": "Notice", "im_msg_preview_pcall": "Voice Call", "im_msg_preview_quote": "Quote", "im_msg_preview_red_packet": "Red Packet", "im_msg_preview_thumb_up": "Like", "im_msg_preview_vcard": "Contact Card", "im_msg_preview_video": "Video", "im_msg_preview_voice": "Audio", "im_msg_preview_voip_meeting": "Voice Meeting", "im_msg_preview_welcome": "Welcome", "im_multiselect": "Multi-select", "im_my_cluster": "Labels", "im_my_record": "{{fromName}}'s Chat History", "im_my_yunpan": "Cloud Files", "im_network_error": "Network error, please try again later.", "im_new_message": "New messages below", "im_no_any_atme_session": "No unread mentions", "im_no_any_session": "Your chats will be listed here", "im_no_implements_yet": "Not supported", "im_no_more_prompts": "Don't Show Again", "im_not_confirm_unmark": "Cancel", "im_not_support_copying": "The image format can't be copied. Please download it to use.", "im_not_supported_folder": "Can't send folders.", "im_notification_lock_screen_mode": "You've got a new message.", "im_one_at_message_count": "@message", "im_one_by_one_forward": "One-by-One Forward", "im_one_keyword_message": "message with keyword", "im_one_unread_message": "unread", "im_only_preview": "Preview", "im_open_markdown_editor": "Edit Markdown", "im_open_message_notification": "Unmute Notifications", "im_operation_fail": "Operation failed.", "im_operation_fail_and_retry": "Operation failed, please try again later.", "im_operation_failed_group_dissolve": "Operation failed! The group has been disbanded", "im_operation_failed_not_friend": "You haven't added each other as contacts. Please send a contact request first. Once approved, you can send messages to each other. [Send Request|{{link}}]", "im_operation_failed_quit_group": "Operation failed! You have left this group.", "im_operation_success": "Success", "im_or_try_to": ", or try ", "im_other_one": "The other side", "im_pack_translation": "Collapse Translation", "im_pack_up": "Collapse", "im_paste_max_length_error": "You can paste up to {{length}} characters at a time.", "im_personal": "individual", "im_preview_err": "Preview failed", "im_private_chat": "DMs", "im_pub_chat": "Official Accounts", "im_pub_menu_default_name": "Service Menu", "im_public_account": "Official Account", "im_public_group": "Public Groups", "im_public_vcard": "Official Account Card", "im_reaction_failed_tips": "The network is currently unstable. Please try again later.", "im_reaction_panel_click_retry": "retry.", "im_reaction_panel_default_emotions": "Default emojis", "im_reaction_panel_show_error": "Loading failed, please try again.", "im_reaction_panel_show_error_2": "Loading failed, ", "im_readSessionBatch_error": "Failed", "im_read_local_data_error": "Local Cache Read Error", "im_record": "Chat History", "im_red_packet": "Red Packet", "im_rejected": "Rejected", "im_remove_session_from_helper": "Remove from Collapsed Chat", "im_replace_fake_image_error": "Image retrieval failed", "im_replace_translation_to_editor": "Use", "im_replace_translation_to_editor_tip": "Replace current content with translation", "im_reply": "Reply", "im_reply_quote_draft": "[Reply Message]", "im_request_failed": "Request failed.", "im_resend_message": "Resend", "im_response_timeout": "Response timeout", "im_revoke_authorization_update_message": "Permission issues occurred during your DaXiang update. Re-download the latest DaXiang version and install it to overwrite the existing one.", "im_revoke_authorization_update_ok": "Download the latest version.", "im_revoke_authorization_update_title": "You have cancelled the update authorization.", "im_robot_account": "BOT", "im_say_forbidden_tips": "Muted: Only group owner and admins can post", "im_secret_already_lock": "Locked", "im_secret_click_to_unlock": "Click on the Daxiang window to unlock", "im_secret_countdown_change_fail": "Failed", "im_secret_destroy_time": "Destroy Time", "im_secret_destroy_time_setting": "Read-After-<PERSON><PERSON><PERSON> Timer", "im_secret_destroy_time_take_effect_newly_send_message": "Settings apply to new messages sent after changes; previously sent messages follow the earlier time limit.", "im_secret_read_countdown_config_open_fail": "Failed", "im_secret_set_destroy_time": "Can set the time for the message to be destroyed after being marked as read;", "im_see_all_medals": "View all medals", "im_select_files_exceed": "Maximum 9 images per insertion", "im_select_message_max_count": "You can select up to 100 messages at most", "im_send_image_2_file_confirm": "Images with resolution over 8K will be sent as a file. Continue?", "im_send_image_pixel_too_much": "Image Resolution Too High", "im_send_images_too_much": "Max 9 images per send", "im_send_message_img_cache_invalid": "Expired draft detected. Please remove invalid images before sending.", "im_send_message_too_long": "Message length exceeds limit", "im_send_vcard": "Contact Card", "im_session": "the current conversation", "im_session_name": "Conversation Name", "im_session_no_more_data": "No more", "im_set_priority_session": "Set as Priority Conversation", "im_shift_select_message_max_count": "For multi-select, a maximum of 100 messages can be selected at one time.", "im_show_unread_AI_summary": "View AI summary results", "im_start_chat": "New Group", "im_start_meeting": "New Meeting", "im_start_vote": "Poll", "im_stick_session": "Pin to Top", "im_stick_session_success": "Success", "im_subscription_account": "Subscription Account", "im_summary_click_tips": "Click", "im_summary_comma_click_tips": ", click", "im_synchronizing": "Connecting...", "im_sys_msg_addAttachmentEmotionNotice": "{{name}} replied [{{emotion}}] to {{messageText}} sent by {{sender}}. [Mute|{{link}}]", "im_sys_msg_addAttachmentEmotionNotice_preview": "{{oprator}}: Replied {{emotion}} // {{showText}}", "im_sys_msg_addAttachmentEmotionNotice_preview_single": "Replied {{emotion}} // {{showText}}", "im_sys_msg_addAttachmentEmotionNotice_send_message_desc": "[{{messageType}}] sent by {{sender}}", "im_sys_msg_addAttachmentEmotionNotice_voice": "{{name}} replied {{emotion}} to {{senderText}}. [Mute|{{link}}]", "im_sys_msg_all": "All", "im_sys_msg_applyFriendshipApproved_i": "I am {{fullName}}.", "im_sys_msg_applyFriendshipApproved_tips": "You and {{name}} are now contacts. Start chatting away!", "im_sys_msg_applyFriendshipApproved_tips_content": "Greeting shown above", "im_sys_msg_beginSecretChatWithSingleChat": "{{oprator}} started an incognito conversation.", "im_sys_msg_beginSecretChatWithSingleChatRule": "Messages will be automatically destroyed after being read and can be set with a time limit. Whether read or not, they will be destroyed after 7 days. \nAll messages cannot be forwarded or copied. \nThe screenshot and screen recording function will be partially restricted. \n[Instructions for using incognito chat|{{instructionLink}}]', {instructionLink}", "im_sys_msg_beginSecretChatWithSingleChatRule_new": "Messages will be automatically destroyed after being read and can be set with a time limit. Whether read or not, they will be destroyed after 7 days. \nAll messages cannot be forwarded or copied. \nThe screenshot and screen recording function will be partially restricted. \n[Instructions for using incognito chat|{{instructionLink}}]", "im_sys_msg_botInviteToJoinGroupNoticeForMember": "{{operator}} invited {{names}} to this conversation. New members can view all chat history. This setting can be modified under Group Settings.", "im_sys_msg_botUpdateGroupAnnounceForMember": "<PERSON><PERSON> updated the group notice.", "im_sys_msg_botUpdateGroupAnnounceForMember1": "{{operator}} updated the group notice. ", "im_sys_msg_buildGroupFromChatNotice": "You invited {{names}} to join the conversation with {{origin<PERSON><PERSON>}}. {{shareHistory}}", "im_sys_msg_buildGroupFromChatNoticeForOriginalPerson": "{{operator}} invited {{inviteeInfoList}} to join the conversation with {{originalPersonInfo}}.", "im_sys_msg_buildGroupFromChatNotice_noShareHistory": "You invited {{names}} to join the conversation with {{origin<PERSON>erson}}.", "im_sys_msg_buildGroupNoticeForManageAndCooperation_checkMemberFreeText": "[view member schedules|{{link}}]", "im_sys_msg_buildGroupNoticeForManageAndCooperation_prefix": "For easy management and teamwork, ", "im_sys_msg_buildGroupNoticeForManageAndCooperation_setGroupMessageText": "[set a group description|{{link}}]", "im_sys_msg_buildGroupNoticeToAdminForHistoryMsg": "New members can view all chat history. You can change it under Group Settings.", "im_sys_msg_buildGroupNoticeToMemberForHistoryMsg": "New members can view all chat history.", "im_sys_msg_buildSecretGroupNotice": "Messages will be automatically destroyed after being read and can be set with a time limit.\nWhether read or not, they will be destroyed after 7 days.\nMessages can't be forwarded or copied. \nScreenshot and screen recording features will be partially restricted. \n[Incognito Chat Instructions|{{instructionLink}}]", "im_sys_msg_buildSecretGroupNoticeForHistoryMsg": "New members cannot view chat history.", "im_sys_msg_cancelGroupAdministratorForOperated": "{{operator}} removed you from group admin.", "im_sys_msg_checkGroupAddition": "The group admin has enabled \"Membership Approval\". New members must send a join request and wait for admin approval.", "im_sys_msg_exitGroup": "[{{name}}|{{link}}] left this conversation.", "im_sys_msg_failToJoinCommonGroup": "{{invitee}} is an external staff member and cannot be added to internal conversations.", "im_sys_msg_failToJoinOverCompanyGroup": "Please send a verification request to {{names}} first. You can only invite them to join the conversation once you are contacts.", "im_sys_msg_groupChatHistoryStatus": "New members can now view the chat history {{browseHistory}} after they join.", "im_sys_msg_groupChatHistoryStatusAll": "All chat history is now available to new members", "im_sys_msg_groupChatHistoryStatusNo": "Chat history has been hidden for new members", "im_sys_msg_im_cancel_admin": "The group admin recalled a message.", "im_sys_msg_im_cancel_opposite": "This message has been recalled.", "im_sys_msg_im_cancel_self": "You recalled a message.", "im_sys_msg_im_cancel_someone": "{{operator}} recalled a message.", "im_sys_msg_im_cancel_system": "The system recalled a message.", "im_sys_msg_inviteJoinGroupForInviteeHasAuth": "{{operator}} invited you, {{invitees}} to this conversation. You can view the chat history {{timeRange}}", "im_sys_msg_inviteJoinGroupForInviteeHasAuthNoOthers": "{{operator}} invited you to this conversation. You can view the chat history {{timeRange}}", "im_sys_msg_inviteJoinGroupForInviteeNoAuth": "{{operator}} invited you, {{invitees}}, to this conversation.", "im_sys_msg_inviteJoinGroupForInviteeNoAuthAllMsgHasOthers": "{{operator}} invited you, {{invitees}} to this conversation. You can view all chat history.", "im_sys_msg_inviteJoinGroupForInviteeNoAuthAllMsgNoOthers": "{{operator}} invited you to this conversation. You can view all chat history.", "im_sys_msg_inviteJoinGroupForInviteeNoAuthAllMsgNoOthersAI": "{{operator}} invited you to this conversation. You can view all chat history.", "im_sys_msg_inviteJoinGroupForInviteeNoAuthNoMsgHasOthers": "{{operator}} invited you, {{invitees}}, to this conversation.", "im_sys_msg_inviteJoinGroupForInviteeNoAuthNoMsgNoOthers": "{{operator}} invited you to this conversation.", "im_sys_msg_inviteJoinGroupInviteeHasAuth": "You invited {{invitee}} to this conversation. New members can view the chat history {{timeRange}}.", "im_sys_msg_inviteJoinGroupInviteeHasAuthAll": "You invited {{invitee}} to this conversation. New members can view all chat history.", "im_sys_msg_inviteJoinGroupInviteeNoAuth": "You invited {{invitees}} to this conversation. {{shareHistory}}", "im_sys_msg_inviteJoinGroupThirdHasAuth": "{{operator}} invited {{invitee}} to this conversation. New members can view the chat history {{timeRange}}.", "im_sys_msg_inviteJoinGroupThirdHasAuthAll": "{{operator}} invited {{invitee}} to this conversation. New members can view all chat history.", "im_sys_msg_inviteJoinGroupThirdNoAuth": "{{operator}} invited {{invitee}} to this conversation.", "im_sys_msg_inviteRobotToJoinGroupForMember": "The system invited {{invitor}} to the conversation.", "im_sys_msg_inviteRobotToJoinGroupForMemberWithAllHistory": "{{operator}} invited {{invitor}} to this conversation. New members can view all chat history.", "im_sys_msg_inviteRobotToJoinGroupForMemberWithHistory": "{{operator}} invited {{invitor}} to this conversation. New members can view the chat history {{browseHistory}}.", "im_sys_msg_inviteRobotToJoinGroupForMemberWithNoHistory": "{{operator}} invited {{invitor}} to this conversation.", "im_sys_msg_inviteRobotToJoinGroupForOperatorWithAllHistory": "You invited {{invitor}} to this conversation. New members can view all chat history. {{cancelLink}}", "im_sys_msg_inviteRobotToJoinGroupForOperatorWithHistory": "You invited {{invitor}} to this conversation. New members can view the chat history {{browseHistory}}. {{cancelLink}}", "im_sys_msg_inviteRobotToJoinGroupForOperatorWithNoHistory": "You invited {{invitor}} to this conversation.", "im_sys_msg_inviteToJoinGroupNotice": "{{operator}} invited {{names}} to this conversation.", "im_sys_msg_inviteToJoinSecretGroupNoticeForInviter": "You invited {{invitee}} to this incognito conversation.", "im_sys_msg_inviteToJoinSecretGroupNoticeForMember": "{{operator}} invited {{inviteeInfoList}} to this incognito conversation.", "im_sys_msg_joinGroupBySelf": "{{operatorText}} joined the conversation.\n", "im_sys_msg_joinGroupNotice": "Tips", "im_sys_msg_joinGroupNoticeNew": "{{<PERSON><PERSON><PERSON><PERSON>}} joined this conversation.", "im_sys_msg_joinGroupNoticeYou": "You joined this conversation.", "im_sys_msg_joinOrgGroup": "Welcome to the {{groupName}} team! Everyone here is a colleague. Say hi!", "im_sys_msg_no_auth": "No Access", "im_sys_msg_one_day": "from the last day", "im_sys_msg_one_week": "from the last week", "im_sys_msg_operateGroupChatForbidSwitch": "The group admin has {{switch}} mute mode.", "im_sys_msg_operateGroupChatForbidSwitch_close": "disabled", "im_sys_msg_operateGroupChatForbidSwitch_open": "enabled", "im_sys_msg_phoneCall": "{{from}} called (viewed) {{to}}'s phone number. You can redial or callback on your phone.", "im_sys_msg_privateTel": "{{from}} called {{to}}'s encrypted phone number. You can redial or callback on your phone.", "im_sys_msg_privateTelContent": "{{from}} dialed {{to}}'s encrypted mobile number.", "im_sys_msg_removeBotInGroup": "You removed {{invitee}} from this conversation.", "im_sys_msg_removeFromOrgGroup": "You've been removed from this conversation.", "im_sys_msg_removeGroupMember": "You removed {{targetName}} from this conversation.", "im_sys_msg_robotInviteJoinGroup": "{{operator}} invited {{names}} to this conversation. New members can view the chat history {{browseHistory}}.", "im_sys_msg_robotInviteJoinGroupForInvitee": "{{operator}} invited {{names}} to this conversation. New members can view the chat history {{browseHistory}}. This setting can be modified in Group Settings.", "im_sys_msg_robotInviteJoinGroupForInvitee_withAllHistory": "{{operator}} invited {{names}} to this conversation. New members can view all chat history. This setting can be modified under Group Settings.", "im_sys_msg_robotInviteJoinGroupForInvitee_withNoHistory": "{{operator}} invited {{names}} to this conversation. New members cannot view chat history.", "im_sys_msg_robotInviteJoinGroup_withAllHistory": "{{operator}} invited {{names}} to this conversation. New members can view all chat history.", "im_sys_msg_robotInviteJoinGroup_withHistory": "{{operator}} invited {{names}} to this conversation. New members can view the chat history {{browseHistory}}.", "im_sys_msg_robotInviteJoinGroup_withNoHistory": "{{operator}} invited {{names}} to this conversation. New members cannot view chat history.", "im_sys_msg_robotModifyGroupName": "{{operator}} changed the group name to '{{groupName}}'.", "im_sys_msg_scanToJoinGroupForMemberWithAllHistory": "{{operator}} joined this conversation via the QR code. New members can view all chat history.", "im_sys_msg_scanToJoinGroupForMemberWithHistory": "{{operator}} joined this conversation via the QR code. New members can view the chat history {{browseHistory}}.", "im_sys_msg_scanToJoinGroupForMemberWithNoHistory": "{{operator}} joined this conversation via the QR code.", "im_sys_msg_scanToJoinGroupForOperator": "You joined this conversation via the QR code.", "im_sys_msg_secret_defaultReadCountDown": "The default self-destruct timer for messages in this group is set to {{time}}.", "im_sys_msg_secret_setDefaultReadCountDown": "{{operator}} set the message self-destruct timer to {{time}}.", "im_sys_msg_setGroupAdministratorForOperated": "{{operator}} set you as group admin.", "im_sys_msg_setGroupNameGuide": "[Name the group|mtdaxiang://www.meituan.com/editGroupName] for easy searching.", "im_sys_msg_systemOperateGroupChatForbidSwitch": "The group admin has {{switch}} mute mode.", "im_sys_msg_systemSetMeAdmin": "You have been set as group admin.", "im_sys_msg_systemSyncGroupName": "System synchronised the department name as '{{groupName}}'.", "im_sys_msg_telContent": "{{from}} dialed(viewed) {{to}}'s mobile number.", "im_sys_msg_updataGroupNameByOthers": "{{operator}} changed the group name to {{groupName}}.", "im_sys_msg_updataGroupNameByYou": "You changed the group name to {{groupName}}.", "im_sys_msg_updataGroupNameForMember": "The group name of [{{name}}|{{vcard}}] has been updated to {{groupName}}.", "im_sys_msg_updataGroupNameForOperator": "{{operator<PERSON>ame}} changed the group avatar.", "im_sys_msg_updataGroupNameForOperatorByYou": "You changed the group avatar.", "im_sys_msg_updateBotAdministrator_cancel": "You removed {{targetName}} from group admin.", "im_sys_msg_updateBotAdministrator_set": "You set {{targetName}} as group admin.", "im_sys_msg_updateGroupAdministratorForOwner": "You {{operationValue}} {{targetName}} as group admin.", "im_sys_msg_updateGroupAdministratorForOwnerNew": "You removed {{targetName}} from group admin.", "im_sys_msg_updateGroupAdministratorForOwnerNewSet": "You set {{targetName}} as group admin.", "im_sys_msg_updateGroupAdministratorForOwner_cancel": "Cancel", "im_sys_msg_updateGroupAdministratorForOwner_set": "Edit", "im_sys_msg_updateGroupAnnounceForMember": "{{operator}} updated the group notice. ", "im_sys_msg_updateGroupAnnounceForOperator": "You updated the group notice. ", "im_sys_msg_updateGroupAvatarFail": "Avatar review failed, please [re-upload|{{link}}]", "im_sys_msg_updateGroupAvatarTimeOut": "Avatar review failed, please [re-upload|{{link}}]", "im_sys_msg_updateOwernToOthers": "{{oldAdmin}} assigned {{newAdmin}} as the group owner.", "im_sys_msg_updateOwernToUserByYou": "You assigned {{new<PERSON><PERSON><PERSON>}} as the group owner.", "im_sys_msg_updateOwernToUserToYou": "{{old<PERSON><PERSON><PERSON>}} assigned you as the group owner.", "im_sys_msg_vote_anonymousVote": "An anonymous group member voted on {{topic}}.", "im_sys_msg_vote_realNameVote": "{{voter}} voted on {{topic}}.", "im_sys_msg_withDrawText": "Revoke invite", "im_sys_msg_withdrawGroupInvitation": "You revoked the group invitation. {{invitee}} has been removed from this conversation.", "im_sys_msg_you": "You", "im_sys_share_history": "Select messages to sync", "im_system_account": "System Account", "im_tail_text_login_again": ", please log in again.", "im_thread_count": "{{num}} reply", "im_thread_counts": "{{num}} replies", "im_timezone_update_later": "Later", "im_timezone_update_restart": "<PERSON><PERSON>", "im_toggle_language_failed": "Failed to switch. Please try again later.", "im_translate_cancel_thump_down": "Dislike removed.", "im_translate_cancel_thump_up": "Like removed.", "im_translate_feedback_report_cancel": "No", "im_translate_feedback_report_confirm": "Yes", "im_translate_feedback_report_desc": "We're sorry this translation didn't meet your expectations. Would you like to upload this task to help us improve?", "im_translate_feedback_report_success": "Uploaded successfully.", "im_translate_feedback_report_title": "Translation Quality Issue", "im_translate_go_to_setting": "Switch", "im_translate_into_english": "Action", "im_translate_language1": "Translate into {{language}}", "im_translate_language_en": "English", "im_translate_language_pt_br": "Portuguese", "im_translate_language_zh": "Simplified Chinese", "im_translate_language_zh_hk": "Traditional Chinese", "im_translate_providerenterprise_data_intelligence": "EDI", "im_translate_replace_modal_no_supported_specific_information": "Bold, italic, underline, and other text formats will be converted to plain text.", "im_translate_replace_modal_no_supported_title": "Text Formatting Not Supported", "im_translate_thump_down": "Disliked", "im_translate_thump_up": "Liked", "im_translate_to_language": "Translate into {language}", "im_translate_translation_support_provided_by_provider": "Translated by {{provider}}", "im_translate_withdraw_mac": "<PERSON><PERSON> (⌘Z)", "im_translate_withdraw_win": "Undo (Ctrl+Z)", "im_translated_into_the_target_language": "Translate into {{targetLanguage}}", "im_translation_failed": "Translation failed. ", "im_translation_failed_click_retry": "Translation failed, click to retry.", "im_translation_support_provided_by_provider": "Translated by {provider}", "im_turn_off_editor_translate_failed": "Failed to turn off. Please try again later.", "im_turn_on_editor_translate_failed": "Failed to turn on. Please try again later.", "im_undead": "Unread", "im_unfold": "Show More", "im_unicard_due_to_content_in_streaming_not_supported_for_ai_overview": "The messages being output do not support AI summarize.", "im_unicard_due_to_content_in_streaming_not_supported_for_forwarding": "The messages being output cannot be forwarded.", "im_unicard_due_to_content_in_streaming_not_supported_for_memo": "The messages being output do not support memo.", "im_unicard_due_to_content_in_streaming_not_supported_for_more_actions": "The messages being output do not support more actions.", "im_unmark": "Unmark", "im_unread_message": "unread", "im_unread_message_count": "{{num}} message", "im_unread_message_counts": "{{num}} messages", "im_unread_message_line_tips": "New", "im_upload_files_with_number_fail": "{{failUploadNumbers}} files failed to upload", "im_upload_one_file_with_fail": "1 file failed to upload", "im_vcard": "Card", "im_video_type_not_support": "Unsupported Video Format", "im_view_vcard": "View Profile", "im_voice_call_join_notification": "invites you to a voice meeting. Check on your mobile.", "im_voice_call_you_notification": "invites you to a voice call. Check on your mobile.", "im_voip_meeting": "A voice meeting is in progress, you can view it on your mobile phone.", "im_voip_meeting_ended": "Voice meeting ended.", "im_watch_in_phone": "You can check it on your phone.", "im_withdraw": "Recall", "im_words_are_not_displayed": "There are {{hideTextLength}} characters not shown.", "im_write_email": "Email", "im_you": "you", "im_you_cancel_a_message": "You recalled a message.", "im_your_peer_is_busy_now": "Line busy", "im_yunpan_picture": "Cloud Images", "image_editor_modal_input_placeholder": "Leave a message", "image_editor_modal_title_edit": "Edit", "image_has_been_withdrawn": "Image has been recalled", "image_out_of_date": "Image expired", "important_msg_setting_includes_keyword": "Messages containing keyword \"{{keyword}}\"", "important_msg_setting_mute": "New messages of this type will no longer be highlighted with ⚡️ notifications.", "important_msg_setting_notice": "New messages of this type will now be highlighted with ⚡️ notifications.", "important_msg_setting_remove_keyword": "Remove {{keyword}} from keywords at the same time", "imsdk_err_client_err_feat_not_support": "Unsupported feature", "imsdk_err_client_err_not_init": "Uninitialised", "imsdk_err_client_err_param": "Parameter exception", "imsdk_err_client_err_proto": "proto parsing exception", "imsdk_err_client_err_resend_fail_kickoff": "Kicked offline, failed to send", "imsdk_err_client_err_resend_fail_logout": "User logged out, failed to send", "imsdk_err_client_err_resend_fail_max_retry": "Send timeout, resend failed", "imsdk_err_client_err_send_resend_message": "The message has been successfully sent", "imsdk_err_file_error": "File transfer error", "imsdk_err_network_bad_request": "Invalid HTTP request parameters", "imsdk_err_network_error": "Network logic error", "imsdk_err_network_not_found": "No results found", "imsdk_err_server_err_failed": "Server request failed", "imsdk_err_server_err_timeout": "Server request timeout", "imsdk_err_server_forbidden": "server-side denial of service", "imsdk_err_server_message_withdraw": "Message has been recalled", "imsdk_err_server_speed_limit": "server-side speed limit", "imsdk_err_storage_cannot_open": "Unable to open the file", "imsdk_err_storage_constraint_violation": "Violating constraints", "imsdk_err_storage_db_internal_err": "Internal database error", "imsdk_err_storage_db_not_open": "Unable to open the database", "imsdk_err_storage_execute_fail": "SQL statement execution failure", "imsdk_err_storage_param_err": "Parameter error", "imsdk_err_unknown": "Unknown error", "input_can_not_empty": "{{name}} can‘t be empty", "input_exceed_limit": "{{name}} can't be more than {{maxLength}} characters", "just_now": "Just Now", "km_creator": "Creator", "km_doc_unable_to_view": "The search term is protected by security policies and can't be viewed.", "km_document_selected_limit": "Maximum of {{MAX_NUMBER_OF_SEND}} items can be selected.", "km_final_modification": "Last edited", "km_lastedited": "Last viewed", "label_no_grouping_available": "No labels", "later_order_by_mark": "Recently Added First", "later_order_by_msg": "Newest Messages First", "link_card_add_permission_chat": "Grant View Access to Recipient", "link_card_add_permission_fail": "Failed, please try again later.", "link_card_add_permission_group_chat": "Grant View Access to This Group", "locale": "en", "long_text_unfold_failed": "Failed", "lower_than_recommended_security_level": "Lower than the recommended security classification, please choose carefully.", "m_translation_failed": "Translation failed. ", "mac_editor_enter_fullscreen": "Full Screen (⌘S)", "main_nav_adjust_main_navigator": "adjust navbar sorting", "main_nav_approval": "Approval", "main_nav_calendar": "Calendar", "main_nav_contacts": "Contacts", "main_nav_demo": "demo", "main_nav_meeting": "Video Meeting", "main_nav_msgs": "Chats", "main_nav_no_free_space_to_show_tab_hint": "Window too small to display app in navigation. Moved to \"More\".", "main_nav_pan": "Cloud Drive", "main_nav_tab_moved_to_more_hint": "Moved to More", "main_nav_tab_moved_to_navigation_hint": "Moved", "main_nav_task": "Tasks", "main_nav_vpn": "VPN", "main_nav_workbench": "Workplace", "main_search_result_within_a_month": "in the last month", "main_search_result_within_a_period": "in the last {{month}} months", "main_search_result_within_a_week": "in the last week", "mark_page_after_mark_important_message": "After marking important messages, ", "mark_page_can_find_it_here": "you can find them here.", "mark_page_cancel_mark": "Unmark", "mark_page_cancel_mark_msg_prompt_info": "After unmarking, chat members will not be able to see the message in the Mark list", "mark_page_download": "Download", "mark_page_downloading": "Downloading", "mark_page_failed_to_cancel_mark": "Unmark failed", "mark_page_failed_to_load": "Loading failed", "mark_page_forward": "Forward", "mark_page_forwarding": "Forward", "mark_page_loading": "Loading", "mark_page_mark_message": "<PERSON>", "mark_page_more_operation": "More", "mark_page_new": "New", "mark_page_no_more": "No more", "mark_page_no_network": "Network disconnected", "mark_page_not_cancel": "Cancel", "mark_page_posted_on": "posted on", "mark_page_preview": "Preview", "mark_page_request_failed": "Request failed", "mark_page_sure_to_cancel_mark_msg": "Are you sure to unmark this message", "mark_page_the_day_before_yesterday": "The day before yesterday", "mark_page_upload_retry": "Retry", "mark_page_video_preview": "Video Preview ", "mark_page_view": "Click to view", "mark_page_view_the_context": "Find in Chat", "mark_page_yesterday": "Yesterday", "markdown_exit_message": "Your edits will be discarded.", "markdown_exit_title": "Exit without saving?", "markdown_input_placeholder": "Enter content here", "markdown_preview": "Preview", "markdown_preview_tips": "Auto-generated Markdown rendering.", "markdown_send_message_to": "To:", "markdown_send_tips": "Will send in the preview format", "markdown_to_rich_text_paste_cancel_convert": "Cancel", "markdown_to_rich_text_paste_toast_tips": "Markdown syntax detected, format automatically converted.", "markdown_window_content_cover": "Overwrited", "markdown_word_exceed_tips": "Maximum 6000 characters allowed. Adjust content before sending.", "merge_forward_loading": "Loading...", "merge_forward_reload_button_label": "Reload", "merge_forward_request_failed": "Request failed, please try again later", "message_add_todo": "Add To-Do", "message_footer_markdown_render_view": "Markdown Preview View", "message_footer_markdown_source_view": "Markdown Source View", "message_show_markdown_content_view": "Switch to Preview View", "message_show_markdown_source_view": "Switch to Source View", "minutes_ago": "minute(s) ago", "modify_group_avatar_network_error": "No network, upload failed", "modify_group_avatar_review_warning": "Can't modify avatar during review", "modify_group_avatar_submit_done": "Avatar modification successful, automatically updated upon approval", "modify_group_avatar_text_overflow": "{pluralCount, plural, one {Maximum of {{wordCount}} Chinese character or {{charCount}} character for avatar text} other {Maximum of {{wordCount}} Chinese characters or {{charCount}} characters for avatar text}}", "modify_group_avatar_text_overflow_fixed": "Maximum of 9 Chinese characters or 18 characters for avatar text", "modify_group_avatar_title": "Modify Group Avatar", "modify_group_avatar_too_large": "Image is too large, please re-upload!", "monaco_editor_auto_wrap": "Word Wrap", "monaco_editor_close": "Close Window", "monaco_editor_copy": "Copy", "monaco_editor_dark_theme": " Dark (Visual Studio)", "monaco_editor_edit": "Edit", "monaco_editor_file": "File", "monaco_editor_format": "Format Code", "monaco_editor_language": "Language", "monaco_editor_light_theme": "Light (Visual Studio)", "monaco_editor_restore": "Revert <PERSON>", "monaco_editor_save_as": "Save As...", "monaco_editor_save_error": "Save Failed", "monaco_editor_save_success": "Save Successful", "monaco_editor_select_all": "Select All", "monaco_editor_theme": "Color Theme", "monaco_editor_view": "View", "msg_helper": "Collapsed Chats", "msg_helper1": "Collapsed Chats", "msg_helper_add_success": "Added", "msg_helper_preview_msg_at": "You're mentioned in {{at}} group(s)", "msg_helper_preview_msg_group": "{{groupNewMsgNum}} group(s) ", "msg_helper_preview_msg_separate": ", ", "msg_helper_preview_msg_user": "{{chat}} DM(s) ", "msg_helper_prview_msg_sufix": "with new messages", "msg_helper_remove_success": "Removed", "msg_message_recalled": "This message has been recalled.", "name_and_other_personNumber_people_at_all": "{{name}}and {{personNumber}} others @All", "name_and_other_personNumber_people_at_you": "{{name}} and {{personNumber}} others mentioned you", "navView_toolbar_message_is_retraction": "Recalled", "nav_add_button_action": "Add", "nav_add_into_nav": "Add to navigation", "nav_add_into_nav_toast": "\"{{appName}}\" added to navigation", "nav_app_top_add_tooltip": "Add to navigation", "nav_common_app_menu_remove": "Remove the App", "nav_common_app_menu_unselect": "Move to \"More\"", "nav_disable_button_action": "Can't be removed", "nav_more_first_show_hint": "Cloud Drive, Video Meeting and VPN into More now, support displaying apps from More in navbar and sorting them in the Settings~", "nav_more_first_show_hint_known_button": "Got it", "nav_more_first_show_hint_to_settings_button": "Settings", "nav_more_panel_more_setting": "More", "nav_more_panel_more_setting_tooltip": "Navbar Management", "nav_more_panel_show_in_navigation": "Display in navbar", "nav_new_app_add_navbar_path": "「[Settings > Navbar Management|{{url}}]」", "nav_new_app_add_optional_area_tips": "\"{{appName}}\" moved to Selectable Section. Re-add via「[Settings > Navbar Management|{{url}}]」Management", "nav_new_app_add_optional_area_tips2": "Re-add via {{navBarPath}} Management", "nav_new_app_hint_add_later": "Try Later", "nav_new_app_hint_not_now_button": "Not Now", "nav_new_app_hint_try_button": "Try It", "nav_new_app_network_error": "Network error", "nav_new_app_remain_in_nav_tips": "\"{{appName}}\" has been confirmed by your department and will remain in the Navbar for daily use", "nav_offline_hint_confirm_button_label": "Exit and Remove", "nav_offline_hint_message": "App is offline, please contact the admin to view details", "nav_offline_hint_title": "Admin has taken down the app", "nav_open_in_browser": "Open in browser", "nav_recommend_app_tab": "Recommended", "nav_remove_button_action": "Remove", "nav_remove_from_more": "Remove from \"More\"", "nav_remove_from_nav_toast": "\"{{appName}}\" removed from navigation", "nav_setting_disable_hidden_and_remove": "Sorting isn't supported yet", "nav_setting_disable_remove_item_from_tab": "Can't be removed", "nav_setting_disable_sort_and_remove": "Can't be removed and sorted", "nav_setting_disable_tab": "De-select", "nav_setting_enable_tab": "Select", "nav_setting_fold_hint": "Smaller window size, temporarily displayed in More Apps", "nav_setting_panel_available": "Selectable", "nav_setting_panel_available_app_empty": "No selectable apps", "nav_setting_panel_common_app": "Common apps", "nav_setting_panel_common_app_tips": "Show selected apps in the navigation bar. Note that if the window is small, apps at the bottom of the list may not be displayed", "nav_setting_panel_enabled": "Selected", "nav_setting_panel_more_app": "More Apps", "nav_setting_panel_more_app_empty": "No more apps, drag and drop to add", "nav_setting_panel_navigation_app": "<PERSON><PERSON><PERSON>", "nav_setting_panel_navigation_app_tips": "Show selected apps in navbar, note that when the window size is small, the apps at the bottom of the list will be shown in \"More\"", "nav_setting_panel_office_app": "Office apps", "nav_setting_pin_hint": "Display in navbar", "nav_setting_remove_item_from_tab": "Remove from navbar", "nav_setting_unpin_hint": "Move to More", "nav_sort_banned_message": "'Chats' doesn't support sorting", "nav_update_hint_add": "Add", "nav_update_hint_label_more": "New apps \"{{appOne}}\", \"{{appTwo}}\", \"{{appThree}}\" etc. can be added, click ", "nav_update_hint_label_one": "New apps \"{{appOne}}\" can be added, click ", "nav_update_hint_label_three": "New apps \"{{appOne}}\", \"{{appTwo}}\", \"{{appThree}}\" can be added, click ", "nav_update_hint_label_two": "New apps \"{{appOne}}\", \"{{appTwo}}\" can be added, click ", "nav_update_hint_to_setting": "Settings-Navbar", "network_fail_after_check_proxy_hint": "If you are using a proxy, please make sure that the proxy service is running properly. If you think you don't need a proxy service, please execute:", "network_fail_cancel_proxy_hint_mac": "Open Network Preferences → Advanced → Proxy, uncheck all selected proxies.", "network_fail_cancel_proxy_hint_win": "IE browser → Tools → Internet Options → Connections → LAN Settings → Proxy Server, uncheck all selected proxies.", "network_fail_close_window_btn": "Close", "network_fail_methods_please": "Try the following methods", "network_fail_open_diagnose_btn": "Diagnosis and Fix", "network_fail_refresh_btn": "Reload", "network_fail_retry_after_check_hint": "Check the network, modem and router and try again to connect to the internet.", "network_fail_title": "Your device is not connected to the network", "new_wps_online_doc": "Create WPS online ", "no_more_data": "No more", "no_search_results_found": "No results found", "over_max_characters_with_options": "Exceeded {{overCharacters}} characters", "over_one_characters_with_options": "Exceeded one character", "page_about_commit": "Submit", "page_about_copy": "Copy", "page_about_current_version": "Version", "page_about_date": "Date", "page_about_env": "environment", "page_about_im_version": "IMSDK version", "page_about_ok": "Confirm", "page_about_pub_date": "Post date", "page_about_sys_info": "System Version Details", "page_about_version": "Version", "page_im_download_apply_download": "Apply for download", "page_im_download_apply_modal_message": "Downloading requires an application and can be done after approval", "page_im_download_apply_modal_title": "The file is under security control and cannot be downloaded", "page_im_download_turn_off": "Close", "pam_security_level_modify": "Modify Security Classification", "pan_access_my_documents": "Access my files", "pan_accomplish": "Accomplish", "pan_add_group_space": "Add", "pan_add_new_member": "Add new member", "pan_add_star": "Star", "pan_add_star_fail": "Failed to star", "pan_add_star_success": "Starred successfully", "pan_add_to": "Add", "pan_add_your_common_group_space_to_cloud": "Add your frequently used group space to the cloud for easy access", "pan_all_files": "All files", "pan_all_image_total_other": "All images, total {{num}}", "pan_all_loaded_total_other": "All loaded, total {{num}}", "pan_already_used": "Used", "pan_are_you_sure_cancel_share": "Confirmed to cancel the share?", "pan_back_to_previous": "Back to previous", "pan_being_file_action": "file", "pan_being_files_action": "{pluralCount, plural, one {{{count}} file} other {{{count}} files}}", "pan_being_files_action_other": "{{count}} files ", "pan_cancel": "Cancel", "pan_cancel_all": "Cancel All", "pan_cancel_delete": "Cancel deletion", "pan_cancel_selection": "Cancel", "pan_cancel_share": "End sharing", "pan_cancel_share_msg": "The link will expire, confirming the termination of this share?", "pan_cancel_upload": "Cancel upload", "pan_change_security_level": "Modifying the security classification will affect the permissions of the file. Are you sure about the modification?", "pan_chat_file": "Chat files", "pan_check_details": "Details", "pan_chosen": "Selected", "pan_clear_completed": "Clear completed", "pan_click_upload_button_to_add": "Use the \"Upload\" button in the upper left corner to add", "pan_cloud_disk": "Cloud Drive", "pan_common_group_space": "Common Group Spaces", "pan_common_load_error": "Failed to load", "pan_comp_image_overdue": "Access has expired", "pan_confidential": "Classified", "pan_confidential_info_1": "Information shared only within the department.", "pan_confidential_info_2": "Meeting minutes, weekly reports, project plans, etc.", "pan_confidential_info_3": "Business requirements, access documentation, project technical solutions, PRD, etc.", "pan_confidential_info_4": "Current situation review, risk report, internal system issues or major security incidents, etc.", "pan_confidential_info_5": "Business activity plan, such as selling strategies, etc.", "pan_confirm": "Confirm the deletion", "pan_confirm_cancel": "Confirm", "pan_confirm_delete": "Confirm", "pan_confirm_delete_completely": "Confirm permanent deletion", "pan_confirm_delete_group_space": "Are you sure about removing the group space", "pan_confirm_notify_title": "Tips", "pan_convert_file_to_link": "Create WPS Online Doc", "pan_copy_link": "Copy", "pan_copy_link_and_code": "Copy", "pan_copy_link_approved": "Link: {{wenshuSendUrl}} extraction code: {{password}}, this link is approved and valid until {{date}}", "pan_copy_link_being_processed": "Link: {{wenshuSendUrl}} extraction code: {{password}}, this link is under approval and can be accessed after approval", "pan_copy_link_no_approval_required": "Link: {{wenshuSendUrl}} extraction code: {{password}}, this link doesn't require approval and is valid until {{date}}", "pan_create_dir_error": "Creation failed", "pan_create_dir_over_500": "Create too many at once, please split them up, no more than 500 each time", "pan_create_dir_success": "Created successfully", "pan_data_security_management": "According to the company's", "pan_data_security_management_regulations": " \"Data Security Management Regulations\"", "pan_data_security_management_requirements": ", all documents in the cloud need to be marked with security classification. Please select the security classification of this document, which can't be lower than the recommended security classification:", "pan_delete": "Delete", "pan_delete_completely": "Confirm", "pan_delete_completely_success": "Completely deleted successfully", "pan_delete_fail": "Delete failed", "pan_delete_success": "Successfully deleted", "pan_delete_the_star_fail": "Failed to remove starred", "pan_delete_the_star_successfully": "Success", "pan_did_you_confirm_delete_file": "Are you sure to delete the file?", "pan_did_you_confirm_delete_share_file": "Sure to delete your sharing relationship with the selected file?", "pan_document": "Files", "pan_download": "Download", "pan_download_accomplish": "Downloaded", "pan_download_completed": "Downloaded", "pan_download_fail": "Download failed", "pan_download_failed": "Download failed", "pan_download_success": "Download successfully", "pan_downloading": "Downloading", "pan_dump": "<PERSON><PERSON>\n", "pan_dump_file": "<PERSON>mp <PERSON>", "pan_failed_count": "file", "pan_failed_deleted": "Failed", "pan_failed_files_count": "{pluralCount, plural, one {{{count}} file failed} other {{{count}} files failed}}", "pan_failed_files_count_other": "{{count}} files failed", "pan_file": "Files", "pan_file_all": "All", "pan_file_analysis": "File analysis...", "pan_file_analysis_error": "File analysis failed", "pan_file_analysis_error_helper": "Abnormal errors are reported during the file analysis process, please try again or ask the Wenshu <a className=\"link\" target=\"_blank\" href=\"https://tt.sankuai.com/ticket/custom/create/1295/5769\"> submit TT </a> for help.", "pan_file_forbid_move": "The file is already here, cannot be moved", "pan_file_name_cant_empty": "File name can't be empty", "pan_file_name_forbid_dot": "The filename cannot be . or ..", "pan_file_name_forbid_pure_space": "File name cannot be empty", "pan_file_name_forbid_special_character": "File names cannot contain characters <>, :, |, *, ?, /, or \\.", "pan_file_name_too_long": "File name cannot exceed 128 bytes", "pan_folder": "Folder", "pan_folder_has_been_deleted": "Folder with files like {{firstFailName}} has been deleted", "pan_forbid_store_private_information": "After resigning, work documents will be handed over to others. Please do not store personal information.", "pan_format_not_support_preview_try_download": "This format is not supported for preview, please download and view it.", "pan_forward_click_to_view_document": "Click to view document", "pan_forwarding": "Forward", "pan_from": "From: ", "pan_generate_wps_online_document_success": "WPS online document has been generated.", "pan_get_bubble_file_info_fail": "No access to information", "pan_get_download_strategy_fail": "Failed to get file download strategy", "pan_get_download_url_fail": "No access to information", "pan_get_preview_url_fail": "Failed to get preview url", "pan_get_role_failed": "Failed to get file role, default value will be used", "pan_get_secret_strategy_fail": "Failed to get Wenshu control strategy", "pan_get_share_status_error": "Request failed, please try again later", "pan_go_to_this_session": "Switch to view", "pan_go_to_view": "To view it.", "pan_group_delete_many_notice": "{{num}} files including {{f1}} and {{f2}}", "pan_group_delete_two_notice": "{{f1}} and {{f2}}", "pan_group_my_download": "My Downloads", "pan_group_my_previewed": "My Previews", "pan_group_my_sent": "The message I sent.", "pan_group_recently_used": "Recent", "pan_group_space_short": "Group Space", "pan_handing": "Processing", "pan_handover": "Transfer", "pan_handover_error": "Transfer failed", "pan_handover_fail_choose_self": "Please don‘t transfer this file to yourself", "pan_handover_popup_title": "Transfer file: {{fileName}}", "pan_handover_success": "Success", "pan_i_know": "Got it", "pan_image": "Images", "pan_in_company_share_close": "Enterprise sharing is closed", "pan_in_company_share_open": "Enterprise sharing is enabled", "pan_input_no_title": "Untitled", "pan_insert": "Insert", "pan_issued_share": "Shared emitted", "pan_link": "Link", "pan_link_approved2": "Approval has been granted and the link is valid until {{date}}", "pan_link_code": "Code", "pan_link_copy_failed": "<PERSON><PERSON> failed, please try again later.", "pan_link_copy_success": "<PERSON>pied", "pan_link_custom": "Custom", "pan_link_custom_date": "({{customTime}} days)", "pan_link_max_validity": "Links are valid for up to 30 days", "pan_link_no_approval_required2": "The file is automatically approved and the ink is valid until {{date}}", "pan_link_processed_by_many": "{{approverUserName}}({{approverUserMis}}), etc. are in the process of approval and the link can't be accessed until it's approved", "pan_link_processed_by_one": "{{approverUserName}}({{approverUserMis}}) is in the process of approval and the link can't be accessed until it's approved", "pan_link_see_detail": "Details", "pan_link_validity": "Validity", "pan_link_validity_value": "{{duration}}", "pan_list_view": "List", "pan_load_failed_try_again": "Loading failed, please try again later.", "pan_loaded_other": "{{num}} loaded", "pan_loading": "Loading...", "pan_loading_more_data": "Loading", "pan_loading_now": "Loading", "pan_member": "Members", "pan_members": "Members", "pan_more": "More", "pan_move": "Move", "pan_move_failed": "Move failed", "pan_move_file": "Move", "pan_move_files_to_this": "Move files here", "pan_move_success": "Moved successfully", "pan_my_cloud_disk": "Private Drive", "pan_my_documents_introductions": "\"My Files\" can store your personal work documents, exclusive personal files.", "pan_my_download": "My Downloads", "pan_my_file": "Private Drive", "pan_my_previewed": "My Previews", "pan_my_received": "The one I received", "pan_my_sent": "The message I sent.", "pan_name": "Name", "pan_new": "New", "pan_new_create": "New", "pan_new_folder": "New Folder", "pan_no_download_files": "No files to download", "pan_no_file": "No file", "pan_no_intro": "No introduction", "pan_no_related_documents_found": "No results found", "pan_no_upload_files": "No files uploaded yet.", "pan_not_support_share_file_from_other": "Supports external sharing of your files only", "pan_online_excel": "New WPS Sheets", "pan_online_ppt": "New WPS Powerpoint", "pan_online_word": "New WPS Docs", "pan_only_delete_entry_and_files_will_not_affected_you_can_add_at_any_time": "It will only remove the shortcut portal, it will not affect the group files and can be added again at any time.", "pan_only_support_share_single_file": "Supports external sharing of single file only", "pan_only_you": "Only you", "pan_other": "Other", "pan_outside_company_share_close": "External sharing is closed", "pan_over_100m_images_cannot_preview": "Images larger than 100MB do not support preview, please download and view them locally.", "pan_over_200m_compressed_packages_cannot_preview": "Files larger than 200MB are not supported for preview. Please download them locally to view.", "pan_owner": "Owner", "pan_positioning": "Positioning", "pan_public_info_1": "Publicly available data released by the company such as Meituan <PERSON>ping press releases, public announcements, merchant advertisements, and group buying information, etc.", "pan_reason_required": "Required information", "pan_received_sharing": "Received sharing", "pan_recently_used": "Recent", "pan_recover": "Rest<PERSON>", "pan_recover_success": "File restored", "pan_recycle_bin": "Recycle Bin", "pan_reduction": "Rest<PERSON>", "pan_rejected_link_share": "{{rejectUserName}}({{rejectUserMis}}) rejected the application, reapplying requires opening the external sharing", "pan_remove_completely": "Permanently delete.", "pan_remove_star": "Remove star", "pan_rename": "<PERSON><PERSON>", "pan_rename_error": "Rename error", "pan_rename_fail": "Failed to rename", "pan_rename_file": "<PERSON><PERSON>", "pan_rename_file_not_found": "File doesn't exist", "pan_rename_success": "Successfully renamed", "pan_request_fail_retry_moment_later": "Request failed, please try again later", "pan_required_outgoing": "Apply to create a link", "pan_restricted": "Restricted", "pan_restricted_info_1": "Institutional or internal resource information for all staff members", "pan_restricted_info_2": "Knowledge sharing and summarization, tutorial courseware", "pan_restricted_info_3": "Business cases or publicly available solutions", "pan_restricted_info_4": "Product user manual, FAQ, etc.", "pan_restricted_info_5": "Project descriptions, work plans, etc. for all staff promotion", "pan_restricted_info_6": "Team related, such as work instructions, recruitment information, duty schedules, etc.", "pan_root_directory": "Root directory", "pan_save_failed": "Save failed", "pan_saved": "Saved", "pan_search": "Search", "pan_search_count_other": "Total {{num}}", "pan_search_files": "Search", "pan_security_file": "File Permission Management", "pan_security_file_authorize": "Files I sent/uploaded", "pan_security_file_subsapprover": "Documents for review and approval.", "pan_security_level": "Security Classification", "pan_select_a_new_save_location": "The folder containing {{firstFailName}} has been deleted, please select a new save location", "pan_selected_other": "{{count}} selected", "pan_send": "Send", "pan_send_to": "Send", "pan_set_confirmation": "Confirmation", "pan_share": "Share", "pan_share_add_failed": "Add failed", "pan_share_add_member_title": "Share: {{fileName}}", "pan_share_add_success": "Added", "pan_share_cancel_failed": "Cancel failed", "pan_share_cancel_success": "Cancelled", "pan_share_delete_failed": "Delete failed", "pan_share_delete_success": "Deleted", "pan_share_dir_permission_denied": "You don't have permission to share this folder, if you want to share this folder, you can contact {{<PERSON><PERSON><PERSON>}} to grant you \"administrator\" permission.", "pan_share_error_read_only": "With only read-only access, you can't upload or create", "pan_share_file_modal_title": "Share: {{fileName}}", "pan_share_file_permission_denied": "You don't have permission to share this file, if you want to share this file, you can contact {{<PERSON><PERSON><PERSON>}} to grant you \"administrator\" permission.", "pan_share_in_company": "Enterprise sharing", "pan_share_in_company_tip": "Permission to view within the company, link valid for three months", "pan_share_links": "Shared link", "pan_share_modify_failed": "Modification failed", "pan_share_modify_success": "Modified", "pan_share_no_reason_or_date": "Fill in the reason for outsourcing or select an expiration date", "pan_share_outside_company": "External sharing", "pan_share_outside_company_tip": "Outsiders have access, please make an outgoing report", "pan_share_outside_reason": "Reason", "pan_share_permission_denied": "You don't have permission to share this {{fileType}}, if you want to share this {{fileType}}, you can contact {{creator<PERSON><PERSON>}} to grant you “administrator” permission.", "pan_share_time": "Shared Time", "pan_share_with_me": "Shared Drive", "pan_share_with_others": "Share with others", "pan_shared_documents": "Shared files", "pan_sharing_via_link": "Share through link", "pan_size": "Size", "pan_starred": "Starred", "pan_successfully_added": "Added", "pan_successfully_deleted": "Successfully deleted", "pan_terminated": "End", "pan_there_is_no_group_space": "No group space", "pan_this_month": "This month", "pan_this_week": "This week", "pan_thumbnail_view": "<PERSON><PERSON><PERSON><PERSON>", "pan_today": "Today", "pan_top_secret": "Top Secret ", "pan_top_secret_info_1": "Data or reports that have a significant impact on a company's earnings or development", "pan_top_secret_info_2": "Core algorithm or code", "pan_top_secret_info_3": "Financial data", "pan_top_secret_info_4": "Contains user identity information or highly sensitive personal information", "pan_top_secret_info_5": "Contains merchant & partner's real identity identification, financial information", "pan_transfer": "Transfer", "pan_transfer_files_to_this": "Transfer files here", "pan_unshare": "Cancel sharing", "pan_update_time": "Updated time", "pan_upload": "Upload", "pan_upload_accomplish": "Upload Completed", "pan_upload_and_convert_to_online": "Upload files and online", "pan_upload_completed": "Upload complete", "pan_upload_failed": "Upload failed", "pan_upload_files": "Upload Files", "pan_upload_folder": "Upload Folder", "pan_upload_retry": "Retry", "pan_uploading": "Uploading", "pan_wps_online_doc": "Word", "pan_wps_online_excel": "Excel", "pan_wps_online_ppt": "Power Point", "pc_add_into_nav_error_exception": "Failed to add. This app cannot be added to navigation from Workplace.", "pc_add_into_nav_error_maximum": "Failed to add. Maximum 30 apps allowed in navigation.", "pc_nav_new_app_add_recommend_area_tips": "\"{{appName}}\" removed from navigation", "picture_exceeds_5m": "Image exceeds 5M", "please_change_the_search_term_and_try_again_or_consult": "Please try different search terms, or ask ", "popup_add_robot_dialog_desc": "Invite the robot to join the group to start the robot automation process.", "popup_add_robot_dialog_title": "Invite the robot to join the group.", "popup_add_robot_invite_to": "Invitation to:", "popup_select_contact_confirm": "Confirm", "popup_select_contact_title": "Select Contacts", "preview_copy_doing": "Copying...", "preview_copy_error": "<PERSON><PERSON> failed, please try again later.", "preview_copy_error_authentication_failure": "Authentication failed", "preview_copy_success": "<PERSON>pied", "preview_extract_text_empty": "No text detected.", "preview_message_retraction": "This message has been recalled.", "preview_ocr_text_extract_guide": "Select text on images to extract it.", "preview_text_extract_all": "Extract all", "preview_text_extract_copy": "Copy", "preview_text_extract_failed": "Extraction failed", "preview_text_extract_image_too_large": "The image size is too large.", "preview_text_extract_not_business_rule": "Not compliant with business rules.", "preview_text_extract_placeholder": "Click to extract text for editing here.", "preview_text_extract_support_ability": "provide support for abilities", "preview_text_extract_title": "Result", "preview_text_extract_tree_platform": "<PERSON><PERSON><PERSON>", "preview_text_extracting": "Extracting", "recently_edited": "Recently edited", "recently_viewed": "Recently viewed", "record_search_loading": "Loading...", "record_search_name_with_count_other": "{{name}} and {{count}} others.", "record_search_name_with_ellipsis_other": "{{name}}...and {{count}} others", "record_search_not_limit_send_people": "From", "record_search_select_sender": "Search", "record_search_sender": "Sender", "record_search_sendtime_no_limit": "Date", "record_search_vote_title": "[Card]", "remember_is_there_any_missing_arrangement_oh": "Kind reminder: any other events on your schedule?", "repeat_groups_for_immediate_use": "Existing conversation available", "reply_message_view_context_without_permission_prompt": "This message is not within your accessible history range. Context cannot be viewed.", "reply_thread_count": "{{replyCount}} reply", "reply_thread_counts": "{{replyCount}} replies", "reply_thread_download": "Download", "reply_thread_downloading": "Downloading", "reply_thread_forward": "Forward", "reply_thread_more_operation": "More", "reply_thread_preview": "Preview", "reply_thread_title": "Details", "reply_thread_view": "Click to view", "rich_text_edit_link_href_empty": "Link can't be empty", "rich_text_edit_link_href_placeholder": "Paste or enter a link", "rich_text_edit_link_title_placeholder": "Enter text", "role_icon_all_staff": "All-staff", "role_icon_department": "Dept.", "role_icon_open": "Public", "role_icon_over_company": "External", "role_icon_public": "Public", "sc_add_cluster": "<PERSON><PERSON>", "sc_add_cluster_success": "Added to '{{name}}'.", "sc_add_session": "Add Chat to '{{cluster}}'", "sc_add_session_to_cluster_fail": "Failed", "sc_add_to_cluster": "Labels", "sc_already_collapsed_all": "All labels collapsed.", "sc_clean_read": "Collapse Read Chats", "sc_cluster_add_fail": "Failed", "sc_cluster_add_success": "'{{name}}' created.", "sc_cluster_id_not_right": "Error, please try again later.", "sc_cluster_init_fail": "Label initialization failed, please restart Daxiang.", "sc_cluster_max": "You can create up to {{length}} labels.", "sc_cluster_name_illegality": "Label name contains invalid characters, please edit again.", "sc_cluster_name_repeated": "Label name exists, please rename.", "sc_cluster_name_too_long": "Exceeded {{num}} characters.", "sc_cluster_rename_success": "Success", "sc_collapse_all_cluster": "Collapse All ({{shortcut}})", "sc_collapse_cluster": "Collapse", "sc_confirm_delete_cluster": "Delete Label", "sc_confirm_delete_cluster_content": "Once deleted, all conversations in '{{name}}' will be removed from the label.", "sc_create_and_add_save": "Create and Add", "sc_create_cluster": "New Label", "sc_create_save": "Create", "sc_created_a_new_group_and_added_a_session_to_name": "Label created and chat added to '{{name}}'.", "sc_delete_cluster": "Delete Label", "sc_delete_cluster_success": "'{{name}}' deleted", "sc_determine": "Add", "sc_disable_pin": "Show Unread Message Count Only", "sc_disable_pin_tips": "Once disabled, unread conversations are collapsed along with the label.", "sc_enable_pin": "Expose Unread Chats", "sc_enable_pin_tips": "Once enabled, unread conversations stay visible when the label is collapsed.", "sc_expand_cluster": "Expand Label", "sc_goto_cluster_after_add": " Go to view", "sc_group_name_modification_failed": "Failed", "sc_guide_toggle_all_expand": "Hold {{key}} and click a label to expand/collapse all labels.", "sc_input_cluster_name": "Please enter a label name", "sc_label_fold_pin_unread_session": "Show Unread Conversations when Collapsed", "sc_label_fold_show_unread_count": "Show Unread Count Only when Collapsed", "sc_label_notify_rule": "Label Notification Settings", "sc_label_sort": "Sort Label", "sc_make_all_as_read": "<PERSON> as <PERSON>", "sc_make_all_read": "<PERSON> as <PERSON>", "sc_no_cluster": "Unlabeled", "sc_no_more_to_collapsed": "No labels to collapse.", "sc_order": "Sort", "sc_recently_session": "Recents", "sc_remove_from_cluster": "Unlabel", "sc_remove_success": "Removed", "sc_rename_cluster": "Edit Label", "sc_rename_save": "Save", "sc_selected_limit": "You can only select up to {{limit}} conversions at a time.", "sc_session_tag_add_fail": "A label can only add up to {{sizeLimit}} conversations.", "sc_session_tag_add_success": "Added to '{{name}}'.", "sc_session_tag_remove_success": "Removed", "sc_set_cluster_pin": "Label notification set to show {{showRule}}.", "sc_set_cluster_pin_fail": "Failed to set label notification. Please try again.", "sc_show_session_pinned_collapsed_in_menu": "Show Unread Conversations when Collapsed", "sc_show_sessions_pinned_collapsed": "unread conversations when collapsed", "sc_show_unread_cllapsed_in_menu": "Show Unread Count Only when Collapsed", "sc_show_unread_collapsed": "unread count only when collapsed", "sc_top_session": "Pin to Top", "sc_un_label": "Unlabeled", "screen_shot_out_of_memory": "<PERSON><PERSON><PERSON> is using a large amount of your device's memory. Using <PERSON><PERSON><PERSON>'s screenshot tool may cause lag. If you experience lag, please use the system's screenshot tool instead.", "search_all": "Messages", "search_before_months_failed": "Failed to search for records from {{months}} months ago", "search_before_months_loading": "Searching for records from {{months}} months ago", "search_chat_record": "Chat History", "search_citadel": "Citadel", "search_document": "Files", "search_enter_keywords": "Search", "search_failed": "Search failed", "search_image": "Images", "search_in_the_citadel": "Search", "search_invalid_link": "Invalid link", "search_join_public_group_success": "Successfully joined the {{groupName}} group", "search_link": "Links", "search_matching_record_not_found": "No matching chat history found", "search_near_months_failed": "Failed to search records for the last  {{months}} month", "search_near_months_loading": "Searching for records in the last {{months}} month", "search_no_more_data": "No more", "search_no_network": "Network disconnected", "search_no_search_result": "No results found", "search_one_result_only": "Only 1 search result", "search_other": "Other", "search_result_count": "[{{msgNum}} messages]", "search_result_only": "{{searchResult}} search results", "search_select_max_selected_other": "Choose up to {{count}} results at most", "search_select_not_found": "{{name}} not found", "search_session_messages": "Search messages", "search_use_ai": "Try searching \"{{keyword}}\" with AI", "search_vote": "Polls", "secret_chat_1_day": "1 day", "secret_chat_1_minute": "1 minute", "secret_chat_day": "days", "secret_chat_hour": "hour", "secret_chat_minute": "minutes", "secret_create_group_limit": "Incognito group can't exceed {{limit}} members\n", "secret_i_know": "Got it", "security_level_is_set_failed": "Modification failed", "security_level_is_set_successfully": "Modified successfully", "see_more": "View More", "selected_members": "Selected", "selected_number": "{{number}} item(s) selected", "selection_recommend": "Rcmd", "send_km_document": "Select Docs", "send_message": "Send", "send_message_empty": "The sent message can't be empty", "send_message_full_screen": "Enter: New Line / Send Button: Send", "send_message_is_empty": "The sent message can't be empty", "send_message_length_limit": "Content too long, please send in parts.", "send_message_only_text": "Text messages only", "send_message_shortcut": "Command + Enter: New Line | Enter: Send", "send_message_shortcut_mac": "Command + Enter: New Line｜Enter: Send", "send_message_shortcut_tips": "{{newLine}}: New Line | {{send}}: Send", "send_message_shortcut_windows": "Ctrl + Enter: New Line | Enter: Send", "send_message_to": "Send to: ", "session_filter_manage": "Edit Filters", "session_filter_order": "Sort", "session_filter_sorting_visibility": "Sort & Visibility", "session_not_found": "Conversation not found", "session_pannel_update_link_text": "update", "session_pannel_update_tip": "For the latest version, click to {{link}}", "setting_panel_1000_help_desk": "1000 Help Center", "setting_panel_6000_help_desk": "Help Center", "setting_panel_about_tab": "About Daxiang", "setting_panel_add_10_replies_at_most": "Add up to 10 messages", "setting_panel_add_failed": "Failed", "setting_panel_add_frequent_reply": "Add Common Phrases", "setting_panel_add_keywords": "Add", "setting_panel_add_success": "Success", "setting_panel_all": "All", "setting_panel_all_group_at_all": "In any group", "setting_panel_all_notify": "Banners", "setting_panel_all_notify_disable_failed": "Failed to turn off new message notifications", "setting_panel_all_notify_disable_successful": "Success", "setting_panel_all_notify_enable_failed": "Failed to enable new message notifications", "setting_panel_all_notify_enable_successful": "Success", "setting_panel_already_showed_all": "That's all for now", "setting_panel_auto_clean": "Automatic cleaning", "setting_panel_auto_translate_session_setting": "Conversations with custom ", "setting_panel_auto_translate_tips": "When enabled, all text content of incoming messages will be auto-translated in conversations.  ", "setting_panel_auto_translate_tips_2": "auto-translation settings are not affected.", "setting_panel_autostart_disabled_failed": "Failed to disable auto startup.", "setting_panel_autostart_disabled_success": "Success", "setting_panel_autostart_enabled_failed": "Failed to enable auto start on boot.", "setting_panel_autostart_enabled_success": "Success", "setting_panel_bubble_chat_style": "Left and right", "setting_panel_cannot_enable_message_notification": "New message notifications are set to off. Please enable notifications in settings.", "setting_panel_change_languge_tips": "Daxia<PERSON> will need to restart after you switch the language", "setting_panel_chat_style_setting_success": "{{modeText}} set successfully", "setting_panel_chat_window_style": "Message Alignment", "setting_panel_check_update": "Check for updates", "setting_panel_check_update_now": "Upgrade now", "setting_panel_clean_helper_cancel": "Cancel", "setting_panel_clean_helper_content": "Clear local storage data to free up more storage space", "setting_panel_clean_helper_ok": "Start cleaning", "setting_panel_clean_helper_start": "Cleaning in progress...", "setting_panel_clean_helper_success": "Clean up successful", "setting_panel_clean_helper_title": "Cleaner Assistant", "setting_panel_cleanup_settings": "Cleanup Settings", "setting_panel_click_to_diagnose": "Problem Diagnosis", "setting_panel_close_navigation_tab_notify": "Notification for \"{{name}}\" has been closed", "setting_panel_confirm_restart_after_switch": "Settings will take effect after restarting Daxiang, sure you want to restart immediately?", "setting_panel_current_version": "Version", "setting_panel_custom_or_all_only_hide_one": "Only one of 'All' or 'Labels' can be hidden.", "setting_panel_deal_with_later": "Later", "setting_panel_debug_mode": "Debug mode", "setting_panel_debug_mode_turned_off": "Debug mode is off", "setting_panel_debug_mode_turned_on": "Debug mode is on", "setting_panel_delete_failed": "Delete failed", "setting_panel_delete_success": "Deleted", "setting_panel_desktop_shortcut": "Desktop shortcut", "setting_panel_desktop_shortcut_create_failed": "Creation failed", "setting_panel_desktop_shortcut_create_success": "Created successfully", "setting_panel_device_memory_or_cpu_usage": "Device memory/CPU utilization", "setting_panel_disable_gpu": "Turn off GPU acceleration", "setting_panel_disable_gpu_hint": "If there is any page display abnormality, please try to turn off GPU acceleration and restart Daxiang", "setting_panel_discover_problems": "Problem Check", "setting_panel_download": "Download", "setting_panel_download_dx_mobile_app": "Download Mobile App", "setting_panel_dx_laboratory_description": "Here are the latest features of Daxiang, you can choose whether to use or not, in the experimental features may end at any time", "setting_panel_dx_laboratory_title": "Daxiang Lab", "setting_panel_editor_translate_language": "Default Target Language", "setting_panel_editor_translate_usage": "De<PERSON><PERSON>e Option for Translations", "setting_panel_employee_help": "Help Center", "setting_panel_employee_help_desk": "Help Center", "setting_panel_enable_auto_start_after_boot": "Enable auto-start on boot up", "setting_panel_enable_frequent_reply": "Enable Common Phrases", "setting_panel_enable_frequent_reply_hint": "Common Phrases is located in the chat toolbar when enabled", "setting_panel_feedback": "<PERSON><PERSON><PERSON>", "setting_panel_filter_save_failed": "Failed, please try again later", "setting_panel_filter_save_successful": "Successfully saved", "setting_panel_frequent_reply": "Common Phrases", "setting_panel_frequent_reply_input_placeholder": "Add text...", "setting_panel_frequent_reply_tab": "Common Phrases", "setting_panel_frequent_reply_title": "Common Phrases", "setting_panel_general_tab": "General", "setting_panel_get_latest_client": "Latest Daxiang configuration", "setting_panel_gpu_acceleration_disabled": "GPU acceleration has been turned off", "setting_panel_gpu_acceleration_enabled": "GPU acceleration has been enabled", "setting_panel_group_chat": "Groups", "setting_panel_hide": "<PERSON>de", "setting_panel_hotkeys_input_placeholder": "Please define shortcuts", "setting_panel_hotkeys_message_send": "{{sendKey}} Send / {{lineBreakKey}} New Line", "setting_panel_hotkeys_tab": "Shortcuts", "setting_panel_hotkeys_title": "Shortcuts", "setting_panel_how_to_use": "User Guide", "setting_panel_important_message": "Priority Messages", "setting_panel_important_message_hint": "Including messages @Me, @All, and messages containing keywords", "setting_panel_important_message_setting": "Priority Message Settings", "setting_panel_important_message_setting_hint": "When enabled, new messages that meet the criteria will be highlighted with ⚡️ notifications.", "setting_panel_important_session": "Priority Conversations", "setting_panel_important_session_hint": "Including setting up important DMs, group chats, official accounts, and starred contacts", "setting_panel_important_session_message_notification": "Priority Conversations and Messages", "setting_panel_important_session_setting": "Priority Conversation Settings", "setting_panel_important_session_setting_hint": "When you receive new messages from priority conversations marked by opened settings and active tags, you will be notified with an important ⚡️ alert.", "setting_panel_keywords_add": "Add", "setting_panel_keywords_adding": "Adding", "setting_panel_keywords_input_placeholder": "Enter keywords, {{min}}-{{max}} characters", "setting_panel_keywords_length_limit": "The keyword is only allowed {{min}}-{{max}} characters", "setting_panel_keywords_max_count_limit": "Up to {{num}} keywords are allowed to be added", "setting_panel_keywords_repeat": "The keyword is repeated", "setting_panel_keywords_setting": "Keyword Settings", "setting_panel_lab_markdown_support_off_description": "When enabled, messages support Markdown-like syntax (including bold, italic, strikethrough, inline code, etc.).", "setting_panel_lab_markdown_support_on_description": "When enabled, messages support Markdown-like syntax (including bold, italic, strikethrough, inline code, etc.).", "setting_panel_lab_mt_wiki_off_description": "When enabled, the page will automatically prompt for Meituanpedia when displaying content.", "setting_panel_lab_mt_wiki_on_description": "When enabled, the page will automatically prompt for Meituanpedia when displaying content.", "setting_panel_lab_new_screenshot_off_description": "More features than the old screenshot tool", "setting_panel_lab_new_screenshot_on_description": "More features than the old screenshot tool", "setting_panel_lab_priority_off_description": "When enabled, if messages/chats hit certain keywords or @ rules, a special ⚡️ icon will be displayed.", "setting_panel_lab_priority_on_description": "When enabled, if messages/chats hit certain keywords or @ rules, a special ⚡️ icon will be displayed.", "setting_panel_lab_timezone_description": "In cross-timezone communication, others can see your timezone in chats, calendars, etc. You can choose to hide it in the settings below.", "setting_panel_lab_timezone_none": "Hide time zone", "setting_panel_lab_timezone_show": "Show time zone", "setting_panel_label_sort": "Sort Label", "setting_panel_laboratory_markdown_support": "Partial Markdown Supported", "setting_panel_laboratory_mt_wiki": "Meituanpedia", "setting_panel_laboratory_new_screenshot": "New Screenshot Tool", "setting_panel_laboratory_priority_title": "Priority Notifications", "setting_panel_laboratory_tab": "Lab", "setting_panel_language_and_timezone_settings": "Language & Time Zone", "setting_panel_language_auto_translate": "Auto-translate incoming messages", "setting_panel_language_display_language": "Display as", "setting_panel_language_editor_translate": "Translate as You Type", "setting_panel_language_editor_translate_title_tips": "Default settings apply to all conversations, unless overridden by the customized settings in an individual conversation.", "setting_panel_language_setting": "Language Settings", "setting_panel_language_settings": "Language", "setting_panel_language_tab": "Language", "setting_panel_language_translate_message": "Translate Incoming Messages", "setting_panel_language_translate_the_content_into": "Translate into", "setting_panel_last_login": "Last login time", "setting_panel_later_order_update_failed": "Failed to update the \"Later\" list sorting.", "setting_panel_later_order_update_success": "'Later' list sorting has been set to '{{order}}'.", "setting_panel_leader": "Leader", "setting_panel_leader_notification_hint": "When enabled, messages sent to you via direct message from your leader will be highlighted as important ⚡️ reminders.", "setting_panel_left_aligned_chat_style": "Left", "setting_panel_logout": "Log Out", "setting_panel_manage": "Edit Filters", "setting_panel_message_at_all": "@All mentions", "setting_panel_message_at_me": "Direct mentions", "setting_panel_message_containing_keywords": "Messages with keywords", "setting_panel_message_sound_notify": "Sound", "setting_panel_modify_password": "Change Password", "setting_panel_nav_button": "Manage Navbar", "setting_panel_nav_tab": "Navigation Bar", "setting_panel_nav_text": "Set which applications to show and in which order", "setting_panel_navigation_app_chat": "Chats", "setting_panel_navigation_app_chat_hint": "Chat notifications cannot be disabled", "setting_panel_navigation_app_notification_sub_title": "Only available to apps with access to notifications", "setting_panel_navigation_app_notification_title": "Notification for apps in navbar", "setting_panel_new_version": "Discover new version", "setting_panel_new_version_last": "Latest version already installed", "setting_panel_notification_tab": "Notifications", "setting_panel_notification_tab_title": "Notifications", "setting_panel_notifications_after_performance_warning_turn_on": "Once turned on, <PERSON><PERSON><PERSON> will send you notifications when memory usage is too high as well as when your device's memory and CPU usage is too high", "setting_panel_notify_me_when_receive_messages_below": "Notify me about", "setting_panel_official_account": "Official Accounts", "setting_panel_one_click_create": "One-click creation", "setting_panel_only_enable_group_at_all": "In unmuted groups", "setting_panel_open_navigation_tab_notify": "Notification for \"{{name}}\" has been enabled", "setting_panel_performance": "Performance", "setting_panel_performance_warning_is_turned_off": "Disabled device availability alerts", "setting_panel_performance_warning_is_turned_on": "Device availability notification has been enabled.", "setting_panel_private_chat": "DMs", "setting_panel_profile_settings": "My Account", "setting_panel_prohibit_disable_notify_at_me": "Notifications for direct mentions cannot be disabled.", "setting_panel_prohibit_to_disable_keyword_messages_notification": "Notifications for messages with keywords cannot be disabled.", "setting_panel_self_startup_settings": "Auto Start Settings", "setting_panel_session_filter_tab": "Filter", "setting_panel_session_filter_title": "Filter", "setting_panel_session_no_hide_data": "No hidden item", "setting_panel_session_style_tab": "Appearance", "setting_panel_setting_title": "Settings", "setting_panel_setup_failed": "Setup Failure", "setting_panel_shortcut_setting_conflict": "This shortcut is already taken", "setting_panel_shortcut_setting_conflict_error_details": "Shortcut key already in use, {{hotkey}} setup failed", "setting_panel_shortcut_setting_error": "Shortcut key setup failed", "setting_panel_show": "Show", "setting_panel_show_clean_helper": "Use cleaning assistant", "setting_panel_software_update_title": "Software Update", "setting_panel_sort": "Edit", "setting_panel_sound_notification_disable_failed": "Failed to turn off sound alerts", "setting_panel_sound_notification_disable_successful": "Sound alert turned off successfully", "setting_panel_sound_notification_enable_failed": "Failed to turn on sound alerts", "setting_panel_sound_notification_enable_successful": "Sound alert enabled successfully", "setting_panel_special_notification_set_successfully": "Set successfully", "setting_panel_special_notify_me_setup_failed": "Failed", "setting_panel_special_notify_me_setup_success": "Set successfully", "setting_panel_star_contacts": "Starred Contacts", "setting_panel_star_contacts_notification_hint": "When enabled, messages sent to you via direct message from starred contacts will be highlighted as important ⚡️ reminders.", "setting_panel_start_diagnose": "Diagnosis", "setting_panel_support_android_or_ios": "Support for Android/iOS", "setting_panel_switch_tabs_hot_key_title": "Jump to Specific Tab", "setting_panel_switch_tabs_hot_key_to": "to", "setting_panel_system_notification": "System Notifications", "setting_panel_system_notification_hint": "When system notifications are enabled, you can customise new message notifications via the methods below:", "setting_panel_these_message_important_to_you": "New messages of this type will now be highlighted with ⚡️ notifications.", "setting_panel_these_message_not_important_to_you": "New messages of this type will no longer be highlighted with ⚡️ notifications.", "setting_panel_these_message_will_be_important": "New messages of this type will now be highlighted with ⚡️ notifications.", "setting_panel_these_message_will_not_be_important": "New messages of this type will no longer be highlighted with ⚡️ notifications.", "setting_panel_timezone": "Time Zone Display", "setting_panel_timezone_setting": "Time Zone Settings", "setting_panel_turn_on_performance_warning": "Enable device performance availability reminder", "setting_panel_view_keywords": "View Keywords", "share_fail": "{{share_status}} failed", "share_success": "{{share_status}} successful", "share_their_schedule_with_you": "share the calendar with you", "side_panel_download_fail": "Download failed.", "side_panel_download_start": "Downloading...", "side_panel_download_success": "Downloaded", "side_panel_loading_timeout": "Loading timeout", "side_panel_not_support_file_preview": "This file does not support preview", "side_panel_record_link_search_error": "Network or server error, please try again later.", "someone_at_all": "{{name}}@All", "someone_at_you": "{{name}} mentioned you", "someone_initiated_the_schedule": "Schedule initiated by {{name}}", "ss_search_placeholder": "Search for people/groups/public accounts.", "startChat_add_member_successfully": "Added", "startChat_create_group_successfully": "Successfully created group", "startChat_custom_group_name": "Label Name", "startChat_custom_group_name_placeholder": "Up to 12 characters. If left blank, the default group name will be used.", "startChat_group_name_already_exists": "Label name already exists!", "startChat_group_name_cannot_be_all_spaces": "Label name cannot be all spaces!", "startChat_group_name_cannot_exceed_12_words": "Label name cannot exceed 12 characters!", "startchat_add_members_batch": "Batch Import", "startchat_all_messages_cannot_be_forwarded_and_copied": ". Messages can't be forwarded or copied.", "startchat_contacts": "Contacts", "startchat_create_groups_quickly": "Quick Create", "startchat_create_secret_group_chat": "Incog<PERSON><PERSON>", "startchat_cross_enterprise_group": "External Group", "startchat_forward_to_open_group_requires_secondary_confirmation": "· To forward internal messages, files, contact cards, events, etc. to an external group, a second confirmation is required.", "startchat_group_chat_name": "Name", "startchat_group_members_cannot_be_empty": "The group members can't be empty", "startchat_group_messages_and_files_unaffected_by_company": "· Group chat history and files remain unaffected when a member's company changes.", "startchat_group_messages_can_view_after_leaving": "· After leaving the company, one can still view group chat history and files.", "startchat_group_name_total_other": "Group name can't be longer than {{count}} characters.", "startchat_group_type": "Group Type", "startchat_mis_group_mast_input_tip": "Support two input methods: \n1. Separate MIS with English comma, e.g. pangxiaoting02,dongying05 \n2. Import Mis with newline, such as \npangxiaoting02 \ndongying05", "startchat_mis_group_must_input_tip": "Required field: Up to 500 people can be added at once. Two input methods are supported:\n1. Separate member MIS IDs with commas, e.g., pangxiaoting02,dongying05\n2. Use line breaks for member MIS IDs, e.g.,\npangxiaoting02\ndongying05\nWhen adding members, please note the remaining capacity of the group (default limit is 500 people; if expanded, follow the expanded limit).", "startchat_mis_group_option_input_tip": "Use English comma to separate(up to 3 people)(optional)", "startchat_nonCrossEnterprise_groups_do_not_support_adding_nonEnterprise_contacts_please_remove_nonEnterprise_contacts": "General groups don't support adding external contacts from other companies. Please remove the external contacts.", "startchat_normal_group_chat": "General Group", "startchat_open_groups_can_add_external_contacts": "· External groups can include contacts from other companies.", "startchat_orgTree_no_data": "No data", "startchat_organization": "Organization", "startchat_recently_contact": "Recent Contacts", "startchat_screenshot_recording_function_will_be_partially_restricted": ". Screenshot and screen recording features will be partially restricted.", "startchat_search_placeholder": "User name, account, or phone number", "startchat_select_member": "Select Members", "startchat_selected_limit": "You can only select up to {{limit}}", "startchat_selection_mode": "Selection Type", "startchat_set_group_name_for_followup_search": "Enter group name (optional)", "startchat_the_message_is_automatically_destroyed_after_it_has_been_read_and_the_destruction_time_limit_can_be_manually_set": ". Messages will be automatically destroyed after being read and can be set with a time limit.", "startchat_the_messages_in_the_group_will_be_destroyed_after_7_days_regardless_of_whether_they_have_been_read_or_not": ". Whether read or not, they will be destroyed after 7 days.", "suggest_add_group_error": "Failed", "supports_formats_tip": "Only supports jpg/png/gif formats", "text_is_copied_to_clipboard_successfully": "<PERSON>pied", "textarea_can_nott_empty": "{{name}} can‘t be empty", "textarea_exceed_limit": "{{name}} can't be more than {{maxLength}} characters", "timezone_diff_hours": "Local time: {{time}} ({{sign}}{{hours}}h from your time)", "timezone_diff_hours_minutes": "Local time: {{time}} ({{sign}}{{hours}}h {{minutes}}min from your time)", "timezone_diff_minutes": "Local time: {{time}} ({{sign}}{{minutes}}min from your time)", "timezone_diff_zero": "Local time: {{time}} (+0h from your time)", "timezone_local_time_hours": "Local time: {{time}} ({{sign}}{{hours}}h from your time)", "timezone_local_time_hours_minutes": "Local time: {{time}} ({{sign}}{{hours}}h {{minutes}}min from your time)", "timezone_local_time_minutes": "Local time: {{time}} ({{sign}}{{minutes}}min from your time)", "timezone_local_time_zero": "Local time: {{time}} (+0h from your time)", "top_session_scroll_guide": "Scroll down to view more.", "traceless_all_read": "All Read", "traceless_destoryed_in_days": "Expires in {{day}}d", "traceless_destoryed_in_days_and_hours": "Expires in {{day}}d {{hour}}h", "traceless_destoryed_in_hours": "Expires in {{hour}}h", "traceless_destoryed_in_hours_and_minutes": "Expires in {{hour}}h {{min}}m", "traceless_destoryed_in_minutes": "Expires in {{min}}m", "traceless_destroyed_in_less_than_a_minute": "Expires in less than 1m", "traceless_everybody_read": "All Read", "traceless_nobody_read": "None Read", "traceless_unread_count": "{{unreadCount}} Unread", "translate_fail": "Translation failed. ", "translate_loading": "Translating...", "translate_to_text_provider": "Provided translation support by <PERSON><PERSON><PERSON>.", "unLabel_limit_sessions": "Unlabeled conversations display latest {{limit}} only", "uni_action_merge_record": "Operation not support currently", "uni_action_merge_record_warn": "Cards in combined forwards cannot be interacted with.", "uni_card_button_submit": "Submit", "uni_card_feedback_negative_messagebox_input_content": "Please comment on other unsatisfaction", "uni_card_feedback_negative_messagebox_title": "Express your unsatisfactions", "uni_card_feedback_positive_messagebox_input_content": "Please comment on other satisfaction", "uni_card_feedback_positive_messagebox_input_tooLong": "The number of words could not exceed {count} words", "uni_card_feedback_positive_messagebox_title": "Give your appreciations", "uni_card_image_loading_failed": "Image failed to load", "uni_card_interactiveProtocol_sendMessage_callUser_bot_planB": "@DaxiangRobot", "uni_card_interactiveProtocol_sendMessage_callUser_people_planB": "@User", "uni_card_interactiveProtocol_sendMessage_failedtoObtainGroupPermissions": "Failed to obtain the group permissions. Please try again later.", "uni_card_interactiveProtocol_sendMessage_groupisBannedAtAllFromMembers": "Operation failed, the group admin did not allow members to @all.", "uni_card_interactiveProtocol_sendMessage_groupisBannedSpeaking": "Failed. Only group admins are allowed to send messages in this group.", "uni_card_interactiveProtocol_sendMessage_onlyCanBeUsediInConversationBubble": "Only support performing this operation within the chat.", "uni_card_interactiveProtocol_sendMessage_onlyCanBeUsediInOriginalConversation": "Please try this on the original message.", "uni_card_interactiveProtocol_sendMessage_unsupportedCoversationType": "The operation is currently not supported within the current chat type.", "uni_card_msg_load_fail": "New card version released", "uni_card_msg_load_fail_retry": "<PERSON>ad failed, please try again.", "uni_card_msg_prefix": "[Card]", "uni_card_msg_quoted": "[Card]", "uni_card_msg_relaunch_dx": "<PERSON><PERSON>", "uni_card_msg_relaunch_dx_enable": "Restart <PERSON> to view this message.", "uni_card_msg_update_dx": "Upgrade Daxiang", "uni_card_msg_update_dx_enable": "Upgrade <PERSON>xiang to view this message.", "uni_card_msg_update_dx_version": "Please upgrade to the new version to view this message.", "uni_card_recommendQues_onlyCanBeUsediInConversationBubble": "Only support performing this operation within the chat.", "uni_card_recommendQues_onlyCanBeUsediInOriginalConversation": "Please try this on the original message.", "uni_card_runtime_request_expired": "The card has expired", "uni_card_runtime_request_failed": "Network error, please try again later.", "uni_card_runtime_request_success": "Operation successful", "uni_card_summary_chart": "[Chart]", "uni_card_summary_default": "[Card] ", "uni_card_summary_err": "[Card] Card load failed", "uni_card_summary_image": "[Image]", "uni_card_summary_prefix": "[Card] ", "uni_card_summary_table_invalid": "The format of table values was incorrect, loading failed", "uni_card_summary_update": "[Card] Please upgrade to the new card version", "uni_router_network_error": "Network or service exception, please try again later", "unicard_action_collect_record_warn": "Cards in favorites cannot be interacted with.", "unicard_action_mark_p1_record_warn": "Cards in Mark cannot be interacted with. Click to ", "unicard_action_mark_p2_record_warn": "View ", "unicard_action_mark_p3_record_warn": "and act in the original.", "unicard_cantforward": "This card cannot be forwarded.", "unicard_card_messages_are_currently_not_supported_for_bookmarking": "Card messages can't be favorited for now.", "unicard_due_to_business_requirements_bizinfo_cards_are_temporarily_not_supported_for_forwarding": "{bizInfo} has not enabled forwarding for this card.", "unicard_due_to_business_requirements_xxxx_cards_are_temporarily_not_supported_for_forwarding": "{{xxxx}} has not enabled forwarding for this card.", "unicard_up_to_count_card_messages_can_be_forwarded_please_try_again": "You can forward up to {{count}} cards at a time. Please reselect.", "unicard_up_to_count_card_messages_can_be_forwarded_please_try_again_other": "You can forward up to {{count}} cards at a time. Please reselect.", "update_abort_error": "Update termination encountered an error.", "update_checkForUpdates_body": "Detected legacy Daxiang Next installation files, administrator privileges are required to delete them in order to upgrade to the latest version", "update_checkForUpdates_title": "Click to request Daxiang update permission", "update_download_error": "Failed to download the update package, please check the stability of the network.", "update_error": "Update process encountered an exception.", "update_framework_error": "Encountered error when updating the framework.", "update_nav_setting_failed": "Save failed, please try again later", "update_new_version_last": "You're on the latest version", "update_not_available": "There are currently no available updates.", "update_notice": "Daxiang Next-{{__VERSION__}} is no longer maintained, click Check for Updates to upgrade to the latest version, or visit {{link}} to update manually", "update_notice_link_text": "Link", "update_notice_title": "Upgrade Announcement", "update_permission_denied": "Daxiang encountered permission issues during the update. It is recommended that you download the latest version of Daxiang and reinstall it to solve the problem.", "update_prompt_status": "New", "update_remind_later": "Later", "update_request_gray_error": "Grayscale policy check failed", "update_unknown_error": "An unknown error occurred, please try again later.", "user_not_found": "User not found", "vcard_account_id": "Account", "vcard_add_contacts": "Add Contacts", "vcard_add_friend_failed": "Failed to add", "vcard_add_friend_success": "Added", "vcard_add_friend_to_limit": "Adding failed. You have reached the daily limit for adding friends", "vcard_add_star_contacts": "Add to Starred Contacts", "vcard_add_star_failed": "Add to starred contacts failed!", "vcard_add_star_success": "Add to starred contacts successfully!", "vcard_administrator": "Admin(s)", "vcard_ai_staff": "AI Staff", "vcard_cancel_star_contacts": "Remove Starred Contacts", "vcard_cannot_start_secret_chat": "Failed to start incognito chat", "vcard_cannot_view_information": "No Access", "vcard_click_to_try_again": "Please try again.", "vcard_company": "Company", "vcard_confirm_to_delete_contact": "Once you delete this contact, they will no longer be able to communicate with you", "vcard_contact_added": "Successfully added", "vcard_copy": "Copy", "vcard_copy_group_link": "Copy Group Link", "vcard_copy_group_link_text": "Click {{groupLink}} to join the {{name}} Daxiang Group.", "vcard_delete_contacts": "Delete Contacts", "vcard_delete_friend_failed": "Delete failed!", "vcard_delete_friend_success": "Deleted", "vcard_department": "Dept.", "vcard_developer": "Developer", "vcard_dotted_line_leader": "Dotted-line Leader", "vcard_download_group_qr_code": "Download QR Code", "vcard_empty_group_info": "No group description", "vcard_empty_information": "Not filled in", "vcard_empty_person_signature": "Say something about yourself...", "vcard_failed_to_get_qr_code": "Failed to get the QR code.", "vcard_failed_to_modify_group_introductions": "Failed to modify group description!", "vcard_failed_to_modify_personal_signature": "Failed", "vcard_forbid_appeal_failed": "Appeal failed", "vcard_forbid_appeal_success": "Submission successful", "vcard_forbid_default_title": "Reminder", "vcard_forbid_please_input_appeal_reason": "Please provide a detailed reason for your appeal (required).", "vcard_forbid_reason_is_required": "Please provide a detailed reason for your appeal .", "vcard_forbid_words_less_than_fifty": "The number of words should not exceed 50.", "vcard_forbidden_view": "No viewing permission", "vcard_group_introduction": "Group Description", "vcard_group_introduction_exceed_limit": "Group description cannot exceed {{maxLength}} characters", "vcard_group_is_full": "Full", "vcard_group_is_full_toast": "Group is full, unable to join", "vcard_group_name_length_limit": "Group name can't be longer than 100 characters.", "vcard_group_qr_code": "Group QR Code", "vcard_has_been_sent": "<PERSON><PERSON>", "vcard_help_document": "Help Docs", "vcard_info_operation_failed": "Failed", "vcard_info_successful_operation": "Success", "vcard_join_in_group": "Join", "vcard_join_in_group_fulled": "Join(Full)", "vcard_leader": "Leader", "vcard_mail": "Email", "vcard_moderator_and_administrator": "Group Owner/Admin(s)", "vcard_modify_group_introduce_success": "Success", "vcard_modify_personal_signature_successfully": "Success", "vcard_more_operations": "More", "vcard_name": "Name", "vcard_nick_exceed_limit": "<PERSON><PERSON> can't be longer than {{maxLength}} characters.", "vcard_nick_name": "<PERSON><PERSON>", "vcard_no_permission_for_organization": "No permission to view this organization", "vcard_no_sensitive_information": "Avoid sensitive info such as phone number", "vcard_organization_profile": "View organization profile", "vcard_permission_denied": "No permission!", "vcard_person_introduction_exceed_limit": "The text can not exceed {{maxLength}} characters", "vcard_person_signature_exceed_limit": "Max {{signatureLimit}} chars for text, {{signatureLink}} chars for links total. Adjust to save.", "vcard_person_signature_link_exceed_limit": "The link address can't exceed {{signatureLink}} characters", "vcard_person_signature_reminder": "Include the hyperlink in the [link text|link address] format, and the link address not counted", "vcard_person_signature_text_exceed_limit": "The maximum length for the signature is {{signatureLimit}}  characters.", "vcard_please_add_contact_then_communicate": "This person isn't in your contacts. Add to chat.", "vcard_position": "Location", "vcard_qr_download_failed": "QR code download failed.", "vcard_remove_from_group_list": "Remove from Groups", "vcard_remove_star_failed": "Failed to remove the star", "vcard_remove_star_success": "Star deletion successful!", "vcard_save_to_group_list": "Save to My Groups", "vcard_send_mail": "Email", "vcard_send_message": "Message", "vcard_send_messages": "Message", "vcard_set_up_failed": "Failed", "vcard_set_up_successfully": "Successfully", "vcard_share_card": "Share Card", "vcard_share_contacts": "Share Contact", "vcard_share_group_vcard": "Share Group", "vcard_share_vcard": "Share Contact", "vcard_signature": "Bio", "vcard_start_secret_chat": "Incog<PERSON><PERSON>", "vcard_system_acount": "System Account", "vcard_tel": "Tel", "vcard_this_company_is_disabled": "The enterprise is prohibited", "vcard_uid_in_parameter_is_empty": "Pass in parameter props.vcard.uid as null!", "vcard_unfold_org": "Show more", "vcard_view": "Click to view", "vcard_view_document": "View", "version": 740, "video_preview": "Video Preview ", "voice_to_text": "Convert to Text", "voice_to_text_copy_done": "<PERSON>pied", "voice_to_text_copy_error": "<PERSON><PERSON> failed, please try again later.", "voice_to_text_done": "Conversion completed", "voice_to_text_empty": "No valid text recognized.", "voice_to_text_fail": "Failed", "voice_to_text_fold": "Collapse", "voice_to_text_provider": "Powered by <PERSON><PERSON><PERSON>", "win_editor_enter_fullscreen": "Full Screen (Ctrl+S)", "within_30_days": "Last 30 days", "within_3_days": "Last 3 days", "within_7_days": "Last 7 days", "workbench_add_my_app_success": "Added", "workbench_add_nav_app_success": "Added", "workbench_administrative": "Admin", "workbench_all_apps_title": "Apps", "workbench_company_affairs": "Corporate", "workbench_data_monitoring": "Data", "workbench_delete_my_app_success": "Removed", "workbench_delete_nav_app_success": "Removed", "workbench_department_business": "Operations", "workbench_fetch_failed_msg": "Request failed, please try again later.", "workbench_financial_reimbursement": "Finance", "workbench_human_resource": "HR", "workbench_it_office": "IT", "workbench_knowledge_document": "Docs", "workbench_main": "Workplace", "workbench_menu_add_my_app": "Add to My Apps", "workbench_menu_add_nav_app": "Add to Navbar", "workbench_menu_delete_my_app": "Remove from My Apps", "workbench_menu_delete_nav_app": "Remove from Navbar", "workbench_my_apps_add_apps": "Add from all apps below", "workbench_my_apps_done": "Done", "workbench_my_apps_edit": "Edit", "workbench_my_apps_no_apps": "No apps added. Add now", "workbench_my_apps_tips": "(Drag to sort)", "workbench_my_apps_title": "Favorite Apps", "workbench_other": "Other", "workbench_reload_apps": "Reload", "workbench_search_error_retry_please": "Request failed, please try again later.", "workbench_search_input_placeholder": "Search", "workbench_search_loading": "Loading", "workbench_search_no_result": "No results found", "xmapp_diagnose_user_center": "Help Center", "xmapp_vpn_user_center": "Help Center", "you_have_no_new_apply": "No contacts request", "you_have_not_saved_any_friends": "No contacts", "you_have_not_saved_any_groups": "No groups", "yp_add_group_space": "Add Common Groups"}