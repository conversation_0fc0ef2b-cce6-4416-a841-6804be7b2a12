@font-face {font-family: "dxicon3";
  src: url('./dxicon3.baseline.eot?t=1756192259195'); /* IE9*/
  src: url('./dxicon3.baseline.eot?t=1756192259195#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('./dxicon3.baseline.woff2?t=1756192259195') format('woff2'), /* chrome、firefox; better compression */
  url('./dxicon3.baseline.woff?t=1756192259195') format('woff'), /* chrome, firefox */
  url('./dxicon3.baseline.ttf?t=1756192259195') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('./dxicon3.baseline.svg?t=1756192259195#dxicon3') format('svg'); /* iOS 4.1- */
}

.dxicon3 {
  font-family: "dxicon3" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dxicon3-calendar-data-fill:before { content: "\e000"; }
.dxicon3-calendar-data:before { content: "\e001"; }
.dxicon3-cloud-disk:before { content: "\e002"; }
.dxicon3-contacts-fill:before { content: "\e003"; }
.dxicon3-contacts:before { content: "\e004"; }
.dxicon3-message-fill:before { content: "\e005"; }
.dxicon3-message:before { content: "\e006"; }
.dxicon3-onestep-vpn:before { content: "\e007"; }
.dxicon3-workbench-fill:before { content: "\e008"; }
.dxicon3-workbench:before { content: "\e009"; }
.dxicon3-zoom:before { content: "\e00a"; }
.dxicon3-cloud-disk-fill:before { content: "\e00b"; }
.dxicon3-zoom-fill:before { content: "\e00c"; }
.dxicon3-add-mini:before { content: "\e00d"; }
.dxicon3-add:before { content: "\e00e"; }
.dxicon3-arrow-left:before { content: "\e00f"; }
.dxicon3-arrow-right:before { content: "\e010"; }
.dxicon3-down-thick:before { content: "\e011"; }
.dxicon3-up-thick:before { content: "\e012"; }
.dxicon3-left-thick:before { content: "\e013"; }
.dxicon3-announcement:before { content: "\e014"; }
.dxicon3-avatar-add:before { content: "\e015"; }
.dxicon3-close-o-fill:before { content: "\e016"; }
.dxicon3-edit:before { content: "\e017"; }
.dxicon3-ellipsis:before { content: "\e018"; }
.dxicon3-file-send:before { content: "\e019"; }
.dxicon3-folder:before { content: "\e01a"; }
.dxicon3-mute:before { content: "\e01b"; }
.dxicon3-record-search:before { content: "\e01c"; }
.dxicon3-reply-quick:before { content: "\e01d"; }
.dxicon3-search:before { content: "\e01e"; }
.dxicon3-setting:before { content: "\e01f"; }
.dxicon3-smily:before { content: "\e020"; }
.dxicon3-vcard:before { content: "\e021"; }
.dxicon3-meeting-h:before { content: "\e022"; }
.dxicon3-close:before { content: "\e023"; }
.dxicon3-right-thick:before { content: "\e024"; }
.dxicon3-browser-mac:before { content: "\e025"; }
.dxicon3-browser-win:before { content: "\e026"; }
.dxicon3-customer:before { content: "\e027"; }
.dxicon3-remove-workbench:before { content: "\e028"; }
.dxicon3-add-workbench:before { content: "\e029"; }
.dxicon3-refresh:before { content: "\e02a"; }
.dxicon3-close-mini:before { content: "\e02b"; }
.dxicon3-remove:before { content: "\e02c"; }
.dxicon3-group-outside:before { content: "\e02d"; }
.dxicon3-oneboat:before { content: "\e02e"; }
.dxicon3-oneboat-fill:before { content: "\e02f"; }
.dxicon3-customer-w-fill:before { content: "\e030"; }
.dxicon3-customer-w:before { content: "\e031"; }
.dxicon3-c-service-fill:before { content: "\e032"; }
.dxicon3-c-service:before { content: "\e033"; }
.dxicon3-airplayvideo:before { content: "\e034"; }
.dxicon3-receipt:before { content: "\e035"; }
.dxicon3-avatar-group-fill:before { content: "\e036"; }
.dxicon3-avatar-fill:before { content: "\e037"; }
.dxicon3-avatar-addnew-fill:before { content: "\e038"; }
.dxicon3-open-group-fill:before { content: "\e039"; }
.dxicon3-hierarchy-fill:before { content: "\e03a"; }
.dxicon3-floor-fill:before { content: "\e03b"; }
.dxicon3-record-screen:before { content: "\e03c"; }
.dxicon3-screenshot:before { content: "\e03d"; }
.dxicon3-icon-department:before { content: "\e03e"; }
.dxicon3-icon-open:before { content: "\e03f"; }
.dxicon3-icon-public:before { content: "\e040"; }
.dxicon3-floor:before { content: "\e041"; }
.dxicon3-avatar-group:before { content: "\e042"; }
.dxicon3-forward:before { content: "\e043"; }
.dxicon3-globe:before { content: "\e044"; }
.dxicon3-close_win:before { content: "\e045"; }
.dxicon3-max2mini_win:before { content: "\e046"; }
.dxicon3-min2bar_win:before { content: "\e047"; }
.dxicon3-mini2max_win:before { content: "\e048"; }
.dxicon3-addapp-workbench:before { content: "\e049"; }
.dxicon3-removeapp-workbench:before { content: "\e04a"; }
.dxicon3-turn:before { content: "\e04b"; }
.dxicon3-more-o-fill:before { content: "\e04c"; }
.dxicon3-more-o:before { content: "\e04d"; }
.dxicon3-huxin:before { content: "\e04e"; }
.dxicon3-checkbox-o:before { content: "\e04f"; }
.dxicon3-copy:before { content: "\e050"; }
.dxicon3-like:before { content: "\e051"; }
.dxicon3-download:before { content: "\e052"; }
.dxicon3-redo:before { content: "\e053"; }
.dxicon3-smily-add:before { content: "\e054"; }
.dxicon3-transfer-save:before { content: "\e055"; }
.dxicon3-redo-admin:before { content: "\e056"; }
.dxicon3-all:before { content: "\e057"; }
.dxicon3-bell:before { content: "\e058"; }
.dxicon3-chathistory:before { content: "\e059"; }
.dxicon3-comment:before { content: "\e05a"; }
.dxicon3-file:before { content: "\e05b"; }
.dxicon3-keyboard:before { content: "\e05c"; }
.dxicon3-lab:before { content: "\e05d"; }
.dxicon3-link:before { content: "\e05e"; }
.dxicon3-picture:before { content: "\e05f"; }
.dxicon3-quickrely:before { content: "\e060"; }
.dxicon3-text:before { content: "\e061"; }
.dxicon3-rotate:before { content: "\e062"; }
.dxicon3-zoom_in:before { content: "\e063"; }
.dxicon3-zoom_out:before { content: "\e064"; }
.dxicon3-bell_fill:before { content: "\e065"; }
.dxicon3-independence:before { content: "\e066"; }
.dxicon3-fixed:before { content: "\e067"; }
.dxicon3-untfix:before { content: "\e068"; }
.dxicon3-gif:before { content: "\e069"; }
.dxicon3-view:before { content: "\e06a"; }
.dxicon3-mark:before { content: "\e06b"; }
.dxicon3-unmark:before { content: "\e06c"; }
.dxicon3-hierarchy:before { content: "\e06d"; }
.dxicon3-calendar:before { content: "\e06e"; }
.dxicon3-umeet:before { content: "\e06f"; }
.dxicon3-secretchat:before { content: "\e070"; }
.dxicon3-lock:before { content: "\e071"; }
.dxicon3-destroytime:before { content: "\e072"; }
.dxicon3-chatsetting:before { content: "\e073"; }
.dxicon3-drag:before { content: "\e074"; }
.dxicon3-dropmenucheck:before { content: "\e075"; }
.dxicon3-latercare:before { content: "\e076"; }
.dxicon3-sequence:before { content: "\e077"; }
.dxicon3-meaning:before { content: "\e078"; }
.dxicon3-telephone-fill:before { content: "\e079"; }
.dxicon3-enlarge-bjtp:before { content: "\e07a"; }
.dxicon3-mosaic-bjtp:before { content: "\e07b"; }
.dxicon3-move-bjtp:before { content: "\e07c"; }
.dxicon3-redo-bjtp:before { content: "\e07d"; }
.dxicon3-rotate-bjtp:before { content: "\e07e"; }
.dxicon3-serikeethrough-bjtp:before { content: "\e07f"; }
.dxicon3-smile-bjtp:before { content: "\e080"; }
.dxicon3-tailoring-bjtp:before { content: "\e081"; }
.dxicon3-undo-bjtp:before { content: "\e082"; }
.dxicon3-edit-bjtp:before { content: "\e083"; }
.dxicon3-jiantou-bjtp:before { content: "\e084"; }
.dxicon3-micrify-bjtp:before { content: "\e085"; }
.dxicon3-info:before { content: "\e086"; }
.dxicon3-eraser:before { content: "\e087"; }
.dxicon3-lock-o:before { content: "\e088"; }
.dxicon3-qiehuancaidanlan:before { content: "\e089"; }
.dxicon3-qiehuanshurulan:before { content: "\e08a"; }
.dxicon3-ic_confidential:before { content: "\e08b"; }
.dxicon3-ic_confidential_fill:before { content: "\e08c"; }
.dxicon3-icon-robot:before { content: "\e08d"; }
.dxicon3-icon-robot3x:before { content: "\e08e"; }
.dxicon3-required_fields:before { content: "\e08f"; }
.dxicon3-ic_access_o:before { content: "\e090"; }
.dxicon3-ic_access:before { content: "\e091"; }
.dxicon3-arrow-up:before { content: "\e092"; }
.dxicon3-onlinedoc:before { content: "\e093"; }
.dxicon3-wps-fill:before { content: "\e094"; }
.dxicon3-location-o:before { content: "\e095"; }
.dxicon3-ic_task_outline:before { content: "\e096"; }
.dxicon3-ic_task_fill:before { content: "\e097"; }
.dxicon3-ic_task_outline_update:before { content: "\e098"; }
.dxicon3-task_f_update:before { content: "\e099"; }
.dxicon3-task_normal:before { content: "\e09a"; }
.dxicon3-task_selected:before { content: "\e09b"; }
.dxicon3-shouqi:before { content: "\e09c"; }
.dxicon3-collapse_input:before { content: "\e09d"; }
.dxicon3-collapseinput:before { content: "\e09e"; }
.dxicon3-shandian:before { content: "\e09f"; }
.dxicon3-fullscreen:before { content: "\e0a0"; }
.dxicon3-arrow-down:before { content: "\e0a1"; }
.dxicon3-tasks:before { content: "\e0a2"; }
.dxicon3-avatar-group-y:before { content: "\e0a3"; }
.dxicon3-bell-o:before { content: "\e0a4"; }
.dxicon3-comment-o:before { content: "\e0a5"; }
.dxicon3-delete-o:before { content: "\e0a6"; }
.dxicon3-focus-o:before { content: "\e0a7"; }
.dxicon3-id-card:before { content: "\e0a8"; }
.dxicon3-vertical-up:before { content: "\e0a9"; }
.dxicon3-dialogue-o:before { content: "\e0aa"; }
.dxicon3-shandian-o:before { content: "\e0ab"; }
.dxicon3-check:before { content: "\e0ac"; }
.dxicon3-quxiaozhiding:before { content: "\e0ad"; }
.dxicon3-quxiao-shandian:before { content: "\e0ae"; }
.dxicon3-quxiao-bell-o:before { content: "\e0af"; }
.dxicon3-weidu-o:before { content: "\e0b0"; }
.dxicon3-quxiao-xiaoxizhushou-o:before { content: "\e0b1"; }
.dxicon3-ic_italic:before { content: "\e0b2"; }
.dxicon3-ic_rich_text:before { content: "\e0b3"; }
.dxicon3-ic_underline:before { content: "\e0b4"; }
.dxicon3-processed:before { content: "\e0b5"; }
.dxicon3-ic_bold:before { content: "\e0b6"; }
.dxicon3-filie:before { content: "\e0b7"; }
.dxicon3-add-circle-o:before { content: "\e0b8"; }
.dxicon3-dislike-fill:before { content: "\e0b9"; }
.dxicon3-dislike:before { content: "\e0ba"; }
.dxicon3-fabulous-fill:before { content: "\e0bb"; }
.dxicon3-fabulous:before { content: "\e0bc"; }
.dxicon3-sendplane:before { content: "\e0bd"; }
.dxicon3-beta:before { content: "\e0be"; }
.dxicon3-copy3:before { content: "\e0bf"; }
.dxicon3-refresh3:before { content: "\e0c0"; }
.dxicon3-replace:before { content: "\e0c1"; }
.dxicon3-right-thick-fillet:before { content: "\e0c2"; }
.dxicon3-up:before { content: "\e0c3"; }
.dxicon3-voice:before { content: "\e0c4"; }
.dxicon3-zhuanwenzi:before { content: "\e0c5"; }
.dxicon3-warning-circle-o:before { content: "\e0c6"; }
.dxicon3-add-circle-fill:before { content: "\e0c7"; }
.dxicon3-cancel-circle-fill:before { content: "\e0c8"; }
.dxicon3-icon-vote:before { content: "\e0c9"; }
.dxicon3-left_voice_2:before { content: "\e0ca"; }
.dxicon3-left_voice_0:before { content: "\e0cb"; }
.dxicon3-left_voice_1:before { content: "\e0cc"; }
.dxicon3-right_voice_2:before { content: "\e0cd"; }
.dxicon3-right_voice_0:before { content: "\e0ce"; }
.dxicon3-right_voice_1:before { content: "\e0cf"; }
.dxicon3-vote-o:before { content: "\e0d0"; }
.dxicon3-vote-fill:before { content: "\e0d1"; }
.dxicon3-link2:before { content: "\e0d2"; }
.dxicon3-list-bulleted:before { content: "\e0d3"; }
.dxicon3-list-numbered:before { content: "\e0d4"; }
.dxicon3-bulleted_1:before { content: "\e0d5"; }
.dxicon3-bulleted_2:before { content: "\e0d6"; }
.dxicon3-bulleted_3:before { content: "\e0d7"; }
.dxicon3-volume1-fill:before { content: "\e0d8"; }
.dxicon3-unlink2:before { content: "\e0d9"; }
.dxicon3-jumplink:before { content: "\e0da"; }
.dxicon3-edit-o:before { content: "\e0db"; }
.dxicon3-delet-mtui:before { content: "\e0dc"; }
.dxicon3-close-mtui:before { content: "\e0dd"; }
.dxicon3-edit-4px-mtui:before { content: "\e0de"; }
.dxicon3-edit-3px-mtui:before { content: "\e0df"; }
.dxicon3-left-mtui:before { content: "\e0e0"; }
.dxicon3-quanping-mtui:before { content: "\e0e1"; }
.dxicon3-done-4px-mtui:before { content: "\e0e2"; }
.dxicon3-font-mtui:before { content: "\e0e3"; }
.dxicon3-list-bulleted-shuangse-mtui:before { content: "\e0e4"; }
.dxicon3-list-numbered-shuangse-mtui:before { content: "\e0e5"; }
.dxicon3-checkbox-checked-1714459382972:before { content: "\e0e6"; }
.dxicon3-bold-mtui:before { content: "\e0e7"; }
.dxicon3-pic-mtui:before { content: "\e0e8"; }
.dxicon3-dingwei_dizhi_shuidixing:before { content: "\e0e9"; }
.dxicon3-jiantou_dibu_xiazai:before { content: "\e0ea"; }
.dxicon3-sousuo_fangdajing_fangda:before { content: "\e0eb"; }
.dxicon3-sousuo_fangdajing_suoxiao:before { content: "\e0ec"; }
.dxicon3-zhuanfa:before { content: "\e0ed"; }
.dxicon3-closemini:before { content: "\e0ee"; }
.dxicon3-xuanzhuan:before { content: "\e0ef"; }
.dxicon3-wenjianjia_tianjia:before { content: "\e0f0"; }
.dxicon3-wenjianjia_yuandian:before { content: "\e0f1"; }
.dxicon3-wenjianjia_zhankai:before { content: "\e0f2"; }
.dxicon3-yichuwenjianjia:before { content: "\e0f3"; }
.dxicon3-wenjianjia_xiaoxi:before { content: "\e0f4"; }
.dxicon3-image_ocr:before { content: "\e0f5"; }
.dxicon3-jiantou_v_da_shang:before { content: "\e0f6"; }
.dxicon3-wenjianjia:before { content: "\e0f7"; }
.dxicon3-chilun_shezhi:before { content: "\e0f8"; }
.dxicon3-shuaxin:before { content: "\e0f9"; }
.dxicon3-citadel:before { content: "\e0fa"; }
.dxicon3-fanyi:before { content: "\e0fb"; }
.dxicon3-toubucaozuo:before { content: "\e0fc"; }
.dxicon3-file_o:before { content: "\e0fd"; }
.dxicon3-jiahao_xinjian_xiao_4px:before { content: "\e0fe"; }
.dxicon3-xiaoxi_pinglun_qipao_weixiao_shuang:before { content: "\e0ff"; }
.dxicon3-yijianshouqi:before { content: "\e100"; }
.dxicon3-lingdang_tongzhitixing:before { content: "\e101"; }
.dxicon3-handover:before { content: "\e102"; }
.dxicon3-jiahao_xinjian:before { content: "\e103"; }
.dxicon3-yanjing_guanbi_yincang:before { content: "\e104"; }
.dxicon3-bianxiebianyi:before { content: "\e105"; }
.dxicon3-jiaruwenjianjia:before { content: "\e106"; }
.dxicon3-zhuti_zhengfangxing_tianjiayingyong:before { content: "\e107"; }
.dxicon3-jia_yuan:before { content: "\e108"; }
.dxicon3-datouzhen_guding_da:before { content: "\e109"; }
.dxicon3-wangyetiaozhuan-4px:before { content: "\e10a"; }
.dxicon3-gantanhao_yuan_zhuyi_mianxing:before { content: "\e10b"; }
.dxicon3-datouzhen_quxiaoguding2:before { content: "\e10c"; }
.dxicon3-shenpi:before { content: "\e10d"; }
.dxicon3-shenpi_mianxing:before { content: "\e10e"; }
.dxicon3-ai_xin:before { content: "\e10f"; }
.dxicon3-unpin:before { content: "\e110"; }
.dxicon3-simple_list:before { content: "\e111"; }
.dxicon3-zhuti_zhengfangxing_yichuyingyong:before { content: "\e112"; }
.dxicon3-yanjing_dakai_xianshi:before { content: "\e113"; }
.dxicon3-biaoqian_mianxing:before { content: "\e114"; }
.dxicon3-biaoqian_wancheng_xianxing:before { content: "\e115"; }
.dxicon3-biaoqianxianxing:before { content: "\e116"; }
.dxicon3-aidanse:before { content: "\e117"; }
.dxicon3-cai_mianxing:before { content: "\e118"; }
.dxicon3-cai:before { content: "\e119"; }
.dxicon3-zan_mianxing:before { content: "\e11a"; }
.dxicon3-zan:before { content: "\e11b"; }
.dxicon3-jiaohuan:before { content: "\e11c"; }
.dxicon3-jiantou_v_xiao_you:before { content: "\e11d"; }
.dxicon3-jiantou_v_xiao_zuo:before { content: "\e11e"; }
.dxicon3-xuanzhong_banxuan:before { content: "\e11f"; }
.dxicon3-xuanzhong_duigou:before { content: "\e120"; }
.dxicon3-folder_with_inner_items:before { content: "\e121"; }
.dxicon3-officialaccount:before { content: "\e122"; }
.dxicon3-yushu:before { content: "\e123"; }
.dxicon3-markdown:before { content: "\e124"; }
.dxicon3-mticon-exit-fullscreen-o:before { content: "\e125"; }
.dxicon3-mticon-fullscreen-o:before { content: "\e126"; }
.dxicon3-location:before { content: "\e127"; }
.dxicon3-mticon-instructions:before { content: "\e128"; }
.dxicon3-zhuanrangricheng:before { content: "\e129"; }
.dxicon3-zhuanranghuiyishi:before { content: "\e12a"; }
.dxicon3-shifanghuiyishi:before { content: "\e12b"; }
.dxicon3-shoujingengduo:before { content: "\e12c"; }
.dxicon3-yichu:before { content: "\e12d"; }
.dxicon3-yichugengduo:before { content: "\e12e"; }
.dxicon3-1_1_bili:before { content: "\e12f"; }
.dxicon3-fitscreen:before { content: "\e130"; }
.dxicon3-person:before { content: "\e131"; }
.dxicon3-daxiang:before { content: "\e132"; }
