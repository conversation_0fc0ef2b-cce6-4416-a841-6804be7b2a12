/* eslint-disable @typescript-eslint/naming-convention */
// eslint-disable-next-line no-restricted-imports
import {
    isMultiLinkMessage,
    isLinkMessage,
    isQuoteMessage,
    isRichTextMessage,
    isTextMessage,
    isCustomMessage
} from '@helpers/messages/check-type';
import {type DxMessage, type SessionId, type TranslateAttachment, TranslateStatus} from '@xm/electron-imsdk-types';

import {getRichNodesFromTranslation} from '.';

import type {RTMImageNode, RTMTextNode} from 'typings/RichTextMessage';

export const MAX_MESSAGE_TRANSLATION_DB_SIZE = 100;

export enum TRANSLATE_STATUS {
    INIT = 'init',
    LOADING = 'loading',
    SUCCESS = 'success',
    FAILED = 'failed',
    TIMEOUT = 'timeout'
}

export enum TranslateResultFeedback {
    INIT = 0,
    THUMB_UP = 1,
    THUMB_DOWN = -1
}

export interface Translation {
    isShow: boolean; // 是否展开，默认true
    targetLanguage: string; // 翻译语言
    status: TRANSLATE_STATUS; // 翻译状态
    // originalData?: (RTMTextNode | RTMImageNode)[]; // 待翻译原始数据
    result?: (RTMTextNode | RTMImageNode)[]; //  翻译结果
    time?: number; // 发起请求的时间
    feedback?: number;
    sessionId: string;
    origin?: (RTMTextNode | RTMImageNode)[];
}

export interface translateOptions {
    translateResult?: TranslateResult;
    translationRetry?: () => void;
    logger?: {
        error: (event: string, ...args: unknown[]) => void;
    };
    fromSource?: 'reply-thread' | 'merge-forward';
    fromSessionId?: SessionId;
    translation?: TranslateAttachment;
    updateTranslationFoldedMsgs?: (mid: string) => void; // 只用于侧边栏存储手动收起的翻译消息
}

export interface TranslateResult {
    status: TranslateStatus;
    text: string;
    isShow: boolean;
    RichNode: (RTMTextNode | RTMImageNode)[];
    time?: number;
    sessionId?: string;
    targetLanguage: string;
    feedback: TranslateResultFeedback;
}

export const DEFAULT_TRANSLATE_RESULT: TranslateResult = {
    status: TranslateStatus.INIT,
    text: '',
    isShow: false,
    targetLanguage: '',
    RichNode: [],
    feedback: TranslateResultFeedback.INIT
};

// 判断是否为纯文本
const isPlainText = (result: Translation['result']): result is RTMTextNode[] => {
    return !!result && result.length === 1 && result[0].t === 'text';
};

export /**
 * 获取翻译结果
 * @param {string} extension
 * @return {TranslateResult}
 */
const getTranslateResult = (dxMessage: DxMessage) => {
    const translation = dxMessage.translate;
    const {message} = dxMessage;
    if (!translation) return DEFAULT_TRANSLATE_RESULT;
    const {showStatus = false, translateLanguage, translateStatus, timestamp, translateText} = translation;
    const translateNode = translateText ? getRichNodesFromTranslation(dxMessage) : [];

    const translateResult: TranslateResult = {
        targetLanguage: translateLanguage,
        isShow: showStatus,
        status: translateStatus,
        text:
            (isQuoteMessage(message) || isTextMessage(message)) && isPlainText(translateNode)
                ? translation?.translateText?.[0] || ''
                : '',
        RichNode:
            isQuoteMessage(message) ||
            isRichTextMessage(message) ||
            isLinkMessage(message) ||
            isMultiLinkMessage(message) ||
            isCustomMessage(message)
                ? translateNode
                : [],
        time: timestamp,
        sessionId: dxMessage.sessionId.uid,
        feedback: translation.feedback
    };
    return translateResult;
};
