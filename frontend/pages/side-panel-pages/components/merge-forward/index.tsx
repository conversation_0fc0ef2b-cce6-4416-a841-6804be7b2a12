import React, {useEffect, useRef, useState} from 'react';
import {dynamicDxDeskTopConfigApi, handleUpdateMessage, myVCardLoader, Toast} from '@pages/side-panel-pages/apis';
import SimpleBubbleItem from '@components/SimpleBubbleItem';
import {timeoutWrapper} from '@pages/side-panel-pages/utils/timeoutWrapper';
import {getMergeForwardTitle} from '@frontend/helpers/mergeForwardMessageTitle';
import {debugLogger} from '@pages/side-panel-pages/apis/logger';
import {updateMsgByCard, UPDATE_FORM_SOURCE, initOrGetCard} from '@utils/card/utils';
import {bindDataMessageByCard, bindCommonEventsByCard, updateCardMsg} from '@utils/card/eventBind';
import {retry} from '@shared/common/async';
import {ActionType} from '@frontend/components/SimpleBubbleItem/actions/typings';
import {i18n} from '@lib/i18n';
import {
    DynamicLoaderVirtualList as DynamicLoader,
    DYNAMIC_LOADER_LOADING_STATE,
    type Virtualizer
} from '@xm/virtual-list';
import debounce from 'lodash/debounce';
import {DxMessageChangedSource} from '@xm/electron-imsdk-types';
import {getTranslateResult} from '@frontend/helpers/messages/translate';

import {report, request} from '#preload/webview';

import {
    autoMessagesTranslate,
    handleConfigChanged,
    handleTranslationUpdate,
    updateTranslationFoldedMsgs
} from '../MessageTranslateWrapper/imsdkMethods';
import {getLocalConfig} from '../MessageTranslateWrapper/userStorage';

import {store} from './store';
import Header from './Header';
import LoadingPage from './LoadingPage';
import ReloadPage from './ReloadPage';
import {
    dxGetGroupMembers,
    dxGetGroupRobotMembers,
    getMergeForwardMessages,
    logger,
    messageOptions,
    updateMessageByCardOrUniCard
} from './apis';
import {SESSION_TYPE} from './types';
import isMergeForwardAllRecalled from './helpers/isMergeForwardAllRecalled';

import type {DataMessage, DxMessage, ISidePanelComponent, Message} from '@pages/side-panel-pages/type';
import type {TransCardData, IMsgInfoList} from '@utils/card/types';
import type {ISidePanelPages} from '@shared/types';

import styles from './index.module.scss';

initOrGetCard(request);

const enum LoadingStatus {
    LOADING = 'loading',
    SUCCESS = 'success',
    FAIL = 'fail'
}

const createHeaderText = (titleParam: Parameters<ISidePanelPages['mergeForward']>[1]): string => {
    if (typeof titleParam === 'string') {
        return titleParam;
    }
    if (titleParam) {
        return getMergeForwardTitle({
            type: titleParam.type,
            fromName: titleParam.from,
            toName: titleParam.to,
            fromEnName: titleParam.fromEnName,
            toEnName: titleParam.toEnName,
            fromZhHkName: titleParam.fromZhHkName,
            toZhHkName: titleParam.toZhHkName,
            toNameShowRule: Number(titleParam.toNameShowRule || 1),
            fromNameShowRule: Number(titleParam.fromNameShowRule || 1),
            toLaName: titleParam.toLaName,
            fromLaName: titleParam.fromLaName
        });
    }
    return '';
};

const MergeForward: ISidePanelComponent<'mergeForward'> = ({params, close, perfTiming}) => {
    const [title, setTitle] = useState<string>('');

    const [messages, setMessages] = useState<Message[]>([]);
    const [myUid, setMyUid] = useState<string>();
    const [validUids, setValidUids] = useState<string[]>([]);
    const [loadingStatus, setLoadingStatus] = useState<LoadingStatus>(LoadingStatus.LOADING);
    const [loadingError, setLoadingError] = useState<unknown>();
    const messagesField = useRef<Message[] | undefined>();
    const [notInGroupList, setNotInGroupList] = useState<string[]>([]);
    const [originDxMessageList, setOriginDxMessageList] = useState<DxMessage[]>([]);
    const [isAutoTranslateEnable, setAutoTranslateEnable] = useState(false);
    const autoTranslateTriggerDelay = useRef(2000);

    const [mergedMessageIds, titleRaw, showBackButton = false, source, contextForCard] = params;

    const curSessionId = contextForCard?.nodeInfo?.belongTo
        ? {
              channelId: 0,
              type: contextForCard?.nodeInfo?.sessionType,
              uid: contextForCard?.nodeInfo?.belongTo,
              sid: ''
          }
        : undefined;

    useEffect(() => {
        store.mergedMessageIds = mergedMessageIds;
        store.source = source;
        store.contextForCard = contextForCard;
    }, [mergedMessageIds, source, contextForCard]);

    const hasReport = useRef<boolean>(false);

    const mergeForwardMsgListRef = useRef<Virtualizer<HTMLDivElement, Element>>(null);
    const scrollTimer = useRef<NodeJS.Timeout | null>(null);

    function handleDataMessageByCard({message}: {message: DataMessage}) {
        const currentData = messagesField?.current?.map(msg => ({message: msg}));
        updateCardMsg(message, {currentData});
    }

    useEffect(() => {
        // 获取灰度配置
        getLocalConfig().then(res => {
            setAutoTranslateEnable(res?.grayConfig?.auto_translate || false);
        });
        dynamicDxDeskTopConfigApi.getOneConfig('auto_translate_trigger_delay').then(res => {
            autoTranslateTriggerDelay.current = Number(res || 2000);
        });
    }, []);

    useEffect(() => {
        // 获取到最新的state值
        messagesField.current = messages;
    }, [messages]);

    useEffect(() => {
        if (messages.length === 0 || messages[0].sessionType !== SESSION_TYPE.GROUP_CHAT) return;
        const sessionUid = messages[0]?.belongTo;
        Promise.all([dxGetGroupMembers(sessionUid), dxGetGroupRobotMembers(sessionUid)]).then(res => {
            const [member, robot] = res;
            const newValidUids = [
                ...member.groupMembers.map(item => item.uid),
                ...robot.robotMembers.map(item => item.id)
            ];
            setValidUids(newValidUids);
        });
    }, [messages]);

    useEffect(() => {
        let startTime = NaN;
        let mergedMessagesFetchedTime = NaN;
        let updateMessageByCardTime = NaN;

        const fetchMergedMessages = async (isTimeout: () => boolean) => {
            debugLogger('start to fetch merged Messages');
            startTime = Date.now();

            // 获取合并转发消息的消息内容
            const forwardParams = mergedMessageIds.map(layer => {
                if ('shareId' in layer) {
                    store.isFromLevi = true;
                    return layer;
                }
                const {fid, mid, sessionType, favoriteId} = layer;
                return {
                    mid,
                    fid,
                    type: sessionType === SESSION_TYPE.CS_CHAT ? SESSION_TYPE.PUB_CHAT : sessionType,
                    favoriteId
                };
            });
            const mergedMessages = await retry(() => getMergeForwardMessages(forwardParams, curSessionId), 0, 2);
            logger.info('get merged messages', curSessionId);

            mergedMessagesFetchedTime = Date.now();
            debugLogger(`fetch merged message ${mergedMessages.title} * ${mergedMessages.messages.length}`);

            myVCardLoader.ready().then(myVCard => {
                setMyUid(String(myVCard.uid));
                debugLogger(`fetched my uid, is ${myVCard.uid}`);
            });

            // 请求get接口
            const messageWithCard = await updateMsgByCard({
                msgs: mergedMessages.messages,
                contextForCard,
                func: (msg: Message) => ({
                    message: msg
                }),
                antiFunc: (msg: {message: Message}) => msg.message,
                fromSource: UPDATE_FORM_SOURCE.MERGE_FORWARD
            });
            updateMessageByCardTime = Date.now();

            // ! test
            // await new Promise(resolve => setTimeout(resolve, 7000));

            // 如果已经超时，就别更新数据了，会误导用户造成迷惑
            if (isTimeout()) {
                return;
            }

            setTitle(mergedMessages.title);
            setMessages(messageWithCard);
            setOriginDxMessageList(mergedMessages.originDxMessages);
            debugLogger('fetch merged messages finished');
            setLoadingStatus(LoadingStatus.SUCCESS);
            setLoadingError(undefined);
        };

        timeoutWrapper(fetchMergedMessages, 7000)
            .catch(error => {
                setLoadingStatus(LoadingStatus.FAIL);
                setLoadingError(error);
                if (isMergeForwardAllRecalled(error)) {
                    // 消息全部撤回：展示返回的错误信息 (已国际化)
                    Toast.error(String(error?.message || ''));
                } else if (error?.message) {
                    // 换回窗口组件的 toast，便于展示长信息
                    Toast.error(i18n.$t('merge_forward_request_failed', '请求失败，请稍后再试'));
                }
                debugLogger('Error occurred during the initialization of the interface:', error);
                logger.error('Error occurred during the initialization of the panel ', error);
                report.metrics({
                    nm: 'SidePanel.loadData.error',
                    val: {
                        page: 'mergeForward',
                        reason: error?.message || 'unknown',
                        duration: Date.now() - perfTiming.callShowPageTime
                    }
                });
            })
            .finally(() => {
                if (!hasReport.current) {
                    hasReport.current = true;
                    report.metrics({
                        nm: 'global.imInnerPage.statTime',
                        val: {
                            page: 'mergeForward',
                            fmt: Date.now() - perfTiming.callShowPageTime,
                            isPreCreated: perfTiming.isPreCreated,
                            extraTime: {
                                fetchMessages: mergedMessagesFetchedTime - startTime,
                                updateMessageByCard: updateMessageByCardTime - mergedMessagesFetchedTime
                            }
                        }
                    });
                }
            });

        // 绑定卡片通用事件
        const disposeCardEvent = bindCommonEventsByCard(
            (data?: TransCardData[] | null, cancelMidList?: string[], ...args) => {
                const extraInfo = args?.[2] as {msgInfoList: IMsgInfoList[]};
                const {newMsgList, noPermissionMsgList} = updateMessageByCardOrUniCard(
                    messagesField?.current || [],
                    data,
                    cancelMidList,
                    extraInfo
                );
                if (noPermissionMsgList?.length) {
                    setNotInGroupList(noPermissionMsgList);
                    return;
                }
                if (!noPermissionMsgList?.length && newMsgList?.length) {
                    setMessages(newMsgList);
                }
            }
        );
        // 绑定卡片IMSDK透传事件
        const disposeCardIMEvent = bindDataMessageByCard(handleDataMessageByCard);

        return () => {
            disposeCardIMEvent.then(d => d());
            disposeCardEvent();
        };
    }, [mergedMessageIds, perfTiming]);

    useEffect(() => {
        if (!isAutoTranslateEnable) return;
        // 添加消息更新监听
        // eslint-disable-next-line @typescript-eslint/no-shadow
        handleUpdateMessage((dxMessages, source) => {
            if (source !== DxMessageChangedSource.Translate) return;
            const newDxMsgList = handleTranslationUpdate(
                originDxMessageList,
                dxMessages,
                mergeForwardMsgListRef.current?.scrollToBottom
            );
            newDxMsgList && setOriginDxMessageList([...newDxMsgList]);
        });
        handleConfigChanged(curSessionId, () => onBubbleListScrollEnd());
    }, [originDxMessageList, isAutoTranslateEnable]);

    // 消息列表停止滚动，获取视口内的消息
    const onBubbleListScrollEnd = debounce(() => {
        if (!isAutoTranslateEnable || !mergeForwardMsgListRef.current || !navigator.onLine || store.isFromLevi) return;
        const delay = autoTranslateTriggerDelay;
        scrollTimer.current && clearTimeout(scrollTimer.current);
        scrollTimer.current = setTimeout(() => {
            // 获取当前视图中可见的消息
            const msgsVirtualItemsInView = mergeForwardMsgListRef.current?.getVirtualItemsInViewport();
            autoMessagesTranslate(
                msgsVirtualItemsInView,
                originDxMessageList,
                {
                    channelId: 0,
                    type: contextForCard?.nodeInfo?.sessionType || messages[0].sessionType,
                    uid: contextForCard?.nodeInfo?.belongTo || messages[0].belongTo,
                    sid: ''
                },
                'mergeForward'
            );
        }, delay.current);
    }, 300);

    useEffect(() => {
        onBubbleListScrollEnd();
    }, [messages, isAutoTranslateEnable]);

    return (
        <div className={styles['forward-messages-panel']}>
            <Header
                title={createHeaderText(titleRaw) || title}
                onBack={showBackButton ? () => close('back') : undefined}
                onClose={() => close('close')}
            />
            <div className={styles.body}>
                {loadingStatus === LoadingStatus.LOADING && <LoadingPage />}
                {loadingStatus === LoadingStatus.FAIL && <ReloadPage error={loadingError} />}
                {loadingStatus === LoadingStatus.SUCCESS && (
                    <DynamicLoader
                        ref={mergeForwardMsgListRef}
                        loadingState={DYNAMIC_LOADER_LOADING_STATE.HIDDEN}
                        onScrollToBottom={() => {}}
                        onScrollEnd={onBubbleListScrollEnd}
                        onScrollHandler={() => {
                            scrollTimer.current && clearTimeout(scrollTimer.current);
                        }}
                    >
                        {messages.map((message, index) => {
                            const isMe = message.from === myUid;
                            const originDxMessage = originDxMessageList[index];
                            return (
                                <SimpleBubbleItem
                                    key={message.mid}
                                    message={message}
                                    side={isMe ? 'right' : 'left'}
                                    options={{
                                        ...messageOptions,
                                        isMe,
                                        myUid,
                                        contextForCard,
                                        notInGroup: notInGroupList.includes(message.mid),
                                        vaildUids: validUids,
                                        translateOptions: {
                                            translation: originDxMessage?.translate,
                                            translateResult: getTranslateResult(originDxMessage || {}),
                                            fromSource: 'merge-forward',
                                            fromSessionId: {
                                                channelId: 0,
                                                type: contextForCard?.nodeInfo?.sessionType || message.sessionType,
                                                uid: contextForCard?.nodeInfo?.belongTo || message.belongTo,
                                                sid: ''
                                            },
                                            updateTranslationFoldedMsgs
                                        }
                                    }}
                                    showTime
                                    showName={!isMe}
                                    avatarShowVCard
                                    messageMenu={{
                                        multiMenu: store.isFromLevi
                                            ? [ActionType.LogMessage]
                                            : [
                                                  ActionType.LogMessage,
                                                  ActionType.Translate,
                                                  ActionType.OperateTranslation
                                              ]
                                    }}
                                />
                            );
                        })}
                    </DynamicLoader>
                )}
            </div>
        </div>
    );
};

export default MergeForward;
