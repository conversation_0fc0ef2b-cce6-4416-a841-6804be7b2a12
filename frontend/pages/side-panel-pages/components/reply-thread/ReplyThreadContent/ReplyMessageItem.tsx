import React from 'react';
import {observer} from 'mobx-react';
import SimpleBubbleItem from '@components/SimpleBubbleItem';
import {ActionType} from '@frontend/components/SimpleBubbleItem/actions/typings';
import {formatMessageDate} from '@frontend/utils/timeRelated';
import {getTranslateResult} from '@frontend/helpers/messages/translate';

import {useStores} from '../store';
import {messageOptions} from '../apis';
import {updateTranslationFoldedMsgs} from '../../MessageTranslateWrapper/imsdkMethods';

import type {IContextForCard} from '@shared/types/decls';
import type {DxMessage, Message} from '@xm/electron-imsdk/types';
import type {IMessageFrom, TypedMessage} from '../../../type';

import styles from './index.module.scss';

const ReplyMessageItem = observer(
    ({
        message,
        originDXMessage,
        from,
        contextForCard,
        showTimeLine = false
    }: {
        message: TypedMessage<any>;
        from: IMessageFrom;
        contextForCard?: IContextForCard | null;
        showTimeLine?: boolean;
        originDXMessage: DxMessage;
    }) => {
        const {messageOptionsData} = useStores();

        return (
            <>
                {showTimeLine && <div className={styles['time-line']}>{formatMessageDate(message.svrTime)}</div>}
                <SimpleBubbleItem
                    className="reply-thread-item"
                    showName={from.side === 'left'}
                    message={message as Message}
                    side={from.side}
                    roleName={from.side === 'left' ? from.roleName : undefined}
                    showTime={false}
                    avatarShowVCard
                    options={{
                        ...messageOptions,
                        myUid: messageOptionsData.myUid,
                        vaildUids: messageOptionsData.validUids,
                        contextForCard,
                        translateOptions: {
                            translation: originDXMessage?.translate,
                            translateResult: getTranslateResult(originDXMessage || {}),
                            fromSource: 'reply-thread',
                            fromSessionId: {
                                channelId: message.channelId,
                                type: message.sessionType,
                                uid: message.belongTo,
                                sid: message.sid
                            },
                            updateTranslationFoldedMsgs
                        }
                    }}
                    enableDebug
                    messageMenu={{
                        viewContextEntry: true,
                        multiMenu: [ActionType.LogMessage, ActionType.Translate, ActionType.OperateTranslation]
                    }}
                />
            </>
        );
    }
);

export default ReplyMessageItem;
