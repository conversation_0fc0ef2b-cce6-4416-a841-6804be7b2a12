import {type IMSDKClient} from '@xm/electron-imsdk/client';
import getErrorMessage from '@helpers/error/getErrorMessage';

import {imsdk, ui, createLogger} from '#preload/webview';

import type {IMSDKMethodsImpl, IMSDKEventImpl} from '@xm/electron-imsdk/server';
import type {UnfoldPromise} from '@shared/types';

const getErrorText = (e: unknown) => {
    const errorCode: number | null = typeof (e as any)?.code === 'number' ? (e as any)?.code : null;
    const errorText = (e as any)?.message || e?.toString();
    if (typeof errorCode === 'number') {
        return getErrorMessage(errorCode, errorText);
    }
    return errorText;
};

class IMClient {
    #IMSDKClient!: IMSDKClient;
    logger = createLogger('message-tranlsate-wrapper');
    translationFoldedMsgs: string[] = []; // 手动收起的翻译在当前会话中不再曝光展开

    init = async () => {
        this.#IMSDKClient = (await imsdk.getClient()) as unknown as IMSDKClient;
    };

    call = async <T extends keyof Omit<IMSDKMethodsImpl, 'release' | 'init'>>(
        event: T,
        args?: Parameters<IMSDKMethodsImpl[T]>,
        options?: {
            /**
             * 非true时不会弹出错误toast
             */
            showError?: boolean;
        }
    ): Promise<UnfoldPromise<ReturnType<IMSDKMethodsImpl[T]>>> => {
        try {
            if (!this.#IMSDKClient) {
                await this.init();
            }
            const res = (await this.#IMSDKClient.call(event, args as any)) as ReturnType<IMSDKMethodsImpl[T]>;

            return res as unknown as Promise<UnfoldPromise<ReturnType<IMSDKMethodsImpl[T]>>>;
        } catch (e) {
            if (options?.showError !== true) {
                throw e;
            }

            const errorText = getErrorText(e);
            errorText &&
                ui.showToast(errorText, {
                    type: 'error',
                    showInactive: true
                });
            throw e;
        }
    };

    on = async <T extends keyof IMSDKEventImpl>(
        event: T,
        listener: (...args: Parameters<IMSDKEventImpl[T]>) => void
    ) => {
        if (!this.#IMSDKClient) {
            await this.init();
        }
        return this.#IMSDKClient.on(event, listener);
    };
}

const imClient = new IMClient();
export default imClient;
