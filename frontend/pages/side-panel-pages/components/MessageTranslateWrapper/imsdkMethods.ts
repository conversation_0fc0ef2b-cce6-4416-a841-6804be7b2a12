import {toJS} from 'mobx';
import {autoTranslateCondition, shouldTriggerAutoTranslate} from '@frontend/helpers/messages/translate';
import {SESSION_TYPE_TO_CONFIG_CHAT_TYPE} from '@frontend/constants/sessions';

import {account} from '#preload/webview';

import imClient from './imClient';

import type {VirtualItem} from '@xm/virtual-list';
import type {DxMessage, Message, SessionId} from '@xm/electron-imsdk-types';

export const dxTranslateMessages = (params: {sessionId: SessionId; messages: Message[]; autoTranslate: boolean}) => {
    return imClient.call('message.dxTranslateMessages', [{...params, first: false}]);
};

export const handleConfigChanged = (
    sessionId: SessionId | undefined,
    listener: ({
        globalAutoTranslate,
        sessionAutoTranslate
    }: {
        globalAutoTranslate: string | undefined;
        sessionAutoTranslate: Record<string, string> | undefined;
    }) => void
) => {
    imClient.on('config.dx_config_changed', ({data}) => {
        if (!sessionId) return;
        const {globalAutoTranslate, sessionAutoTranslate} = data;
        if (!globalAutoTranslate && !sessionAutoTranslate) return;
        const key = `${SESSION_TYPE_TO_CONFIG_CHAT_TYPE[sessionId.type]}-${sessionId.uid}`;
        if (sessionAutoTranslate?.[key] || globalAutoTranslate) {
            listener({globalAutoTranslate, sessionAutoTranslate});
        }
    });
};

/**
 * 翻译数据更新，更新侧边栏消息数据
 * @param originDxMessage 原来的dxMessage消息列表
 * @param updateDxMessages 监听到的dxMessage消息列表
 * @returns DxMessage[]
 */
export const handleTranslationUpdate = (
    originDxMessage: DxMessage[],
    updateDxMessages: DxMessage[],
    scrollToBottom?: () => void
): DxMessage[] => {
    if (updateDxMessages.length === 0) return originDxMessage;

    const updateMap = new Map();
    for (const message of updateDxMessages) {
        updateMap.set(message.uuid, message.translate);
    }

    let hasUpdates = false;
    const updatedMessages = originDxMessage.map((message, index) => {
        const newTranslate = updateMap.get(message.uuid);
        if (newTranslate !== undefined && newTranslate !== message.translate) {
            hasUpdates = true;
            index === originDxMessage.length - 1 &&
                setTimeout(() => {
                    scrollToBottom?.();
                }, 100);
            return {...message, translate: newTranslate};
        }
        return message;
    });

    return hasUpdates ? updatedMessages : originDxMessage;
};

export const updateTranslationFoldedMsgs = (mid: string) => {
    !imClient.translationFoldedMsgs.includes(mid) && imClient.translationFoldedMsgs.push(mid);
};

/**
 * 视口内的消息自动翻译主流程
 * @param virtualItemsInView 视口内的virtualItem
 * @param messageList 原始dxMessage消息列表
 * @param fromSessionId 触发翻译的当前会话sessionId
 * @returns void
 */
export const autoMessagesTranslate = async (
    virtualItemsInView: VirtualItem[] | undefined,
    messageList: DxMessage[],
    fromSessionId: SessionId,
    fromSource: 'replyThread' | 'mergeForward'
) => {
    if (!virtualItemsInView || !messageList) return;
    const translateMsgs: Message[] = [];
    const myUid = (await account.getLoginInfo()).uid;
    (virtualItemsInView ?? []).forEach(item => {
        if (item.data && ['fowardLoader', 'BackLoader'].includes(item.data)) return;
        const msg = messageList[item.index].message;

        // 校验消息类型及消息内容是否触发自动翻译
        const isMsgFromMe = msg.from?.toString() === myUid;
        if (isMsgFromMe || !autoTranslateCondition(msg) || imClient.translationFoldedMsgs.includes(msg.mid)) return;
        const shouldTrigger = shouldTriggerAutoTranslate(msg);
        if (!shouldTrigger) return;

        translateMsgs.push(toJS(msg));
    });

    if (!translateMsgs.length) return;

    try {
        const msgs = await dxTranslateMessages({
            sessionId: fromSessionId,
            messages: translateMsgs,
            autoTranslate: true
        });
        imClient.logger.info('dxTranslateMessages', fromSource, translateMsgs.length, msgs);
    } catch (error) {
        imClient.logger.error('dxTranslateMessages error', error);
    }
};
