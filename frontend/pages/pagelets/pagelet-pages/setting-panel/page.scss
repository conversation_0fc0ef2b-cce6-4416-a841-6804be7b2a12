// 关于单个设置标签页的通用 mtd 样式

$button-color: rgb(0 0 0 / 84%);
$button-border-color: rgb(0 0 0 / 12%);

@mixin default-button-color {
    color: $button-color;
    border-color: $button-border-color;
}

.settings-page-wrapper.modified {
    width: 100%;
    position: relative;

    // 自定义 mtd-button 样式
    .mtd-button.mtd-button-rect {
        height: unset;
        padding: 1px 12px;
        margin-left: 12px;
        line-height: 1.5;
        vertical-align: middle;

        &:focus {
            @include default-button-color;
        }

        &:hover {
            @include default-button-color;

            background-color: rgb(0 0 0 / 5%);
        }

        &:active {
            @include default-button-color;

            background-color: rgb(0 0 0 / 10%);
        }

        .mtd-button-label {
            line-height: 1;
            vertical-align: baseline;
        }
    }

    // 自定义 mtd-checkbox 样式
    .mtd-checkbox {
        height: 20px;
        line-height: initial;

        .mtd-checkbox-inner {
            top: 2px;
            width: 14px;
            height: 14px;
            margin: 0 1px;
        }

        &-label {
            padding-left: 6px;
        }
    }

    .mtd-checkbox:hover .mtd-checkbox-inner,
    .mtd-checkbox-input:focus + .mtd-checkbox-inner {
        border-color: rgb(0 0 0 / 12%) !important;
    }

    // checkbox 下的提示文字样式
    .checkbox-hint {
        font-size: 12px;
        color: rgb(0 0 0 / 38%);
        letter-spacing: 0;
        line-height: 18px;
        padding-left: 24px;
        margin-top: 2px;
    }

    // 自定义 mtd-radio 样式
    .mtd-radio-group {
        display: block;
        padding-left: 16px;

        .mtd-radio.mtd-radio-group-item {
            margin: 15px 0;
            display: flex;
            height: 20px;
            align-items: center;

            &:not(.mtd-radio-disabled) .mtd-radio-input:focus + .mtd-radio-inner {
                border-color: rgb(0 0 0 / 12%) !important;
            }

            &:not(.mtd-radio-disabled):hover .mtd-radio-inner {
                border-color: #166ff7 !important;
            }

            .mtd-radio-label {
                flex: 0 1 auto;
            }
        }
    }
}

// 自定义 mtd-message 样式
.mtd-message {
    .mtd-message-notice:not(:last-child) {
        display: none;
    }

    .mtd-message-notice:last-child {
        top: 16px !important;
    }

    .mtd-message-success {
        box-shadow: 0 2px 8px 0 rgb(0 0 0 / 20%);
        padding: 10px 16px;
        border-radius: 4px;
        text-align: center;
        background-color: #f7fbf5;
        color: #5abb3c;
    }
}

// 自定义 mtd-select 样式
.mtd-select {
    width: 223px;
    font-size: 12px;
    cursor: pointer;

    .mtd-select-filter {
        height: 22px;
        width: 221px;

        // 禁用输入
        .mtd-select-filter-input {
            max-width: 0;
        }

        .mtd-select-filter-label {
            padding: 0 0 0 12px;
            line-height: 22px;
            height: 22px;

            &.mtd-select-filter-hint {
                color: rgb(0 0 0 / 84%);
            }

            .dxicon3-dropmenucheck {
                display: none;
            }
        }

        .mtd-select-filter-icon {
            line-height: 22px;
            right: 4px;
        }

        .mtdicon-down-thick {
            color: rgb(0 0 0 / 84%);
        }
    }
}

.mtd-select-popup.mtd-select-popup-small {
    padding: 4px;
    border-radius: 6px;

    .mtd-select-item {
        padding: 0 0 0 8px;
        font-size: 12px;
        height: 28px;
        display: flex;
        align-items: center;
        border-radius: 4px;

        .mtd-select-item-content {
            justify-content: space-between;
            align-items: center;
            display: flex;
        }

        &:hover {
            background-color: rgb(0 0 0 / 4%);
        }
    }

    .mtd-select-item-selected {
        color: rgb(0 0 0 / 87%);
        background-color: white;

        .dxicon3-dropmenucheck {
            padding-right: 4px;
            color: #166ff7;
        }
    }
}

// 单个设置标签页的通用样式

.settings-page {
    // 设置组
    &-group {
        padding: 15px 126px 0;

        &:first-child {
            padding-top: 0;
        }

        &-title {
            font-size: 14px;
            font-weight: 600;
            color: rgb(0 0 0 / 87%);
            padding: 20px 0;
            line-height: 30px;
        }

        &-content {
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;

            &.no-underline {
                border-bottom: 0;
            }

            .setting-item {
                margin-bottom: 10px;
                line-height: normal;

                &-content {
                    display: inline-block;
                    line-height: 32px;
                    vertical-align: middle;
                }

                &-comment {
                    font-size: 12px;
                    color: rgb(0 0 0 / 60%);
                    line-height: 22px;
                }
            }
        }

        .with-sub-title {
            display: inline-block;
            line-height: 32px;

            span {
                display: block;
            }

            .sub-title {
                color: #3d3d3d;
                opacity: 0.5;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
            }
        }
    }

    // 设置页新版卡片样式
    &-section-title {
        font-size: 18px;
        font-weight: 500;
        line-height: 26px;
        padding: 20px 0 10px 12px;
    }

    &-section-item {
        border-radius: 8px;
        background: rgb(17 25 37 / 3%);

        &-title {
            font-size: 14px;
            font-weight: 600;
            color: rgb(0 0 0 / 87%);
            padding: 20px 0 7px 12px;
            line-height: 30px;
        }

        &-title-tips {
            font-size: 12px;
            color: rgb(17 25 37 / 45%);
            line-height: 20px;
            font-weight: normal;
        }
    }

    &-section-item-select {
        display: flex;
        padding: 5px 8px 5px 0;
        justify-content: space-between;
        align-items: center;
    }

    &-section-item-switch {
        padding: 11px 0;
    }

    &-section-item-radio {
        padding: 11px 12px 0 0;
    }

    &-section-item-select, &-section-item-switch, &-section-item-radio {
        margin-left: 12px;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: 0;
        }
    }
}
