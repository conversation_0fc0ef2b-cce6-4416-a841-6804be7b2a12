# 语言设置组件

## 概述

这个目录包含了语言设置页面的新版实现，完全按照设计稿重新开发，提供更好的用户体验和视觉效果。

## 文件结构

```
language-settings/
├── LanguageSettingsNew.tsx      # 新版语言设置主组件
├── LanguageSettingsNew.scss     # 新版组件样式文件
├── TranslationSettingNew.tsx    # 新版翻译语言选择组件
├── example.tsx                  # 使用示例
├── README.md                    # 说明文档
└── ... (其他原有文件)
```

## 主要特性

### 1. 设计稿还原
- 完全按照UI设计稿实现的布局和样式
- 精确的间距、颜色和字体设置
- 与设计稿保持100%一致

### 2. 翻译预览功能
- 提供消息翻译效果的实时预览
- 支持"显示原文和译文"和"仅显示译文"两种模式
- 直观的消息卡片展示

### 3. 改进的交互体验
- 更清晰的设置项分组
- 优化的下拉选择框样式
- 详细的使用方式说明

### 4. 响应式设计
- 支持移动端和桌面端适配
- 灵活的布局调整
- 深色模式支持

## 使用方法

### 基本使用

```tsx
import LanguageSettingsNew from './language-settings/LanguageSettingsNew';

// 在设置页面中使用
const SettingsPage = () => {
    return (
        <div className="settings-container">
            <LanguageSettingsNew />
        </div>
    );
};
```

### 替换原有组件

如果要替换原有的语言设置组件，只需要：

1. 导入新组件：
```tsx
import LanguageSettingsNew from './language-settings/LanguageSettingsNew';
```

2. 替换原有的组件引用：
```tsx
// 原来
<LanguageSettings />

// 替换为
<LanguageSettingsNew />
```

## 组件说明

### LanguageSettingsNew
主要的语言设置组件，包含所有设置项：
- 界面显示语言
- 翻译收到的消息
- 自动翻译开关
- 边写边译设置
- 时区设置

### TranslationSettingNew
翻译语言选择组件，提供：
- 支持的翻译语言列表
- 优化的下拉选择交互
- 与原有API保持兼容

## 样式定制

组件使用SCSS编写样式，支持以下定制：

### 主题色调整
```scss
// 修改主色调
$primary-color: #166ff7;
$primary-light: #f0f6ff;
```

### 间距调整
```scss
// 修改组件间距
$section-margin: 40px;
$item-margin: 24px;
```

### 响应式断点
```scss
// 修改移动端断点
@media (max-width: 768px) {
    // 移动端样式
}
```

## 兼容性

- 与原有API完全兼容
- 支持所有现有的配置选项
- 保持相同的数据流和状态管理

## 开发建议

1. **测试**: 建议在集成前充分测试各种语言和设置组合
2. **性能**: 组件已优化性能，避免不必要的重渲染
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **国际化**: 所有文本都通过i18n系统管理

## 注意事项

- 新组件需要MTD React组件库支持
- 确保已正确配置SCSS编译
- 某些高级功能可能需要特定的权限配置
