.settings-page-wrapper {
    // 语言设置选项
    .language-setting {
        & > .mtd-radio.mtd-radio-group-item {
            margin-top: 0;
        }
    }
    .settings-page-group-content.language-content {
        padding-bottom: 25px;
    }

    .timezone-setting-notice {
        font-size: 12px;
        color: rgb(17 25 35 / 45%);
    }

    .timezone-setting.mtd-radio-group {
        display: flex;
        flex-direction: row;
        gap: 11px; // 控制选项间距
        padding: 0;
        .mtd-radio-label {
            padding: 0;
        }
        .mtd-radio.mtd-radio-group-item {
            height: 157px;
            width: 208px;
            border-radius: 10px;
            border: 1px solid rgb(17 25 37 / 5%);
            &.mtd-radio-checked {
                border: 1px solid #166ff7;
            }
        }
        .mtd-radio-input-wrapper {
            position: absolute;
            bottom: 11px;
            left: 11px;
        }
    }

    .timezone-setting-radio-text {
        font-size: 14px;
        height: 35px;
        padding-left: 35px;
        line-height: 31px;
    }

    .option-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .timezone-setting-option-image {
        width: 208px; // 图片宽度
        height: 116px; // 图片高度
        background-color: #f3f7ff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }

    .auto-translation-setting {
        .setting-item {
            margin-bottom: 6px;
        }

        .auto-translate-tips {
            font-size: 12px;
            color: rgb(17 25 37 / 45%);
            margin-top: 5px;

            .setting-icon-meaning {
                font-size: 14px;
                left: 0;
            }

            .icon-notify-container {
                width: 12px;
                height: 12px;
                margin-right: 4px;
            }
        }

        .mtd-switch {
            margin-left: 16px;
        }

        .notification-bg.middle {
            width: 284px;
            height: 194px;
            border-radius: 8px;

            &.top {
                top: -205px;
                left: -136px;

                .triangle-arrow::before {
                    top: 191px;
                    left: 140px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                top: 0;
                right: 0;
            }
        }
    }

    .timezone-title {
        padding: 10px 0 20px;
    }

    .settings-page-section-item-title >.tooltip-with-icon-wrapper .notification-bg.middle {
        width: 276px;
        height: 184px;
        border-radius: 10px;

        img {
            width: 100%;
            height: 100%;
            top: 0;
            right: 0;
        }
    }
}

.language-timezone-settings {
    .settings-page-group-title {
        padding-bottom: 1px;
    }
}
