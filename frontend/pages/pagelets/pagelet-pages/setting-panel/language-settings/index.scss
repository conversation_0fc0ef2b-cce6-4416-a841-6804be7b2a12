.settings-page-wrapper {
    // 语言设置选项
    .language-setting {
        & > .mtd-radio.mtd-radio-group-item {
            margin-top: 0;
        }
    }
    .settings-page-group-content.language-content {
        padding-bottom: 25px;
    }

    .timezone-setting-notice {
        font-size: 12px;
        color: rgb(17 25 35 / 45%);
    }

    .timezone-setting.mtd-radio-group {
        display: flex;
        flex-direction: row;
        gap: 11px; // 控制选项间距
        padding: 0;
        .mtd-radio-label {
            padding: 0;
        }
        .mtd-radio.mtd-radio-group-item {
            height: 157px;
            width: 208px;
            border-radius: 10px;
            border: 1px solid rgb(17 25 37 / 5%);
            &.mtd-radio-checked {
                border: 1px solid #166ff7;
            }
        }
        .mtd-radio-input-wrapper {
            position: absolute;
            bottom: 11px;
            left: 11px;
        }
    }

    .timezone-setting-radio-text {
        font-size: 14px;
        height: 35px;
        padding-left: 35px;
        line-height: 31px;
    }

    .option-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .timezone-setting-option-image {
        width: 208px; // 图片宽度
        height: 116px; // 图片高度
        background-color: #f3f7ff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }

    .language-content .settings-page-group-title {
        padding: 20px 0 7px;
    }

    .language-setting-select {
        width: 208px;
        height: 32px;
        border-bottom: 2px solid transparent;

        .mtd-select-filter {
            height: 100%;
            width: 100%;
            border: 1px solid rgb(17 25 37 / 15%);
            border-radius: 6px;

            // 禁用输入
            .mtd-select-filter-input {
                max-width: 0;

                input {
                    height: 32px;
                    line-height: 32px;
                    padding: 0;
                }
            }

            .mtd-select-filter-label {
                padding: 0 15px 0 8px;
                line-height: 32px;
                height: 32px;
                border-radius: 4px;
                font-size: 14px;
            }

            .mtd-select-filter-icon {
                line-height: 32px;
                right: 5px;
                width: 208px;
                display: flex;
                justify-content: flex-end;
            }

            .mtdicon-down-thick {
                color: rgb(17 25 37 / 65%);
                font-weight: 500;
            }
        }
    }

    .auto-translation-setting {
        .settings-page-group-title {
            padding: 8px 0 0;
        }

        .setting-item {
            margin-bottom: 6px;
        }

        .auto-translate-tips {
            font-size: 12px;
            color: rgb(17 25 37 / 45%);

            .setting-icon-meaning {
                font-size: 14px;
                left: 0;
            }

            .icon-notify-container {
                width: 12px;
                height: 12px;
                margin-right: 4px;
            }
        }

        .mtd-switch {
            padding: 8px 0 0;
        }

        .notification-bg.middle {
            width: 284px;
            height: 194px;
            border-radius: 8px;

            &.top {
                top: -205px;
                left: -136px;

                .triangle-arrow::before {
                    top: 191px;
                    left: 140px;
                }
            }

            img {
                width: 100%;
                height: 100%;
                top: 0;
                right: 0;
            }
        }
    }

    .timezone-title {
        padding: 10px 0 20px;
    }
}

.language-timezone-settings {
    .settings-page-group-title {
        padding-bottom: 1px;
    }
}

// 下拉选择框
.language-setting-popup {
    width: 208px !important;
    border-radius: 6px;

    .mtd-select-item {
        padding: 7px 8px;
        margin: 0 3px;

        &:hover {
            border-radius: 6px;
            background-color: #F0F6FF
        }

        &::after {
            content: "";
            width: 16px;
            height: 16px;
            background-image: url("dxpc://img/option-checked.png");
            background-size: 16px 16px;
            margin-left: 24px;
            opacity: 0;
        }
    }

    .mtd-select-item-selected {
        color: #111925 !important;
        align-items: center;

        &::after {
            opacity: 1;
        }
    }

    .mtd-select-item-focused {
        background-color: transparent !important;

        &:hover {
            background-color: #F0F6FF !important;
        }
    }
}
