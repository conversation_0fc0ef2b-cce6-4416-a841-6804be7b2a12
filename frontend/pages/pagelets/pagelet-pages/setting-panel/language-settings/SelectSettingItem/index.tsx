import React from 'react';
import {Select} from '@ss/mtd-react';

import './index.scss';

const {Option} = Select;

interface SelectSettingItemProps {
    value: string;
    optionsArray: {value: string; content: string; contentTip?: string}[];
    onChange: (setValue: any) => void;
}

const SelectSettingItem = (props: SelectSettingItemProps) => {
    const {value, optionsArray, onChange} = props;

    return (
        <Select
            className="setting-select"
            value={value}
            optionLabelProp="originOption"
            onChange={option => onChange(option.value)}
            popLayer={{
                width: 'auto',
                getContainer: () => document.body,
                className: 'setting-select-popup',
                align: {points: ['tr', 'br']}
            }}
            filterable={false}
            clearable={false}
        >
            {optionsArray.map(({value: langValue, content, contentTip}) => {
                return (
                    <Option key={content} value={langValue} originOption={content}>
                        <div className="select-option-content">{content}</div>
                        <span className="select-option-tip">{contentTip}</span>
                    </Option>
                );
            })}
        </Select>
    );
};

export default SelectSettingItem;
