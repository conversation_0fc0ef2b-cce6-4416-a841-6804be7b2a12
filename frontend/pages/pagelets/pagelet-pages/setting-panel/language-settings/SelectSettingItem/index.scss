.settings-page-wrapper .setting-select {
    width: auto;
    height: 32px;
    border-bottom: 2px solid transparent;
    background-color: transparent;

    .mtd-select-filter {
        height: 100%;
        width: 100%;
        background-color: transparent;
        border: 1px solid transparent;

        // 禁用输入
        .mtd-select-filter-input {
            max-width: 0;

            input {
                height: 32px;
                line-height: 32px;
                padding: 0;
            }
        }

        .mtd-select-filter-label {
            padding: 0 20px 0 8px;
            line-height: 32px;
            height: 32px;
            border-radius: 4px;
            font-size: 14px;
        }

        .mtd-select-filter-icon {
            line-height: 32px;
            right: 5px;
            width: 208px;
            display: flex;
            justify-content: flex-end;
        }

        .mtdicon-down-thick {
            color: rgb(17 25 37 / 65%);
            font-weight: 500;
        }

        &:hover, &.mtd-select-filter-focused{
            background-color: rgb(17 25 37 / 5%);
        }
    }
}

// 下拉选择框
.setting-select-popup {
    width: 208px !important;
    border-radius: 6px;

    .mtd-select-item {
        padding: 7px 8px;
        margin: 0 3px;

        &:hover {
            border-radius: 6px;
            background-color: #F0F6FF
        }

        &::after {
            content: "";
            width: 16px;
            height: 16px;
            background: url("dxpc://img/option-checked.png") no-repeat;
            background-size: 16px 16px;
            margin-left: 24px;
            opacity: 0;
        }

        .mtd-select-item-content {
            flex: 1;
        }
    }

    .mtd-select-item-selected {
        color: #111925 !important;
        align-items: center;

        &::after {
            opacity: 1;
        }
    }

    .mtd-select-item-focused {
        background-color: transparent !important;

        &:hover {
            background-color: #F0F6FF !important;
        }
    }

    .select-option-tip {
        font-size: 12px;
        color: rgb(17 25 37 / 45%);
    }
}
