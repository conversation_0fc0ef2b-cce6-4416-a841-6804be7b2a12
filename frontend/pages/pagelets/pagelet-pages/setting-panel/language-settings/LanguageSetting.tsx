import React, {useCallback, useEffect, useState} from 'react';
import CommonWindowComponent from '@components/CommonWindowComponent';
import {i18n} from '@lib/i18n';
import {LANGUAGE_MAP, type TLocaleLanguage} from '@shared/common/I18n';
import {Select} from '@ss/mtd-react';
import {retry} from '@shared/common/async';

import {mainWindow} from '#preload/internal';

import {changeLanguage} from '../apis';

import RestartModal from './RestartModal';

const {Option} = Select;

const languageMap = {
    [LANGUAGE_MAP.zh]: {
        value: LANGUAGE_MAP.zh,
        content: '简体中文'
    },
    [LANGUAGE_MAP.zh_HK]: {
        value: LANGUAGE_MAP.zh_HK,
        content: '繁體中文'
    },
    [LANGUAGE_MAP.en]: {
        value: LANGUAGE_MAP.en,
        content: 'English'
    },
    [LANGUAGE_MAP.pt_BR]: {
        value: LANGUAGE_MAP.pt_BR,
        content: 'Português'
    }
};

const LanguageSetting = (): JSX.Element => {
    /**
     * 当前语言不需要作为 state
     * 因为切换语言会调出弹窗，若用户点击取消，则不需要切换当前语言
     * 若用户点击确定，则直接重启大象了
     */
    const [selectLang, setSelectLang] = useState<TLocaleLanguage>(i18n.getLanguage());
    const [showRestartModal, setShowRestartModal] = useState(false);
    const [enablePtBr, setEnablePtBr] = useState(false);

    useEffect(() => {
        retry(() => mainWindow.getIMData('getGrayConfig', 'la_set_pt-BR'), 50, 3).then(res => {
            setEnablePtBr(!!res);
        });
    }, []);

    const showRestartWindow = useCallback(() => {
        setShowRestartModal(true);
    }, []);

    const hideRestartWindow = useCallback(() => {
        setShowRestartModal(false);
        setSelectLang(i18n.getLanguage());
    }, []);

    const handleLanguageChange = useCallback(
        (lang: TLocaleLanguage) => {
            setSelectLang(lang);
            showRestartWindow();
        },
        [showRestartWindow]
    );

    const restart = useCallback(() => {
        changeLanguage(selectLang, true);
    }, [selectLang]);

    return (
        <div className="settings-page-section-item-select">
            {i18n.$t('setting_panel_language_display_language', '界面显示语言')}
            <Select
                className="language-setting-select"
                value={selectLang}
                optionLabelProp="originOption"
                onChange={option => handleLanguageChange(option.value)}
                popLayer={{width: 'auto', getContainer: () => document.body, className: 'language-setting-popup'}}
                filterable={false}
                clearable={false}
            >
                {Object.values(languageMap).map(({value, content}) => {
                    if (value === LANGUAGE_MAP.pt_BR && !enablePtBr) {
                        return null;
                    }
                    return (
                        <Option key={content} value={value} originOption={content}>
                            <span className="lan-option-content">{content}</span>
                        </Option>
                    );
                })}
            </Select>
            <CommonWindowComponent show={showRestartModal} mask width={432} height={136} center x={2500} y={2500}>
                <RestartModal onCancel={hideRestartWindow} onConfirm={restart} />
            </CommonWindowComponent>
        </div>
    );
};

export default LanguageSetting;
