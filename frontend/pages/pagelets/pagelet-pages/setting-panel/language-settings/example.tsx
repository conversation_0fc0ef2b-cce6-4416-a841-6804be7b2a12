import React from 'react';
import LanguageSettingsNew from './LanguageSettingsNew';

/**
 * 使用新版语言设置组件的示例
 * 
 * 要在设置面板中使用新版语言设置，请按以下方式导入和使用：
 * 
 * ```tsx
 * import LanguageSettingsNew from './language-settings/LanguageSettingsNew';
 * 
 * // 在设置页面的标签页内容中使用
 * <LanguageSettingsNew />
 * ```
 * 
 * 新版组件特性：
 * 1. 完全按照设计稿重新实现的布局和样式
 * 2. 包含翻译预览卡片，用户可以直观看到翻译效果
 * 3. 改进的交互体验和视觉设计
 * 4. 支持响应式布局和深色模式
 * 5. 保持与原有API的兼容性
 */
const LanguageSettingsExample: React.FC = () => {
    return (
        <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
            <div style={{ 
                backgroundColor: 'white', 
                borderRadius: '8px', 
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                overflow: 'hidden'
            }}>
                <div style={{ 
                    padding: '20px 24px', 
                    borderBottom: '1px solid #eee',
                    fontSize: '18px',
                    fontWeight: 600,
                    color: '#111925'
                }}>
                    语言设置
                </div>
                <div style={{ padding: '24px 0' }}>
                    <LanguageSettingsNew />
                </div>
            </div>
        </div>
    );
};

export default LanguageSettingsExample;
