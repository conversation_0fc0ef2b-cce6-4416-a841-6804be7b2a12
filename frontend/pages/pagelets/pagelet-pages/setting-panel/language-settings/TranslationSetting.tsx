import {Select} from '@ss/mtd-react';
import React from 'react';
import {i18n} from '@lib/i18n';
import {useAsyncState} from '@components/hooks';
import {LANGUAGE_MAP, type ITranslationLanguage} from '@shared/common/I18n';

import {translateLanguage, getTranslateLanguage} from '../apis';
import imsdkClient from '../apis/imsdkClient';
import LanguageSelect from './LanguageSelect';

const {Option} = Select;

const languageMap = {
    [LANGUAGE_MAP.zh]: {
        value: LANGUAGE_MAP.zh,
        content: '简体中文'
    },
    [LANGUAGE_MAP.zh_HK]: {
        value: LANGUAGE_MAP.zh_HK,
        content: '繁體中文'
    },
    [LANGUAGE_MAP.en]: {
        value: LANGUAGE_MAP.en,
        content: 'English'
    },
    [LANGUAGE_MAP.pt_BR]: {
        value: LANGUAGE_MAP.pt_BR,
        content: 'Português'
    },
    [LANGUAGE_MAP.ar]: {
        value: LANGUAGE_MAP.ar,
        content: 'العربية'
    }
};

const TranslationSetting = (): JSX.Element => {
    const [selectTrans, setSelectTrans] = useAsyncState<ITranslationLanguage>(
        i18n.getTranslationLanguage(),
        async () => {
            const tl = await getTranslateLanguage();
            return tl || i18n.getTranslationLanguage();
        }
    );
    const handleTranslationChange = (lang: ITranslationLanguage) => {
        setSelectTrans(lang);
        translateLanguage(lang);
        // 通知DXSDK翻译语言变化
        imsdkClient.call('account.dxSetTranslateLanguage', [{language: lang}]);
    };

    return (
        <div className="settings-page-section-item-select">
            {i18n.$t('setting_panel_language_translate_the_content_into', '将内容翻译为')}
            {/* <Select
                className="language-setting-select"
                value={selectTrans}
                optionLabelProp="originOption"
                onChange={option => handleTranslationChange(option.value)}
                popLayer={{width: 'auto', getContainer: () => document.body, className: 'language-setting-popup'}}
                filterable={false}
                clearable={false}
            >
                {Object.values(languageMap).map(({value, content}) => (
                    <Option key={content} value={value} originOption={content}>
                        <span className="lan-option-content">{content}</span>
                    </Option>
                ))}
            </Select> */}
            <LanguageSelect value={selectTrans} languageMap={languageMap} onChange={handleTranslationChange} />
        </div>
    );
};

export default TranslationSetting;
