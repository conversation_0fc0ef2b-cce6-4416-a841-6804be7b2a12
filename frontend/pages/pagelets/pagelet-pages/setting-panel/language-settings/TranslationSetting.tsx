import React from 'react';
import {i18n} from '@lib/i18n';
import {useAsyncState} from '@components/hooks';
import {LANGUAGE_MAP, type ITranslationLanguage} from '@shared/common/I18n';

import {translateLanguage, getTranslateLanguage} from '../apis';
import imsdkClient from '../apis/imsdkClient';

import SelectSettingItem from './SelectSettingItem';

const languageMap = [
    {
        value: LANGUAGE_MAP.zh,
        content: '简体中文'
    },
    {
        value: LANGUAGE_MAP.zh_HK,
        content: '繁體中文'
    },
    {
        value: LANGUAGE_MAP.en,
        content: 'English'
    },
    {
        value: LANGUAGE_MAP.pt_BR,
        content: 'Português'
    },
    {
        value: LANGUAGE_MAP.ar,
        content: 'العربية'
    }
];

const TranslationSetting = (): JSX.Element => {
    const [selectTrans, setSelectTrans] = useAsyncState<ITranslationLanguage>(
        i18n.getTranslationLanguage(),
        async () => {
            const tl = await getTranslateLanguage();
            return tl || i18n.getTranslationLanguage();
        }
    );
    const handleTranslationChange = (lang: ITranslationLanguage) => {
        setSelectTrans(lang);
        translateLanguage(lang);
        // 通知DXSDK翻译语言变化
        imsdkClient.call('account.dxSetTranslateLanguage', [{language: lang}]);
    };

    return (
        <div className="settings-page-section-item-select">
            {i18n.$t('setting_panel_language_translate_the_content_into', '将收到的消息翻译为')}
            <SelectSettingItem value={selectTrans} optionsArray={languageMap} onChange={handleTranslationChange} />
        </div>
    );
};

export default TranslationSetting;
