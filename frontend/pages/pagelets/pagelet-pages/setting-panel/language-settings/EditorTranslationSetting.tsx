import React, {useEffect} from 'react';
import {i18n} from '@lib/i18n';
import {useAsyncState} from '@components/hooks';
import {LANGUAGE_MAP, type ITranslationLanguage} from '@shared/common/I18n';
import {TranslateActionConfig} from '@xm/electron-imsdk-types';

import {dxConfigManager} from '../apis';

import SelectSettingItem from './SelectSettingItem';

const languageMap = [
    {
        value: LANGUAGE_MAP.zh,
        content: i18n.$t('im_translate_language_zh', '简体中文')
    },
    {
        value: LANGUAGE_MAP.zh_HK,
        content: i18n.$t('im_translate_language_zh_hk', '繁体中文')
    },
    {
        value: LANGUAGE_MAP.en,
        content: i18n.$t('im_translate_language_en', '英文')
    },
    {
        value: LANGUAGE_MAP.pt_BR,
        content: i18n.$t('im_translate_language_pt_br', '葡萄牙语')
    }
];

const usageMap = [
    {
        value: 'replace',
        content: i18n.$t('im_replace_translation_to_editor', '替换'),
        contentTip: i18n.$t('im_replace_translation_to_editor_tip', '译文替换输入框内容')
    },
    {
        value: 'insert',
        content: i18n.$t('im_insert_translation_to_editor', '插入'),
        contentTip: i18n.$t('im_insert_translation_to_editor_tip', '译文插入到输入框内容后')
    }
];

export const EditorTranslationLanguage = (): JSX.Element => {
    const [selectTrans, setSelectTrans] = useAsyncState<ITranslationLanguage>(LANGUAGE_MAP.en, () =>
        dxConfigManager.dxGetConfig('globalInputTranslate')
    );

    useEffect(() => {
        dxConfigManager.on((res: {data: {[key: string]: any; isDelete: boolean}}) => {
            if ('globalInputTranslate' in (res?.data || {})) {
                setSelectTrans(res.data.globalInputTranslate);
            }
        });
    }, []);

    const handleTranslationChange = async (lang: ITranslationLanguage) => {
        const preTranslationLan = selectTrans;
        setSelectTrans(lang);
        try {
            const {isSuccess} = await dxConfigManager.dxSetConfig({globalInputTranslate: lang});
            !isSuccess && setSelectTrans(preTranslationLan);
        } catch (error) {
            setSelectTrans(preTranslationLan);
        }
    };

    return (
        <div className="settings-page-section-item-select">
            {i18n.$t('setting_panel_editor_translate_language', '将输入的内容默认翻译为')}
            <SelectSettingItem value={selectTrans} optionsArray={languageMap} onChange={handleTranslationChange} />
        </div>
    );
};

export const EditorTranslationUsage = (): JSX.Element => {
    const [translateUsage, setTranslateUsage] = useAsyncState<TranslateActionConfig>(
        TranslateActionConfig.REPLACE,
        () => dxConfigManager.dxGetConfig('globalInputTranslateOperation')
    );

    useEffect(() => {
        dxConfigManager.on((res: {data: {[key: string]: any; isDelete: boolean}}) => {
            if ('globalInputTranslateOperation' in (res?.data || {})) {
                setTranslateUsage(res.data.globalInputTranslateOperation);
            }
        });
    }, []);

    const handleChange = async (operation: TranslateActionConfig) => {
        const preConfigValue = translateUsage;
        setTranslateUsage(operation);
        try {
            const {isSuccess} = await dxConfigManager.dxSetConfig({globalInputTranslateOperation: operation});
            !isSuccess && setTranslateUsage(preConfigValue);
        } catch (error) {
            setTranslateUsage(preConfigValue);
        }
    };

    return (
        <div className="settings-page-section-item-select">
            {i18n.$t('setting_panel_editor_translate_usage', '译文默认使用方式')}
            <SelectSettingItem value={translateUsage} optionsArray={usageMap} onChange={handleChange} />
        </div>
    );
};
