import {Radio} from '@ss/mtd-react';
import React, {useEffect} from 'react';
import {i18n} from '@lib/i18n';
import {useAsyncState} from '@components/hooks';

import {dxConfigManager} from '../apis';

const TimezoneSetting = (): JSX.Element => {
    const [showTimezone, setShowTimezone] = useAsyncState('OPEN', () => dxConfigManager.dxGetConfig('showTimeZone'));
    const currentLanguage = i18n.getLanguage();
    const imagePath = currentLanguage === 'zh' || currentLanguage === 'zh-HK' ? 'dxpc:///img/zh-HK/' : 'dxpc://img/en/';

    useEffect(() => {
        dxConfigManager.on((res: {data: {[key: string]: any; isDelete: boolean}}) => {
            if ('showTimeZone' in (res?.data || {})) {
                setShowTimezone(res.data.showTimeZone);
            }
        });
    }, []);

    const handleTimezoneChange = (value: 'OPEN' | 'CLOSE') => {
        setShowTimezone(value);
        dxConfigManager.dxSetConfig({showTimeZone: value});
    };

    return (
        <div className="settings-page-section-item-radio">
            <div>{i18n.$t('setting_panel_timezone', '对外展示时区')}</div>
            <span className="timezone-setting-notice">
                {i18n.$t(
                    'setting_panel_lab_timezone_description',
                    '跨时区沟通时，对方可在会话、日历等位置看到我的时区。关闭后，我的时区信息不对外展示。'
                )}
            </span>
            <Radio.Group onChange={handleTimezoneChange} value={showTimezone} className="timezone-setting">
                <Radio value="OPEN">
                    <div className="timezone-setting-option-content">
                        <img src={`${imagePath}<EMAIL>`} className="timezone-setting-option-image" />
                        <div className="timezone-setting-radio-text">
                            {i18n.$t('setting_panel_lab_timezone_show', '展示时区')}
                        </div>
                    </div>
                </Radio>
                <Radio value="CLOSE">
                    <div className="timezone-setting-option-content">
                        <img src={`${imagePath}<EMAIL>`} className="timezone-setting-option-image" />
                        <div className="timezone-setting-radio-text">
                            {i18n.$t('setting_panel_lab_timezone_none', '不展示时区')}
                        </div>
                    </div>
                </Radio>
            </Radio.Group>
        </div>
    );
};

export default TimezoneSetting;
