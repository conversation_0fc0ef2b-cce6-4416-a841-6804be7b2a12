import React, {useEffect, useState} from 'react';
import {i18n} from '@lib/i18n';
import {useAsyncState} from '@components/hooks';

import {createLogger, createUserStorage} from '#preload/internal';

import {dxConfigManager} from '../apis';
import TooltipWithIcon from '../components/TooltipWithIcon';

import LanguageSetting from './LanguageSetting';
import TranslationSetting from './TranslationSetting';
import TimezoneSetting from './TimezoneSetting';
import AutoTranslationSetting from './AutoTranslationSetting';
import {EditorTranslationLanguage, EditorTranslationUsage} from './EditorTranslationSetting';

import './index.scss';

const logger = createLogger('session-filter-setting');

/**
 * 设置页面中的「常规」页面内容框架
 * @returns {JSX.Element} 该部分内容的 JSX
 */
const LanguageSettings = (): JSX.Element => {
    const [showTimezoneSwitch] = useAsyncState(false, () =>
        dxConfigManager.dxGetConfig('blacklist').then(blacklist => {
            return !blacklist.includes('TimeZone');
        })
    );
    const [showAutoTranslate, setShowAutoTranslate] = useState(false);

    useEffect(() => {
        const userStorage = createUserStorage('userLocalConfig');
        userStorage
            .get('default')
            .then(data => {
                setShowAutoTranslate(data?.grayConfig?.auto_translate || false);
            })
            .catch(() => {
                logger.error('fetchGrayConfig error');
            });
    }, []);

    return (
        <div className="settings-page-wrapper modified language-timezone-settings">
            <div className="settings-page-group">
                <div className="language-content no-underline">
                    <div className="settings-page-section-title">
                        {i18n.$t('setting_panel_language_settings', '语言设置')}
                    </div>
                    <div className="settings-page-section-item">
                        <LanguageSetting />
                    </div>
                    <>
                        <div className="settings-page-section-item-title">
                            <TooltipWithIcon src="dxpc:///img/message_translate_config_guide.png" size="middle">
                                {i18n.$t('setting_panel_language_translate_message', '翻译收到的消息')}
                            </TooltipWithIcon>
                        </div>
                        <div className="settings-page-section-item">
                            <TranslationSetting />
                            {showAutoTranslate && <AutoTranslationSetting />}
                        </div>
                    </>
                    <>
                        <div className="settings-page-section-item-title">
                            <TooltipWithIcon src="dxpc:///img/editor_translate_config_guide.png" size="middle">
                                {i18n.$t('setting_panel_language_editor_translate', '边写边译')}
                            </TooltipWithIcon>
                            <div className="settings-page-section-item-title-tips">
                                {i18n.$t(
                                    'setting_panel_language_editor_translate_title_tips',
                                    '所有会话保持相同的默认设置，会话内设置变更后不受此设置控制。'
                                )}
                            </div>
                        </div>
                        <div className="settings-page-section-item">
                            <EditorTranslationLanguage />
                            <EditorTranslationUsage />
                        </div>
                    </>
                </div>
            </div>
            {showTimezoneSwitch && (
                <div className="settings-page-group">
                    <div className="settings-page-group-content timezone-content no-underline">
                        <div className="settings-page-section-title">
                            {i18n.$t('setting_panel_timezone_setting', '时区设置')}
                        </div>
                        <div className="settings-page-section-item">
                            <TimezoneSetting />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default LanguageSettings;
