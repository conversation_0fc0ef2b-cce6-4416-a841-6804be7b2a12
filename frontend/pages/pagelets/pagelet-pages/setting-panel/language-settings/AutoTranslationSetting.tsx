import React, {useEffect} from 'react';
import SwitchLine from '@frontend/components/SwitchLine';
import {i18n} from '@frontend/lib/i18n';
import {useAsyncState} from '@frontend/components/hooks';

import TooltipWithIcon from '../components/TooltipWithIcon';
import {dxConfigManager} from '../apis';

const AutoTranslationSetting = (): JSX.Element => {
    const [autoTranslate, setAutoTranslate] = useAsyncState('CLOSE', () =>
        dxConfigManager.dxGetConfig('globalAutoTranslate')
    );
    const currentLanguage = i18n.getLanguage();

    useEffect(() => {
        dxConfigManager.on((res: {data: {[key: string]: any; isDelete: boolean}}) => {
            if ('globalAutoTranslate' in (res?.data || {})) {
                setAutoTranslate(res.data.globalAutoTranslate);
            }
        });
    }, []);

    const getImagePath = () => {
        switch (currentLanguage) {
            case 'zh':
                return 'dxpc:///img/';
            case 'zh-HK':
                return 'dxpc:///img/zh-HK/';
            case 'en':
                return 'dxpc://img/en/';
            default:
                return 'dxpc://img/en/';
        }
    };

    const handleAutoTranslateChange = async (value: boolean) => {
        const configValue = value ? 'OPEN' : 'CLOSE';
        setAutoTranslate(configValue);
        try {
            const {isSuccess} = await dxConfigManager.dxSetConfig({globalAutoTranslate: configValue});
            !isSuccess && setAutoTranslate(value ? 'CLOSE' : 'OPEN');
        } catch (error) {
            setAutoTranslate(value ? 'CLOSE' : 'OPEN');
        }
    };

    return (
        <div className="auto-translation-setting settings-page-section-item-switch">
            <SwitchLine checked={autoTranslate === 'OPEN'} onChange={handleAutoTranslateChange}>
                {i18n.$t('setting_panel_language_auto_translate', '自动翻译聊天中收到的消息')}
            </SwitchLine>
            <div className="setting-item">
                <span className="auto-translate-tips">
                    {i18n.$t('setting_panel_auto_translate_tips', '开启后所有会话聊天收到的文字消息会被自动翻译，')}
                    <TooltipWithIcon
                        src={`${getImagePath()}auto_translate_config_guide.gif`}
                        size="middle"
                        placement="top"
                    >
                        {i18n.$t('setting_panel_auto_translate_session_setting', '单独设置')}
                    </TooltipWithIcon>
                    {i18n.$t('setting_panel_auto_translate_tips_2', '自动翻译的会话不受此开关控制。')}
                </span>
            </div>
        </div>
    );
};

export default AutoTranslationSetting;
