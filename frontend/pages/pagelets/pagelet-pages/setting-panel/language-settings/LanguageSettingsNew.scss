.language-settings-new {
    padding: 0 126px;
    max-width: 800px;

    // 设置区块
    .setting-section {
        margin-bottom: 40px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    // 区块标题
    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #111925;
        margin: 0 0 16px 0;
        display: flex;
        align-items: center;
        gap: 8px;

        .info-icon {
            font-size: 14px;
            color: #999;
            cursor: help;
        }
    }

    // 区块描述
    .section-description {
        font-size: 12px;
        color: rgba(17, 25, 35, 0.45);
        margin: 0 0 16px 0;
        line-height: 18px;
    }

    // 设置项
    .setting-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        &.switch-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 24px;
        }
    }

    // 设置项标签
    .setting-label {
        display: block;
        font-size: 14px;
        color: #111925;
        margin-bottom: 8px;
        font-weight: 500;
    }

    // 设置项描述
    .setting-description {
        font-size: 12px;
        color: rgba(17, 25, 35, 0.45);
        line-height: 18px;
        margin: 0;

        .link-text {
            color: #166ff7;
            cursor: pointer;
            text-decoration: underline;
        }
    }

    // 开关内容区域
    .switch-content {
        flex: 1;
    }

    // 自动翻译开关
    .auto-translate-switch {
        flex-shrink: 0;
        margin-top: 4px;
    }

    // 翻译预览区域
    .translation-preview-section {
        margin-top: 16px;
    }

    .preview-radio-group {
        display: flex;
        gap: 24px;
        padding: 0;

        .mtd-radio-group-item {
            margin: 0;
        }
    }

    .preview-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    // 消息预览卡片
    .message-preview-card {
        width: 240px;
        padding: 16px;
        border: 1px solid rgba(17, 25, 37, 0.1);
        border-radius: 8px;
        background: #fff;

        &.selected {
            border-color: #166ff7;
            box-shadow: 0 0 0 1px #166ff7;
        }
    }

    .message-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f0f6ff;
            border: 1px solid rgba(17, 25, 37, 0.1);
        }

        .username {
            font-size: 14px;
            color: #111925;
            font-weight: 500;
        }
    }

    .message-content {
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        line-height: 20px;

        &.original,
        &.translated {
            background: #f0f6ff;
            color: #111925;
        }
    }

    // 预览单选按钮
    .preview-radio {
        .mtd-radio-label {
            font-size: 14px;
            color: #111925;
        }

        .mtd-radio-inner {
            border-color: #166ff7;
        }

        &.mtd-radio-checked .mtd-radio-inner {
            background-color: #166ff7;
            border-color: #166ff7;
        }
    }

    // 使用方式下拉框
    .usage-mode-dropdown {
        position: relative;
        display: inline-block;
        margin-bottom: 16px;

        .usage-select {
            width: 208px;
            height: 32px;
            padding: 0 32px 0 12px;
            border: 1px solid rgba(17, 25, 37, 0.15);
            border-radius: 6px;
            font-size: 14px;
            color: #111925;
            background: #fff;
            appearance: none;
            cursor: pointer;

            &:focus {
                outline: none;
                border-color: #166ff7;
                box-shadow: 0 0 0 2px rgba(22, 111, 247, 0.1);
            }
        }

        .dropdown-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: rgba(17, 25, 37, 0.65);
            pointer-events: none;
        }
    }

    // 使用方式说明
    .usage-explanation {
        border: 1px solid rgba(17, 25, 37, 0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    .usage-option-detail {
        padding: 12px 16px;
        border-bottom: 1px solid rgba(17, 25, 37, 0.05);

        &:last-child {
            border-bottom: none;
        }

        .option-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            .option-name {
                font-size: 14px;
                color: #111925;
                font-weight: 500;
            }

            .check-icon {
                color: #166ff7;
                font-size: 16px;
                font-weight: bold;
            }
        }

        .option-description {
            font-size: 12px;
            color: rgba(17, 25, 35, 0.45);
            margin: 0;
            line-height: 18px;
        }
    }

    // 语言选择下拉框样式覆盖
    .language-setting-select,
    .translation-setting-new {
        width: 208px;

        .mtd-select-filter {
            height: 32px;
            border: 1px solid rgba(17, 25, 37, 0.15);
            border-radius: 6px;

            .mtd-select-filter-label {
                padding: 0 32px 0 12px;
                line-height: 32px;
                font-size: 14px;
                color: #111925;
            }

            .mtd-select-filter-icon {
                right: 12px;
                line-height: 32px;

                .mtdicon-down-thick {
                    color: rgba(17, 25, 37, 0.65);
                    font-size: 12px;
                }
            }
        }

        &:hover .mtd-select-filter {
            border-color: #166ff7;
        }
    }

    // 时区设置样式
    .timezone-setting {
        margin-top: 16px;

        .mtd-radio-group {
            display: flex;
            gap: 16px;
            padding: 0;

            .mtd-radio.mtd-radio-group-item {
                height: 157px;
                width: 208px;
                border-radius: 10px;
                border: 1px solid rgba(17, 25, 37, 0.05);
                margin: 0;
                position: relative;

                &.mtd-radio-checked {
                    border-color: #166ff7;
                    box-shadow: 0 0 0 1px #166ff7;
                }

                .mtd-radio-input-wrapper {
                    position: absolute;
                    bottom: 11px;
                    left: 11px;
                }

                .mtd-radio-label {
                    padding: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                }
            }
        }

        .timezone-setting-option-image {
            width: 100%;
            height: 116px;
            background-color: #f3f7ff;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #fff;
                border: 1px solid rgba(17, 25, 37, 0.1);
                margin-bottom: 8px;
            }

            .username {
                font-size: 12px;
                color: #111925;
            }

            .time-display {
                font-size: 10px;
                color: rgba(17, 25, 35, 0.45);
                margin-top: 4px;
            }
        }

        .timezone-setting-radio-text {
            font-size: 14px;
            height: 35px;
            padding: 8px 35px 8px 12px;
            line-height: 19px;
            color: #111925;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 0 24px;

        .preview-radio-group {
            flex-direction: column;
            gap: 16px;
        }

        .message-preview-card {
            width: 100%;
            max-width: 300px;
        }

        .setting-item.switch-item {
            flex-direction: column;
            gap: 16px;
        }

        .timezone-setting .mtd-radio-group {
            flex-direction: column;

            .mtd-radio.mtd-radio-group-item {
                width: 100%;
                max-width: 300px;
            }
        }
    }

    // 深色模式支持
    @media (prefers-color-scheme: dark) {
        .section-title {
            color: #fff;
        }

        .setting-label {
            color: #fff;
        }

        .message-preview-card {
            background: #2a2a2a;
            border-color: rgba(255, 255, 255, 0.1);

            .username {
                color: #fff;
            }

            .message-content {
                background: rgba(255, 255, 255, 0.05);
                color: #fff;
            }
        }

        .usage-explanation {
            border-color: rgba(255, 255, 255, 0.1);
            background: #2a2a2a;

            .option-name {
                color: #fff;
            }
        }

        .usage-select {
            background: #2a2a2a;
            border-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
    }
}

// 下拉选择框弹出层样式
.translation-setting-new-popup,
.language-setting-popup {
    width: 208px !important;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(17, 25, 37, 0.1);

    .mtd-select-item {
        padding: 8px 12px;
        margin: 0;
        font-size: 14px;
        color: #111925;

        &:hover {
            background-color: #f0f6ff;
            border-radius: 4px;
        }

        &:first-child {
            margin-top: 4px;
        }

        &:last-child {
            margin-bottom: 4px;
        }
    }

    .mtd-select-item-selected {
        color: #166ff7 !important;
        background-color: #f0f6ff;
        font-weight: 500;

        &::after {
            content: "✓";
            float: right;
            color: #166ff7;
            font-weight: bold;
        }
    }

    .mtd-select-item-focused {
        background-color: transparent !important;

        &:hover {
            background-color: #f0f6ff !important;
        }
    }

    .translation-option-content,
    .lan-option-content {
        display: block;
        width: 100%;
    }
}
