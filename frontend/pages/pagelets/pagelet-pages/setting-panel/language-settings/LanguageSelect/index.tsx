import React from 'react';
import {Select} from '@ss/mtd-react';

import type {ITranslationLanguage, LANGUAGE_MAP, TLocaleLanguage} from '@shared/common/I18n';

const {Option} = Select;

interface LanguageSelectProps {
    value: string;
    languageMap: Record<string, {lanValue: typeof LANGUAGE_MAP; content: string}>;
    onChange: (lang: TLocaleLanguage | ITranslationLanguage) => void;
}

const LanguageSelect = (props: LanguageSelectProps) => {
    const {value, languageMap, onChange} = props;

    return (
        <Select
            className="language-setting-select"
            value={value}
            optionLabelProp="originOption"
            onChange={option => onChange(option.value)}
            popLayer={{width: 'auto', getContainer: () => document.body, className: 'language-setting-popup'}}
            filterable={false}
            clearable={false}
        >
            {Object.values(languageMap).map(({lanValue, content}) => {
                return (
                    <Option key={content} value={lanValue} originOption={content}>
                        <span className="lan-option-content">{content}</span>
                    </Option>
                );
            })}
        </Select>
    );
};

export default LanguageSelect;
