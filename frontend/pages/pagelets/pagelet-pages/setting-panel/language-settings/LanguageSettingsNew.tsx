import React, {useEffect, useState} from 'react';
import {i18n} from '@lib/i18n';
import classnames from 'classnames';
import {useAsyncState} from '@components/hooks';

import {createLogger, createUserStorage} from '#preload/internal';

import {dxConfigManager} from '../apis';

import LanguageSetting from './LanguageSetting';
import TranslationSetting from './TranslationSetting';
import TimezoneSetting from './TimezoneSetting';
import AutoTranslationSetting from './AutoTranslationSetting';

import './index.scss';

const logger = createLogger('session-filter-setting');

/**
 * 设置页面中的「常规」页面内容框架
 * @returns {JSX.Element} 该部分内容的 JSX
 */
const LanguageSettings = (): JSX.Element => {
    const [showTimezoneSwitch] = useAsyncState(false, () =>
        dxConfigManager.dxGetConfig('blacklist').then(blacklist => {
            return !blacklist.includes('TimeZone');
        })
    );
    const [showAutoTranslate, setShowAutoTranslate] = useState(false);

    useEffect(() => {
        const userStorage = createUserStorage('userLocalConfig');
        userStorage
            .get('default')
            .then(data => {
                setShowAutoTranslate(data?.grayConfig?.auto_translate || false);
            })
            .catch(() => {
                logger.error('fetchGrayConfig error');
            });
    }, []);

    return (
        <div className="settings-page-wrapper modified language-timezone-settings">
            <div className="settings-page-group">
                <div
                    className={classnames({
                        'settings-page-group-content': true,
                        'language-content': true,
                        'no-underline': !showTimezoneSwitch
                    })}
                >
                    <div className="settings-page-section-title">
                        {i18n.$t('setting_panel_language_setting', '语言设置')}
                    </div>
                    <div className="settings-page-section-item">
                        <LanguageSetting />
                    </div>
                    <>
                        <div className="settings-page-group-title">
                            {i18n.$t('setting_panel_language_translate_message', '翻译收到的消息')}
                        </div>
                        <div className="settings-page-section-item">
                            <TranslationSetting />
                            {showAutoTranslate && <AutoTranslationSetting />}
                        </div>
                    </>
                    <>
                        <div className="settings-page-group-title">
                            {i18n.$t('setting_panel_language_editor_translate', '边写边译')}
                        </div>
                        <div className="settings-page-section-item">
                            <TranslationSetting />
                            <TranslationSetting />
                        </div>
                    </>
                </div>
            </div>
            {showTimezoneSwitch && (
                <div className="settings-page-group">
                    <div className="settings-page-group-content timezone-content no-underline">
                        <div className="settings-page-section-title">
                            {i18n.$t('setting_panel_timezone_setting', '时区设置')}
                        </div>
                        <div className="settings-page-section-item">
                            <TimezoneSetting />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default LanguageSettings;
