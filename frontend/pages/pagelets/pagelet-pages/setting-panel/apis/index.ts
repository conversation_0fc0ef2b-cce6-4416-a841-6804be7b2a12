import {i18n} from '@lib/i18n';
import {defaultShortcutAccelerator, HornKeyName} from '@shared/common/constants';
import {safeJsonParse} from '@shared/common/safeJsonParse';
import {isUndefinedOrNull} from '@shared/common/is';
import {createDebugLogger} from '@shared/common/debug';
import {showToast} from '@components/Toast';
import {TranslateActionConfig, type SessionTabInfo} from '@xm/electron-imsdk-types';

import {
    hornDynamicConfig,
    env,
    shortcut,
    vcard,
    imsdk,
    localSetting,
    account,
    i18n as i18nApi,
    mainWindow,
    device,
    protocol,
    diagnose,
    windows
} from '#preload/internal';

import imsdkClient from './imsdkClient';

import type {IDXLocalSetting, IHornDxClientConfiguration, IHornDxDesktopConfiguration} from '@shared/types';
import type {DXConfigObject} from '@xm/electron-imsdk/server/IMSDKMethodImpl';
import type {ITranslationLanguage} from '@shared/common/I18n';

const logger = createDebugLogger('setting-panel');

const {translateLanguage} = i18nApi;
const {getTranslateLanguage} = i18nApi;
const {changeLanguage} = i18nApi;
const logout = () => account.logout();
export {logout, mainWindow, shortcut, changeLanguage, translateLanguage, getTranslateLanguage};

// 把这个函数设置为小写字母开头，消除调用时的 eslint 报错
export const lxAnalytics = window.LXAnalytics;

export const selfContext = windows.registerContext('setting-panel');

type ILocalSettingData = IDXLocalSetting;

let localSettingCache: IDXLocalSetting | null = null;
localSetting.subscribeLocalSettingChange();
localSetting.onLocalSettingChange(setting => {
    localSettingCache = setting;
});
export const localSettingApis = {
    async get<T extends keyof ILocalSettingData>(key: T): Promise<ILocalSettingData[T]> {
        if (!localSettingCache) {
            localSettingCache = await localSetting.getLocalSetting();
        }
        return localSettingCache[key];
    },
    set<T extends keyof ILocalSettingData>(key: T, value: ILocalSettingData[T]) {
        localSetting.setLocalSetting(key, value);
        return Promise.resolve();
    },
    onChange: localSetting.onLocalSettingChange
};

// 全局消息提示（设置页面专用）
export const Toast = {
    success: (message: string) => {
        showToast(message, {
            type: 'success',
            duration: 2000,
            singleNotify: true
        });
    },
    error: (message: string) => {
        showToast(message, {
            type: 'error',
            duration: 2000,
            singleNotify: true
        });
    }
};

export const getStartTime = async () => {
    return (await env.getInitialState()).startTime;
};
export const getMyMobile = async () => {
    return (await vcard.getMyDetailVCard())?.mobile ?? '';
};

// 配置接口
interface IDXConfigManager {
    // 快捷键相关
    send_message: string;
    quick_to_top_session: string;
    start_chat: string;
    focus_search: string;
    quick_to_input: string;
    open_setting: string;
    showDaxiang: string;
    screenshotShortcut: string;
    screenRecorder: string;
    showAssistant: string;
    // 其它设置
    soundPc: boolean;
    allNotifyPc: boolean;
    importantSession: boolean;
    // 通知关键词设置
    keyNotice: string[]; // 关键词内容
    keywordNotify: boolean; // 关键词是否生效，作为重要消息通知
    // 重要会话设置
    priorityMsg: 'OPEN' | 'CLOSE'; // 重要消息提醒
    starPersonPriority: boolean; // 星标联系人消息是否重要
    leaderPersonPriority: boolean; // 上级消息是否重要
    // 重要消息设置
    atAllNotify: 'open-only' | 'open-all' | 'close-all' | 'close-only'; // @所有人的消息中，哪些需要被通知
    // 常用语设置
    isFrequentlyReplyEnabled: boolean; // 常用语是否开启
    // 实验室功能
    BaikeSwitch: 'OPEN' | 'CLOSE'; // 美团百科
    QRCodeSwitch: 'OPEN' | 'CLOSE'; // 二维码识别
    pcNewScreenshot: 'OPEN' | 'CLOSE'; // 新版截图
    markdownSupport: 'OPEN' | 'CLOSE'; // markdown解析
    handleLaterOrder: 'msg' | 'mark' | ''; //稍后列表中排序
    /** 是否对外展示时区 */
    showTimeZone: 'OPEN' | 'CLOSE';
    /** 功能黑名单 */
    blacklist: string[];
    /** 全局自动翻译 */
    globalAutoTranslate: 'OPEN' | 'CLOSE';
    globalInputTranslate: ITranslationLanguage; // 全局边写边译翻译语言设置
    globalInputTranslateOperation: TranslateActionConfig; // 全局边写边译译文使用方式
}

type IDXConfigManagerKeys = keyof IDXConfigManager;

const dxConfigManagerDefaultValue: IDXConfigManager = {
    // 快捷键
    // eslint-disable-next-line camelcase
    send_message: 'enterSend',
    ...defaultShortcutAccelerator,
    // 其它设置
    soundPc: true,
    allNotifyPc: true,
    importantSession: true,
    keyNotice: [],
    keywordNotify: false,
    priorityMsg: 'CLOSE',
    starPersonPriority: false,
    leaderPersonPriority: false,
    atAllNotify: 'open-all',
    isFrequentlyReplyEnabled: false,
    BaikeSwitch: 'OPEN',
    QRCodeSwitch: 'CLOSE',
    pcNewScreenshot: 'CLOSE',
    markdownSupport: 'CLOSE',
    handleLaterOrder: 'mark',
    showTimeZone: 'OPEN',
    blacklist: [],
    globalAutoTranslate: 'CLOSE',
    globalInputTranslate: 'en',
    globalInputTranslateOperation: TranslateActionConfig.REPLACE
};

export const dxConfigManager = {
    /**
     * 获取配置
     * @param {K extends IDXConfigManagerKeys} key 需要获取的配置的键值
     * @returns {Promise<IDXConfigManager[K]>} 配置的具体值，含类型
     * @example
     * const keywordsFromConfig = await dxConfigManager.dxGetConfig('keyNotice');
     * keywordsFromConfig === ['123', '456'] // true
     */
    async dxGetConfig<K extends IDXConfigManagerKeys>(key: K): Promise<IDXConfigManager[K]> {
        const client = await imsdk.getClient();
        const config = await client.call('config.dxGetCfg', [{key}]).catch(e => {
            logger(`config.dxGetCfg [%s] error: %O`, key, e);
            return null;
        });

        const value = config?.[key];

        const defaultValue = dxConfigManagerDefaultValue[key];

        // 若值为空，返回默认值
        if (isUndefinedOrNull(value)) {
            logger(`config.dxGetCfg [%s] has no value, return default value [%s]`, key, defaultValue);
            return defaultValue;
        }

        // 检查类型，若该配置的值类型应为 string 则直接返回（这里使用默认值的类型判断配置的类型），否则转换类型
        if (typeof defaultValue === 'string') {
            // 这里强硬地使用断言是因为 dxGetCfg 返回值的联合类型里有 Record<string, never>
            // 为了排除 never 的影响才这么做
            return value as unknown as IDXConfigManager[K];
        }

        // 若转换失败，也返回默认值
        return (safeJsonParse<IDXConfigManager[K]>(value as string) as IDXConfigManager[K] | null) ?? defaultValue;
    },

    /**
     * 设置指定配置值
     * @param config 包含新的值的配置对象
     * @param config.key 键值为 key 的配置值
     * @returns {void}
     * @example
     * await dxConfigManager.dxSetConfig({keyNotice: ['123', '456']});
     */
    async dxSetConfig<K extends IDXConfigManagerKeys>(config: {[key in K]?: IDXConfigManager[key]}, sync = true) {
        const transformedConfig: {[key in IDXConfigManagerKeys]?: string} = {};

        for (const key in config) {
            // 这个 for...in 如果不用 if 包裹来筛选不需要的属性的话，eslint 会报错
            if (!Object.hasOwn(transformedConfig, key)) {
                const value = config[key];
                transformedConfig[key] = typeof value === 'string' ? value : JSON.stringify(value);
            }
        }
        const client = await imsdk.getClient();
        return client.call('config.dxSetCfg', [
            {
                cfg: transformedConfig as Partial<DXConfigObject>,
                sync
            }
        ]);
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async on(cb: (res: {data: {[key: string]: any; isDelete: boolean}}) => void) {
        const client = await imsdk.getClient();
        return client.on('config.dx_config_changed', cb as any);
    },
    async dxSetHightPriority(config: {keys: IDXConfigManagerKeys[]; nestedKeys: string[]}) {
        const {keys, nestedKeys = []} = config;
        const client = await imsdk.getClient();
        const {isSuccess} = await client.call('config.dxSetHighPriorityCfgs', [
            {
                keys,
                nestedKeys
            }
        ]);
        // console.log('13432', isSuccess);
        return isSuccess;
    }
};

type IDynamicConfig = IHornDxClientConfiguration;

const hornClient = hornDynamicConfig.createHornClientByHornKey(HornKeyName.dxClientConfiguration);
export const dynamicConfigApi = {
    getOneConfig<T extends keyof IDynamicConfig>(key: T): Promise<IDynamicConfig[T]> {
        return hornClient.getConfig().then(config => {
            return config[key];
        });
    }
};

const dxDesktopHornClient = hornDynamicConfig.createHornClientByHornKey(HornKeyName.dxDesktopConfiguration);
export const dxDesktopDynamicConfigApi = {
    getOneConfig<T extends keyof IHornDxDesktopConfiguration>(key: T): Promise<IHornDxDesktopConfiguration[T]> {
        return dxDesktopHornClient.getConfig().then(config => {
            return config[key];
        });
    }
};

export const enterCustomerService = () => {
    protocol.openMTDaxiangLink('mtdaxiang://www.meituan.com/helpDesk');
};

export const openDxDiagnose = () => diagnose.showDiagnoseWindow();

export const autoStartConfig = {
    getAutoStart() {
        return device.isAutoStartOnBoot();
    },

    setAutoStart(isAutoStart: boolean) {
        return device.autoStartOnBoot(isAutoStart);
    }
};

export const {createDesktopShortcut} = device;

export type SessionType = 'entire' | 'later' | 'chat' | 'groupChat' | 'pubChat' | 'unRead' | 'custom' | 'atMe';

export enum SessionFilterType {
    All = 'entire',
    Chat = 'chat',
    GroupChat = 'groupChat',
    PubChat = 'pubChat',
    DealWithLater = 'later',
    UnRead = 'unRead',
    AtMe = 'atMe',
    Custom = 'custom'
}

export const DefaultTopFiltersKey = [
    SessionFilterType.All,
    SessionFilterType.DealWithLater,
    SessionFilterType.Custom,
    SessionFilterType.UnRead,
    SessionFilterType.AtMe,
    SessionFilterType.Chat,
    SessionFilterType.GroupChat,
    SessionFilterType.PubChat
];

export const DefaultTopFilters = DefaultTopFiltersKey.map(key => ({key, display: true}));

export const sessionFilterManager = {
    async saveOrder(order: SessionTabInfo[]) {
        return await imsdkClient.call('session.dxSetSessionTabOrder', [{order, type: 'top'}]);
    },
    async loadOrder() {
        const {order} = await imsdkClient
            .call('session.dxGetSessionTabOrder', [{type: 'top'}], {showError: true})
            .catch(() => {
                return {order: []};
            });
        if (!order.length) {
            return DefaultTopFilters;
        }
        return order;
    }
};

// 快捷键名称
export type ILocalHotkeysName =
    | 'quick_to_top_session'
    | 'start_chat'
    | 'focus_search'
    | 'quick_to_input'
    | 'open_setting'
    | 'send_message';
export type IGlobalHotkeysName = 'showDaxiang' | 'screenshotShortcut' | 'screenRecorder' | 'showAssistant';
export type IHotkeysName = ILocalHotkeysName | IGlobalHotkeysName;

// 快捷键对象接口
/**
 * **注释来自老大象**
 * - `name`: 快捷键标识
 * - `type`: 0/1 是否出现在输入框顶部的菜单栏上
 * - `pc_default_key`; 仅出现在PC 侧的快捷键，全局注册的快捷键只有PC有，所以配置项中仅有pc_default_key
 * - `default_key`: web、PC上都有的快捷键，这些快捷键仅在web聚焦的时候可用
 * - `callback`: 快捷键触发时的回调函数
 *
 * globalConfig中将全局快捷键的注册放在壳上，是因为全局快捷键注册使用electron.globalShortcut，remote方法不支持函数类型的参数
 *
 * TODO: type 这个字段在新大象似乎没有用，再观察一段时间，确实没有就下掉吧
 */
export interface IHotkey {
    type: 0 | 1; // ? 改为 displayOnMenu: boolean 会不会好些
    name: IHotkeysName;
    description: string; // 使用 i18n 初始化
    icon?: string;
    isVisible?: () => boolean; // 若没有该属性，则行为同 () => true，即视为 visible
    /**
     * @default false
     */
    disabled?: boolean; // 是否禁用
}

// 注册到本地的快捷键——从老大象中复制来
export const localHotkeys: IHotkey[] = [
    {
        type: 0,
        name: 'quick_to_top_session',
        description: i18n.$t('hotkeys_quick_to_top_session', '快速定位到会话列表顶部'),
        isVisible: () => {
            // 本期暂时不做 2023/11/23
            return false;
        }
    },
    {
        type: 0,
        name: 'start_chat',
        description: i18n.$t('hotkeys_start_chat', '创建群组')
    },
    {
        type: 0,
        name: 'focus_search',
        description: i18n.$t('hotkeys_focus_search', '聚焦搜索')
    },
    {
        type: 0,
        name: 'quick_to_input',
        description: i18n.$t('hotkeys_quick_to_input', '快速定位到输入框')
    },
    {
        type: 0,
        name: 'open_setting',
        description: i18n.$t('hotkeys_open_setting', '打开设置')
    },
    {
        type: 0,
        name: 'send_message',
        description: i18n.$t('hotkeys_send_message', '发送消息')
    }
];

// 全局快捷键——从老大象中复制来
export const globalHotkeys: IHotkey[] = [
    {
        name: 'showDaxiang',
        type: 0,
        description: i18n.$t('hotkeys_show_daxiang', '显示/隐藏主面板')
    },
    {
        name: 'screenshotShortcut',
        type: 1,
        icon: 'screenshot',
        description: i18n.$t('hotkeys_screenshot', '截图')
        // isVisible: () => false
    },
    {
        name: 'showAssistant',
        type: 0,
        description: i18n.$t('hotkeys_show_ai_search', 'AI 搜索')
    }
];

export const sendMessageHotkeyOptions = {
    enterSend: 'enterSend',
    enterWrap: 'enterWrap'
} as const;

export type ISendMessageHotkeyOptionValue = keyof typeof sendMessageHotkeyOptions;

const getSendMessageHotKeyTextI18n = (sendKey: string, lineBreakKey: string) =>
    i18n.$t('setting_panel_hotkeys_message_send', '{{sendKey}}发送/{{lineBreakKey}}换行', {
        sendKey,
        lineBreakKey
    });

export const getSendMessageHotKeySettingText = (isMac: boolean) =>
    isMac
        ? {
              [sendMessageHotkeyOptions.enterSend]: getSendMessageHotKeyTextI18n('Enter', 'Command+Enter'),
              [sendMessageHotkeyOptions.enterWrap]: getSendMessageHotKeyTextI18n('Command+Enter', 'Enter')
          }
        : {
              [sendMessageHotkeyOptions.enterSend]: getSendMessageHotKeyTextI18n('Enter', 'Ctrl+Enter'),
              [sendMessageHotkeyOptions.enterWrap]: getSendMessageHotKeyTextI18n('Ctrl+Enter', 'Enter')
          };

export type ILabItemId =
    | 'BaikeSwitch'
    | 'QRCodeSwitch'
    | 'preview'
    | 'pcNewScreenshot'
    | 'markdownSupport'
    | 'priorityMsg';

export interface ILabItem {
    id: ILabItemId; // 功能 id
    title: string; // 功能标题
    logo: string; // 功能的 logo 的 url
    introduce: [string, string]; // [功能关闭时的文字描述, 功能开启时的文字描述]
    isDefaultShow: boolean; // 该功能是否默认显示
    isActualShow?: () => Promise<boolean>; // 该功能实际是否显示，若无该属性，则是否显示取决于 isDefaultShow
    onSwitch: (opened: boolean) => Promise<boolean>; // 该实验室功能开闭时调用的回调，返回值表示操作是否成功
    defaultOpen?: boolean;
}

// 复制自老大象（client/component/other/settingSlidePanel/laboratory/index.js）
export const getPCLabItem = (): ILabItem[] => {
    return [
        {
            id: 'BaikeSwitch',
            title: i18n.$t('setting_panel_laboratory_mt_wiki', '提示美团百科词条'),
            logo: 'https://s3plus.meituan.net/v1/mss_de9bbaffb6644e81b118621ea8fc00cf/assets/laboratory_baike%403x.png',
            introduce: [
                i18n.$t('setting_panel_lab_mt_wiki_off_description', '打开开关后，气泡页展示文字时会自动提示百科词条'),
                i18n.$t('setting_panel_lab_mt_wiki_on_description', '打开开关后，气泡页展示文字时会自动提示百科词条')
            ],
            isDefaultShow: true,
            defaultOpen: true,
            async onSwitch(opened: boolean) {
                try {
                    await dxConfigManager.dxSetConfig({BaikeSwitch: opened ? 'OPEN' : 'CLOSE'});
                    return true;
                } catch (error) {
                    return false;
                }
            }
        },
        // {
        //     id: 'markdownSupport',
        //     title: i18n.$t('setting_panel_laboratory_markdown_support', '支持类 Markdown 语法'),
        //     logo: 'https://s3plus.meituan.net/static-page/icons/markdown-lab-icon.png',
        //     introduce: [
        //         i18n.$t(
        //             'setting_panel_lab_markdown_support_off_description',
        //             '开启后，消息渲染支持类 Markdown 语法。（包括加粗、斜体、中划线、行内代码等语法）'
        //         ),
        //         i18n.$t(
        //             'setting_panel_lab_markdown_support_on_description',
        //             '开启后，消息渲染支持类 Markdown 语法。（包括加粗、斜体、中划线、行内代码等语法）'
        //         )
        //     ],
        //     isDefaultShow: true,
        //     defaultOpen: false,
        //     async onSwitch(opened: boolean) {
        //         try {
        //             await dxConfigManager.dxSetConfig({markdownSupport: opened ? 'OPEN' : 'CLOSE'});
        //             return true;
        //         } catch (error) {
        //             return false;
        //         }
        //     }
        // },
        {
            id: 'priorityMsg',
            title: i18n.$t('setting_panel_laboratory_priority_title', '重要消息加强提醒'),
            logo: 'https://s3plus.meituan.net/dx-desktop-update-assets-old/laboratory_important.png',
            introduce: [
                i18n.$t(
                    'setting_panel_lab_priority_off_description',
                    '打开开关后，如命中关键字、@规则，消息/会话会有特殊⚡️标识'
                ),
                i18n.$t(
                    'setting_panel_lab_priority_on_description',
                    '打开开关后，如命中关键字、@规则，消息/会话会有特殊⚡️标识'
                )
            ],
            isDefaultShow: true,
            defaultOpen: false,
            async onSwitch(opened: boolean) {
                try {
                    await dxConfigManager.dxSetConfig({priorityMsg: opened ? 'OPEN' : 'CLOSE'});
                    return true;
                } catch (error) {
                    return false;
                }
            }
        }
        // {
        //     id: 'QRCodeSwitch',
        //     title: i18n.$t('setting_panel_laboratory_qrcode_recognition', '二维码自动识别'),
        //     logo: 'dxpc://img/qrcode_logo.png',
        //     introduce: [
        //         i18n.$t(
        //             'setting_panel_lab_qrcode_off_description',
        //             '打开开关后，鼠标悬停在消息中的二维码图像上时将自动识别'
        //         ),
        //         i18n.$t(
        //             'setting_panel_lab_qrcode_on_description',
        //             '打开开关后，鼠标悬停在消息中的二维码图像上时将自动识别'
        //         )
        //     ],
        //     isDefaultShow: true,
        //     defaultOpen: false,
        //     async onSwitch(opened: boolean) {
        //         try {
        //             await dxConfigManager.dxSetConfig({QRCodeSwitch: opened ? 'OPEN' : 'CLOSE'});
        //             return true;
        //         } catch (error) {
        //             return false;
        //         }
        //     }
        // }
        // {
        //     id: 'markdownSupport',
        //     title: i18n.$t('setting_panel_laboratory_markdown_support', '支持类 Markdown 语法'),
        //     logo: 'https://s3plus.meituan.net/static-page/icons/markdown-lab-icon.png',
        //     introduce: [
        //         i18n.$t(
        //             'setting_panel_lab_markdown_support_off_description',
        //             '开启后，消息渲染支持类 Markdown 语法。（包括加粗、斜体、中划线、行内代码等语法）'
        //         ),
        //         i18n.$t(
        //             'setting_panel_lab_markdown_support_on_description',
        //             '开启后，消息渲染支持类 Markdown 语法。（包括加粗、斜体、中划线、行内代码等语法）'
        //         )
        //     ],
        //     isDefaultShow: true,
        //     defaultOpen: false,
        //     async onSwitch(opened: boolean) {
        //         try {
        //             await dxConfigManager.dxSetConfig({markdownSupport: opened ? 'OPEN' : 'CLOSE'});
        //             return true;
        //         } catch (error) {
        //             return false;
        //         }
        //     }
        // }
        // {
        //     id: 'pcNewScreenshot',
        //     title: i18n.$t('setting_panel_laboratory_new_screenshot', '体验新版截屏工具'),
        //     logo: 'https://s3plus.meituan.net/dx-desktop-update-assets-old/icons/lab-screenshot.png',
        //     introduce: [
        //         i18n.$t('setting_panel_lab_new_screenshot_off_description', '相比旧有截图工具，功能更丰富'),
        //         i18n.$t('setting_panel_lab_new_screenshot_on_description', '相比旧有截图工具，功能更丰富')
        //     ],
        //     isDefaultShow: true,
        //     async onSwitch(opened: boolean) {
        //         try {
        //             await dxConfigManager.dxSetConfig({pcNewScreenshot: opened ? 'OPEN' : 'CLOSE'});
        //             return true;
        //         } catch (error) {
        //             return false;
        //         }
        //     }
        // }
    ];
};
