import React, {useCallback, useEffect, useRef, useState, type PropsWithChildren, lazy, Suspense} from 'react';
import {Tabs} from '@ss/mtd-react';
import {DxIcon3 as Icon} from '@components/DxIcon3';
import {i18n} from '@lib/i18n';
import {Watermark} from '@components/Watermark';

import {report, i18n as i18nApi} from '#preload/internal';

import {selfContext} from './apis';

import type {ITranslationLanguage} from '@shared/common/I18n';
import type {ISettingPageTabName} from '@shared/types';
import type {PageletComponentProps} from '../../utils/PageletRenderer';

import './index.scss';
import './page.scss';

// 使用React.lazy懒加载各个设置组件
const GeneralSettings = lazy(() => import('./general-settings'));
const NotificationSettings = lazy(() => import('./notification-settings'));
const LanguageSettings = lazy(() => import('./language-settings/LanguageSettingsNew'));
// const SessionStyleSettings = lazy(() => import('./session-style-settings'));
const SessionFilterSettings = lazy(() => import('./session-filter-settings'));
const HotkeySettings = lazy(() => import('./hot-keys-settings'));
const FrequentReplySettings = lazy(() => import('./frequent-reply-settings'));
const LaboratorySettings = lazy(() => import('./laboratory'));
const AboutSettings = lazy(() => import('./about-settings'));

const {TabPane} = Tabs;

type TabLabelProps = PropsWithChildren<{
    iconType: string;
}>;

/**
 * 渲染指定的标签页标题
 * @param {TabLabelProps} props 组件属性
 * @param {string} props.iconType 标签页标题的 icon
 * @param {string} props.text 标签页标题
 * @returns {JSX.Element} 需要渲染的标题 JSX
 */
const TabLabel = ({iconType, children = undefined}: TabLabelProps): JSX.Element => {
    return (
        <span className="setting-tabs-item">
            <Icon type={iconType} className="setting-tabs-icon" />
            {children}
        </span>
    );
};

// 类型守卫
const isTabNameValid = (tabName: string): tabName is ISettingPageTabName =>
    [
        'general',
        'notification',
        'language',
        // 'sessionStyle',
        'sessionFilter',
        'hotkeys',
        'frequentReply',
        'nav',
        'laboratory',
        'about'
    ].includes(tabName);

// 加载状态组件
const LoadingFallback = () => (
    <div className="setting-loading">
        <div className="setting-loading-spinner" />
    </div>
);

// 预加载组件的函数
const preloadComponent = (importFn: () => Promise<any>) => {
    importFn();
};

export default function SettingPanel(props: PageletComponentProps): JSX.Element {
    const {viewStartTime} = props;
    const hasReportRef = useRef(false);
    const [tabName, setTabName] = useState<ISettingPageTabName>('general');
    // 「会话筛选」页的编辑状态控制
    const [isEditing, setIsEditing] = useState(false);
    const [scrollToApp, setScrollToApp] = useState<string>('');

    // 预加载相邻标签页的组件
    const preloadAdjacentTabs = useCallback((currentTab: ISettingPageTabName) => {
        const tabOrder: ISettingPageTabName[] = [
            'general',
            'nav',
            'notification',
            'language',
            'sessionFilter',
            'hotkeys',
            'frequentReply',
            'laboratory',
            'about'
        ];

        const currentIndex = tabOrder.indexOf(currentTab);
        if (currentIndex !== -1) {
            // 预加载前一个和后一个标签页
            if (currentIndex > 0) {
                const prevTab = tabOrder[currentIndex - 1];
                preloadTabComponent(prevTab);
            }
            if (currentIndex < tabOrder.length - 1) {
                const nextTab = tabOrder[currentIndex + 1];
                preloadTabComponent(nextTab);
            }
        }
    }, []);

    // 根据标签名预加载对应组件
    const preloadTabComponent = useCallback((tab: ISettingPageTabName) => {
        switch (tab) {
            case 'general':
                preloadComponent(() => import('./general-settings'));
                break;
            case 'notification':
                preloadComponent(() => import('./notification-settings'));
                break;
            case 'language':
                preloadComponent(() => import('./language-settings'));
                break;
            case 'sessionFilter':
                preloadComponent(() => import('./session-filter-settings'));
                break;
            case 'hotkeys':
                preloadComponent(() => import('./hot-keys-settings'));
                break;
            case 'frequentReply':
                preloadComponent(() => import('./frequent-reply-settings'));
                break;
            case 'laboratory':
                preloadComponent(() => import('./laboratory'));
                break;
            case 'about':
                preloadComponent(() => import('./about-settings'));
                break;
        }
    }, []);

    const handleTabChange = useCallback(
        (currentTabName: string) => {
            if (!isTabNameValid(currentTabName)) return;
            setTabName(currentTabName);
            // 当切换标签页时，预加载相邻标签页
            if (isTabNameValid(currentTabName)) {
                preloadAdjacentTabs(currentTabName);
            }
        },
        [preloadAdjacentTabs]
    );

    useEffect(() => {
        // 注册服务，给外部使用，可以在主进程中调用并跳转到指定 tab
        const {dispose} = selfContext.on('changeTab', (_tabName: ISettingPageTabName, options) => {
            let currentTabName = _tabName;
            if (!isTabNameValid(currentTabName)) {
                currentTabName = 'general';
            }
            setTabName(currentTabName);

            // 预加载相邻标签页
            preloadAdjacentTabs(currentTabName);

            // 处理 options
            if (!options) return;
            if (options.isEditing) {
                setIsEditing(options.isEditing);
            }
            if (options.scrollToApp) {
                setScrollToApp(options.scrollToApp);
            }
        });

        i18nApi.getTranslateLanguage().then((lt: ITranslationLanguage) => {
            if (lt) {
                i18n.setTranslationLanguage(lt);
            }
        });

        // 初始加载时预加载当前标签页和相邻标签页
        preloadTabComponent(tabName);
        preloadAdjacentTabs(tabName);

        return dispose;
    }, [tabName, preloadAdjacentTabs, preloadTabComponent]);

    useEffect(() => {
        if (!viewStartTime || hasReportRef.current) return;
        // 首屏性能埋点上报
        const firstRenderTime = Date.now();
        report.metrics({
            nm: 'global.imInnerPage.statTime',
            val: {
                page: 'settings',
                fmt: firstRenderTime - viewStartTime
            }
        });
        hasReportRef.current = true;
    }, [viewStartTime]);

    return (
        <div className="setting-panel">
            <Watermark />
            <div className="setting-panel-title">{i18n.$t('setting_panel_setting_title', '设置')}</div>
            <div className="setting-panel-content">
                <Tabs className="setting-tabs" tabBarPosition="left" activeKey={tabName} onChange={handleTabChange}>
                    <TabPane
                        label={<TabLabel iconType="setting">{i18n.$t('setting_panel_general_tab', '常规')}</TabLabel>}
                        key="general"
                    >
                        {tabName === 'general' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <GeneralSettings />
                            </Suspense>
                        )}
                    </TabPane>
                    <TabPane
                        label={<TabLabel iconType="bell">{i18n.$t('setting_panel_notification_tab', '通知')}</TabLabel>}
                        key="notification"
                    >
                        {tabName === 'notification' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <NotificationSettings scrollToApp={scrollToApp} />
                            </Suspense>
                        )}
                    </TabPane>
                    <TabPane
                        label={
                            <TabLabel iconType="globe">
                                {i18n.$t('setting_panel_language_and_timezone_settings', '语言和时区')}
                            </TabLabel>
                        }
                        key="language"
                    >
                        {tabName === 'language' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <LanguageSettings />
                            </Suspense>
                        )}
                    </TabPane>
                    {/* 哈哈哈哈会话样式 12 月新大象也不支持 */}
                    {/* <TabPane
                        label={
                            <TabLabel iconType="comment">
                                {i18n.$t('setting_panel_session_style_tab', '会话样式')}
                            </TabLabel>
                        }
                        key="sessionStyle"
                    >
                        <Suspense fallback={<LoadingFallback />}>
                            <SessionStyleSettings />
                        </Suspense>
                    </TabPane> */}

                    <TabPane
                        label={
                            <TabLabel iconType="chatsetting">
                                {i18n.$t('setting_panel_session_filter_tab', '会话筛选')}
                            </TabLabel>
                        }
                        key="sessionFilter"
                    >
                        {tabName === 'sessionFilter' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <SessionFilterSettings sorting={isEditing} onSortingChange={setIsEditing} />
                            </Suspense>
                        )}
                    </TabPane>

                    <TabPane
                        label={
                            <TabLabel iconType="keyboard">{i18n.$t('setting_panel_hotkeys_tab', '快捷键')}</TabLabel>
                        }
                        key="hotkeys"
                    >
                        {tabName === 'hotkeys' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <HotkeySettings />
                            </Suspense>
                        )}
                    </TabPane>
                    <TabPane
                        label={
                            <TabLabel iconType="reply-quick">
                                {i18n.$t('setting_panel_frequent_reply_tab', '常用语')}
                            </TabLabel>
                        }
                        key="frequentReply"
                    >
                        {tabName === 'frequentReply' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <FrequentReplySettings />
                            </Suspense>
                        )}
                    </TabPane>
                    <TabPane
                        label={<TabLabel iconType="lab">{i18n.$t('setting_panel_laboratory_tab', '实验室')}</TabLabel>}
                        key="laboratory"
                    >
                        {tabName === 'laboratory' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <LaboratorySettings />
                            </Suspense>
                        )}
                    </TabPane>
                    <TabPane
                        label={<TabLabel iconType="info">{i18n.$t('setting_panel_about_tab', '关于')}</TabLabel>}
                        key="about"
                    >
                        {tabName === 'about' && (
                            <Suspense fallback={<LoadingFallback />}>
                                <AboutSettings />
                            </Suspense>
                        )}
                    </TabPane>
                </Tabs>
            </div>
        </div>
    );
}
