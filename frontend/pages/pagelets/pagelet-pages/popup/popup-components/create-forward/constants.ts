import {showToast} from '@components/Toast';
import {i18n} from '@lib/i18n';

// 一次最多转发会话数
export const MULTI_FORWARD_LIMIT = 9;

// 过滤特殊的空字符,避免发出空气泡
export const matchWhiteSpaceReg =
    /^([\x00-\x1F\x7F-\x9F\u0000-\u001F\u2028\u2029\u200B\u200C\u200D\uFEFF]+|(\s)+)|([\x00-\x1F\x7F-\x9F\u0000-\u001F\u2028\u2029\u200B\u200C\u200D\uFEFF]+|(\s)+)$/g;
// 目前支持的发送消息的最大字数
export const MAX_SEND_MESSAGE_LENGTH = 6000;

export const LOAD_RECENT_SESSION_LIMIT = 50;

// 转发组件随容器宽度自适应相关尺寸常量
export const MIN_FORWARD_PANEL_WIDTH = 336; // 转发组件最小宽度
export const MAX_FORWARD_PANEL_WIDTH = 500; // 最大宽度
export const WINDOW_WIDTH_NEED_ADAPT = 524; // 转发组件需要开始自适应宽度的容器宽度，大于这个值时转发组件是定宽
export const CONFIRM_MODAL_NEED_ADAPT = 424;
export const LEVI_HIDE_CREATE_GROUP_WIDTH = 800; // Levi 下隐藏建群按钮的窗口宽度
export const MIN_PADDING_WIDTH = 24;
export const TOP_ITEM_WIDTH = 44; // 置顶会话单个元素宽度
export const MIN_TOP_ITEM_Margin = 8; // 最小间距
export const MAX_TOP_ITEMS_PER_ROW = 7; // 置顶区域最大每行元素数

export enum FromSource {
    bubble = 'bubble',
    thread = 'thread',
    screenshot = 'screenshot',
    forward = 'forward'
}

/** 消息不可发送的原因：字符串过长、消息仅包含无效字符 */
export enum UnsendableReason {
    tooLong = 'tooLong',
    invalidText = 'invalidText',
    /**
     * 图片上传未完成
     */
    imgUploadNotComplete = 'imgUploadNotComplete',
    /**
     * 图片上传失败
     */
    imgUploadFailed = 'imgUploadFailed',
    /**
     * 图片过期
     */
    imgOverdue = 'imgOverdue'
}

export const unsendHandler = {
    [UnsendableReason.tooLong]: () => {
        showToast(i18n.$t('forward_msg_too_long_tip', '发送消息超长，请分条发送'), {
            type: 'error',
            singleNotify: true
        });
    },
    [UnsendableReason.invalidText]: () => {
        showToast(i18n.$t('forward__send_content_cannot_be_empty', '发送内容不能为空'), {
            type: 'error',
            singleNotify: true
        });
    },
    [UnsendableReason.imgUploadNotComplete]: () => {
        showToast(i18n.$t('forward_img_uploading', '正在上传图片，请稍等'), {type: 'error', singleNotify: true});
    },
    [UnsendableReason.imgUploadFailed]: () => {
        showToast(i18n.$t('forward_img_upload_failed', '有图片未上传成功，请重新上传'), {
            type: 'error',
            singleNotify: true
        });
    },
    [UnsendableReason.imgOverdue]: () => {}
};

export const confirmResult: {
    isForwardMsgCreate: boolean;
} = {
    isForwardMsgCreate: true
};

export enum ForwardFromSource {
    Label = 'label',
    LabelTitle = 'labelTitle',
    TopSession = 'topSession',
    LastSession = 'lastSession',
    SelectedPanel = 'selectedPanel'
}

export const borderHeight = 2;
