import {makeAutoObservable, reaction, toJS} from 'mobx';
import {
    DoubleLevelConfigKeys,
    type Message,
    MESSAGE_STATUS,
    MESSAGE_TYPE,
    type SessionId
} from '@xm/electron-imsdk-types';
import {parseStringSessionId} from '@xm/electron-imsdk/client/tools';
import debounce from 'lodash/debounce';
import {shouldTriggerAutoTranslate, autoTranslateCondition} from '@frontend/helpers/messages/translate';
import {i18n} from '@lib/i18n';
import {generateUuid} from '@shared/common/uuid';
import {SESSION_TYPE_TO_CONFIG_CHAT_TYPE} from '@frontend/constants/sessions';

import {appEvents, createLogger} from '#preload/internal';

import configure from '../../configure';
import messageSingleton from '..';
import im from '../../../domains/im';
import rootStore from '../../../RootStore';

import type {ConfigStore} from '@frontend/stores/ConfigStore';
import type {Virtualizer} from '@xm/virtual-list';

const logger = createLogger('im-main');

export class AutoTranslateMessageSingleton {
    autoTranslateConfig = false;
    translationFoldedMsgs: string[] = []; // 手动收起的翻译在当前会话中不再曝光展开
    private configStore: ConfigStore;
    private currentSessionId: SessionId | undefined = undefined;
    /** 消息虚拟滚动列表 */
    private virtualizedList?: Virtualizer<HTMLDivElement, Element>;
    private scrollTimer: NodeJS.Timeout | null = null;

    constructor(options: {configStore: ConfigStore}) {
        makeAutoObservable(this);
        this.configStore = options.configStore;
        im.offAllByGroup('autoTranslate');
        im.on('imsdk:data_sync_finish', this.setTranslateLang, {group: 'autoTranslate'});
        im.on('network:changed', this.onBubbleListScrollEnd, {group: 'autoTranslate'});
        im.on('config:dx_config_changed', this.initAutoTranslateConfig, {group: 'autoTranslate'});

        reaction(() => this.currentSessionId, this.initAutoTranslateConfig, {fireImmediately: true});
        reaction(() => this.autoTranslateConfig, this.onBubbleListScrollEnd, {fireImmediately: true});
        reaction(
            () => messageSingleton.messageList.length,
            () => {
                this.virtualizedList?.isAtBottom && this.onBubbleListScrollEnd();
            },
            {fireImmediately: true}
        );

        appEvents.on('translateLanguage-change', this.onBubbleListScrollEnd);
    }

    get isTranslateEnable() {
        return this.configStore.grayConfig.data.auto_translate;
    }

    setTranslateLang = () => {
        const language = i18n.getTranslationLanguage();
        im.call('account.dxSetTranslateLanguage', [{language}]);
    };

    // 切会话获取配置
    initAutoTranslateConfig = async () => {
        if (!this.currentSessionId || !this.isTranslateEnable) return;
        const {globalAutoTranslate} = this.configStore.dxsdkConfig.configs;
        const {sessionAutoTranslate = {}} = await im.call('config.dxGetNestedCfgs', [
            {key: DoubleLevelConfigKeys.SESSION_AUTO_TRANSLATE, nestedKeys: []}
        ]);
        logger.info('im.autoTranslate.config', globalAutoTranslate, sessionAutoTranslate);
        const config =
            sessionAutoTranslate?.[
                `${SESSION_TYPE_TO_CONFIG_CHAT_TYPE[this.currentSessionId.type]}-${this.currentSessionId.uid}`
            ];
        this.autoTranslateConfig = config ? config === 'true' : globalAutoTranslate === 'OPEN';
    };

    /** 设置虚拟滚动列表 */
    setVirtualizedList(virtualizedList: Virtualizer<HTMLDivElement, Element>) {
        this.virtualizedList = virtualizedList;
    }

    // 消息列表滚动销毁计时器
    bubbleListScroll = () => {
        if (!this.isTranslateEnable) return;
        this.scrollTimer && clearTimeout(this.scrollTimer);
    };

    createEventGuideMessage = async () => {
        if (!this.currentSessionId) return;
        const {serverTimestamp} = await im.getCts();
        const protocol = 'mtdaxiang://www.meituan.com/switchToSettingPanel?page=language&section=autoTranslate';
        return {
            uuid: generateUuid(),
            sessionType: this.currentSessionId.type,
            from: configure.myUid,
            belongTo: String(this.currentSessionId.uid),
            fromName: '',
            channelId: 0,
            type: MESSAGE_TYPE.MSG_TYPE_EVENT,
            status: MESSAGE_STATUS.STATUS_SENDED,
            body: {
                text: this.autoTranslateConfig
                    ? `${i18n.$t('im_auto_translate_opened', '已开启全局自动翻译，')}[${i18n.$t('im_auto_translate_close', '关闭自动翻译')}|${protocol}]`
                    : `${i18n.$t('im_auto_translate_closed_and_detected', '检测到外语消息，试试 ')}[${i18n.$t('im_auto_translate', '自动翻译')}|${protocol}]`
            },
            receipt: false,
            extension: JSON.stringify({
                systemNotice: {
                    caseKey: this.autoTranslateConfig ? 'closeAutoTranslateNotice' : 'openAutoTranslateNotice',
                    caseData: '{}'
                }
            }),
            time: Number(serverTimestamp),
            svrTime: Number(serverTimestamp)
        };
    };

    // 消息列表停止滚动，获取视口内的消息
    onBubbleListScrollEnd = debounce(() => {
        if (!navigator.onLine || !this.isTranslateEnable) return;
        this.scrollTimer && clearTimeout(this.scrollTimer);
        const triggerDelay = this.configStore.hornConfig.desktop.auto_translate_trigger_delay;
        this.scrollTimer = setTimeout(async () => {
            if (!this.currentSessionId) return;
            // 获取当前视图中可见的消息
            const msgsVirtualItemsInView = this.virtualizedList?.getVirtualItemsInViewport();
            const translateMsgs: Message[] = [];

            (msgsVirtualItemsInView ?? []).forEach(item => {
                if (item.data && ['fowardLoader', 'BackLoader'].includes(item.data)) return;
                const msg = messageSingleton.messageList[item.index - 1];
                const {message} = msg.message;
                // 校验消息类型及消息内容是否触发自动翻译
                const isMsgFromMe = message.from?.toString() === configure.myUid;
                if (isMsgFromMe || !autoTranslateCondition(message) || this.translationFoldedMsgs.includes(message.mid))
                    return;

                const shouldTrigger = shouldTriggerAutoTranslate(message);
                if (!shouldTrigger) return;
                translateMsgs.push(toJS(message));
            });

            if (!translateMsgs.length) return;

            try {
                const eventMessage = await this.createEventGuideMessage();
                const msg = await im.call('message.dxTranslateMessages', [
                    {
                        sessionId: toJS(this.currentSessionId),
                        messages: translateMsgs,
                        autoTranslate: true,
                        first: Boolean(this.virtualizedList?.isAtBottom),
                        event: eventMessage
                    }
                ]);
                logger.info('im.autoTranslate.dxTranslateMessages', translateMsgs.length, msg);
            } catch (error) {
                logger.error('im.autoTranslate.error', error);
            }
        }, triggerDelay);
    }, 300);

    updateTranslationFoldedMsgs = (mid: string) => {
        !this.translationFoldedMsgs.includes(mid) && this.translationFoldedMsgs.push(mid);
    };

    clearTranslationFoldedMsgs = () => {
        this.translationFoldedMsgs = [];
    };

    onSessionChange = (sessionIdStr?: string) => {
        this.currentSessionId = sessionIdStr ? parseStringSessionId(sessionIdStr) : undefined;
        this.clearTranslationFoldedMsgs();
    };
}

const autoTranslateMessageSingleton = new AutoTranslateMessageSingleton({configStore: rootStore.config});

export default autoTranslateMessageSingleton;
