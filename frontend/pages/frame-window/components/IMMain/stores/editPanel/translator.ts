/* eslint-disable accessor-pairs */
import {makeAutoObservable, observable, reaction} from 'mobx';
import {parseStringSessionId, toStringSessionId} from '@xm/electron-imsdk/client/tools';
import md5Hex from 'md5-hex';
import {
    type TranslateLang,
    translateValue,
    type TranslateChat,
    type TranslateTextContent,
    type TranslateTextDecodeContent,
    TranslateTextCode,
    TranslateTextType
} from '@lib/apis/translate';
import {safeJsonParse} from '@shared/common/safeJsonParse';
import {
    CHAT_TYPE_TO_SESSION_TYPE,
    SESSION_TYPE_TO_CONFIG_CHAT_TYPE,
    SESSION_TYPE_TO_CHAT_TYPE
} from '@constants/sessions';
import get from 'lodash/get';
import {ACTION_KEY, CUSTOM_KEY, EditorAction} from '@components/RichTextEditor/helpers/constants';
import {nodeSome} from '@components/RichTextEditor/helpers/common';
import {isPMParagraphNode, isPMTextNode} from '@components/RichTextEditor/helpers/type-check';
import {historyKey} from '@components/RichTextEditor/extensions/history-extension/pm-history';
import {ONE_MINUTE} from '@helpers/constants';
import toJSON from '@helpers/toJSON';
import {
    syncTranslationLanguagesToLocalStorage,
    syncTranslationOperationToLocalStorage
} from '@pages/frame-window/components/IMMain/helpers/translate';
import {to} from '@xm/elefanto';
import {DoubleLevelConfigKeys, type TranslateActionConfig, type SessionId} from '@xm/electron-imsdk-types';

import {report} from '#preload/internal';

import imProxy from '../../domains/im';
import logger from '../../domains/logger';

import type {RichTextMessageBodyData} from '../../../../../../typings/RichTextMessage';
import type {IEditorHandles} from '@components/RichTextEditor';
import type {EditorState, Transaction} from 'prosemirror-state';
import type {PMNode} from '@components/RichTextEditor/typings';
import type {ConfigStore} from '@frontend/stores/ConfigStore';
import type {DXSDKConfigStore} from '@frontend/stores/ConfigStore/DXSDKConfigStore';

export enum TranslateActionType {
    REPLACE = 'replace',
    INSERT = 'insert',
    WITHDRAW = 'withdraw'
}

/** 判断输入框中是否有能翻译的节点（不为空的文本节点） */
function hasTranslateNode(editorHandles: IEditorHandles) {
    const view = editorHandles.getView();
    if (!view) return false;
    const {doc} = view.state;
    const isValidateText = (node: PMNode) => isPMTextNode(node) && node.text.trim() !== '';
    const isValidateParagraph = (node: PMNode) => {
        return isPMParagraphNode(node) && nodeSome(node, isValidateText, false);
    };
    return nodeSome(doc, isValidateParagraph);
}

function buildTranslateData(params: {
    richData: RichTextMessageBodyData;
    targetLanguage: TranslateLang;
    sessionId: SessionId;
}): TranslateData {
    const {richData, targetLanguage, sessionId} = params;
    const md5 = md5Hex(JSON.stringify(richData));
    const decodeContent: TranslateTextDecodeContent = {
        targetLanguage,
        code: TranslateTextCode.SUCCESS,
        chat: {
            chatType: SESSION_TYPE_TO_CHAT_TYPE[sessionId.type],
            chatId: sessionId.uid,
            channel: sessionId.channelId,
            sid: '',
            peerUid: ''
        },
        result: richData,
        textType: TranslateTextType.RICH,
        md5
    };
    const data: TranslateData = {
        md5,
        targetLanguage,
        status: TranslateReqStatus.SUCCESS,
        result: decodeContent
    };
    return data;
}

// 翻译接口需要先调用翻译接口，但是翻译结果
export enum TranslateReqStatus {
    /** 请求成功 */
    SUCCESS = 0,
    /** 请求失败 */
    FAILED = 1,
    REQUESTING = 2,
    WAITING = 3
}

export interface TranslateData {
    md5?: string;
    result?: TranslateTextDecodeContent;
    status: TranslateReqStatus;
    targetLanguage: TranslateLang;
}

export class EditorTranslator {
    /** 如果 data[sessionIdStr] === undefined，说明没有发生过请求，为默认状态。
     * key: sessionIdStr; value: translate result
     */
    data: Record<string, TranslateData | undefined> = {};

    private configStore: ConfigStore;

    private translateReqTimer?: NodeJS.Timeout;

    private translateTimeoutTimer?: NodeJS.Timeout;

    private currSessionStr?: string;

    private currSessionId?: SessionId;

    private inputTranslateConfigs: Record<string, TranslateLang> | undefined;

    private inputTranslateOperationConfigs: Record<string, TranslateActionConfig> | undefined;

    getEditorHandles: () => IEditorHandles | undefined;

    history = {
        undo: undefined as TranslateData | undefined,
        redo: undefined as TranslateData | undefined
    };

    get undoHistory() {
        return this.history.undo;
    }

    get redoHistory() {
        return this.history.redo;
    }

    get currSessionTranslateData() {
        return this.currSessionStr ? this.data[this.currSessionStr] : undefined;
    }

    get translateWait() {
        return this.configStore.hornConfig.client.editor_translate_wait ?? 2000;
    }

    get targetLangConfigs() {
        return (
            this.inputTranslateConfigs ||
            (this.configStore.dxsdkConfig.configs.inputTranslate as Record<string, TranslateLang> | undefined)
        );
    }

    get currentSessionSubKey() {
        if (!this.currSessionId) return undefined;
        const configChatType = SESSION_TYPE_TO_CONFIG_CHAT_TYPE[this.currSessionId.type];
        const chatId = this.currSessionId.uid;
        const channel = this.currSessionId.channelId;
        return `${configChatType}-${chatId}-${channel}`;
    }

    get targetLanguage() {
        if (!this.currentSessionSubKey || !this.configStore.grayConfig.data.inputTranslate) return undefined;
        const targetLang = this.targetLangConfigs?.[this.currentSessionSubKey];
        logger.info('editor.translate', {targetLang});
        return targetLang;
    }

    get translateOperation() {
        if (!this.currentSessionSubKey || !this.configStore.grayConfig.data.inputTranslate) return undefined;
        const operationConfigs =
            this.inputTranslateOperationConfigs ||
            (this.configStore.dxsdkConfig.configs.inputTranslateOperation as
                | Record<string, TranslateActionConfig>
                | undefined);
        return (
            operationConfigs?.[this.currentSessionSubKey] ||
            this.configStore.dxsdkConfig.configs.globalInputTranslateOperation
        );
    }

    get isTranslateEnable() {
        return this.configStore.grayConfig.data.inputTranslate;
    }

    get targetLanguageWithSession() {
        return {
            sessionId: this.currSessionId,
            targetLanguage: this.targetLanguage
        };
    }

    constructor(options: {configStore: ConfigStore; getEditorHandles: () => IEditorHandles | undefined}) {
        this.configStore = options.configStore;
        this.getEditorHandles = options.getEditorHandles;
        makeAutoObservable<EditorTranslator, 'configure'>(
            this,
            {
                configure: observable.ref
            },
            {
                autoBind: true
            }
        );
        imProxy.offByGroup('msgEnhancement:textTranslate', 'editorTranslator');
        imProxy.on('msgEnhancement:textTranslate', this.onGetTranslateRes, {
            group: 'editorTranslator'
        });
        imProxy.offByGroup('config:dx_config_changed', 'editorTranslator');
        imProxy.on('config:dx_config_changed', this.onConfigChange, {
            group: 'editorTranslator'
        });

        reaction(
            () => this.targetLanguageWithSession,
            ({sessionId, targetLanguage}, {sessionId: prevSessionId}) => {
                const editorHandles = this.getEditorHandles();
                this.onTargetLangChange({targetLanguage, sessionId, prevSessionId, editorHandles});
            }
        );
    }

    private onGetTranslateRes = (contentStr: string) => {
        if (this.currSessionTranslateData?.status === TranslateReqStatus.FAILED) {
            return;
        }
        const content = safeJsonParse<TranslateTextContent>(contentStr);
        if (!content) return;
        const {result, md5, chat, targetLanguage} = content;
        const sessionId: SessionId = {
            type: CHAT_TYPE_TO_SESSION_TYPE[chat.chatType],
            uid: chat.chatId,
            channelId: chat.channel,
            sid: ''
        };
        const sessionIdStr = toStringSessionId(sessionId);
        if (sessionIdStr !== this.currSessionStr) return;

        // md5 和目标语言都一致时才采用翻译结果
        const prevData = this.data[sessionIdStr];
        if (md5 !== prevData?.md5 || targetLanguage !== prevData.targetLanguage) return;

        const decodeRes = safeJsonParse<RichTextMessageBodyData>(result);
        if (!decodeRes) return;
        const decodeContent: TranslateTextDecodeContent = {...content, result: decodeRes};
        this.data[sessionIdStr] = {
            ...prevData,
            status:
                decodeContent.code === TranslateTextCode.SUCCESS
                    ? TranslateReqStatus.SUCCESS
                    : TranslateReqStatus.FAILED,
            result: decodeContent
        };
        clearTimeout(this.translateTimeoutTimer);
    };

    private onTargetLangChange = (options: {
        sessionId?: SessionId;
        prevSessionId?: SessionId;
        editorHandles?: IEditorHandles;
        targetLanguage: TranslateLang | undefined;
    }) => {
        const {sessionId, prevSessionId, targetLanguage, editorHandles} = options;

        if (!editorHandles) {
            logger.error('changeLangWithoutEditorHandles');
            return;
        }

        // 会话不存在，不翻译
        if (!sessionId || !prevSessionId) return;
        const sessionIdStr = toStringSessionId(sessionId);
        const prevSessionIdStr = toStringSessionId(prevSessionId);
        // 必须是在同一个会话切换语言才出触发翻译
        if (sessionIdStr !== prevSessionIdStr) return;

        // targetLanguage 不存在时为关闭翻译功能
        if (!targetLanguage) {
            this.data[sessionIdStr] = undefined;
            return;
        }

        this.wantTranslate({targetLanguage, sessionId, editorHandles, immediately: true});
    };

    private onConfigChange({data: changedConfigs}: {data: DXSDKConfigStore['configs']}) {
        const {globalInputTranslate} = this.configStore.dxsdkConfig.configs;
        // 有效的边写边译配置需要存入localStorage, 在下一次打开时使用
        if (changedConfigs.inputTranslate) {
            const translateConfigValue = changedConfigs.inputTranslate as Record<string, TranslateLang>;
            const translateConfig = syncTranslationLanguagesToLocalStorage(translateConfigValue, globalInputTranslate);
            this.inputTranslateConfigs = {...this.inputTranslateConfigs, ...translateConfig};
        }
        if (changedConfigs.inputTranslateOperation) {
            const translateOperationConfigValue = changedConfigs.inputTranslateOperation as Record<
                string,
                TranslateActionConfig
            >;
            const translateOperationConfig = syncTranslationOperationToLocalStorage(translateOperationConfigValue);
            this.inputTranslateOperationConfigs = {
                ...this.inputTranslateOperationConfigs,
                ...translateOperationConfig
            };
        }
    }

    onEditorChange = (options: {
        sessionId: SessionId;
        editorHandles: IEditorHandles;
        tr: Transaction;
        state: EditorState;
        oldState: EditorState;
    }) => {
        clearTimeout(this.translateReqTimer);

        const {targetLanguage} = this;
        const {tr, sessionId, editorHandles, state, oldState} = options;
        if (!targetLanguage) return;

        // 翻译「替换、撤回、重做」引发的变化不会重新触发翻译
        // 替换
        const customData = CUSTOM_KEY.getState(state);
        const translateType = get(customData, 'translate');
        if ([TranslateActionType.REPLACE, TranslateActionType.INSERT].includes(translateType)) {
            this.history.undo = this.currSessionTranslateData;
            this.history.redo = undefined;
            return;
        }

        // 撤回
        const histMeta = tr.getMeta(historyKey);
        const prevStateCustomData = CUSTOM_KEY.getState(oldState);
        const prevStateTranslateType = get(prevStateCustomData, 'translate');
        if (
            get(histMeta, 'redo') === false &&
            [TranslateActionType.REPLACE, TranslateActionType.INSERT].includes(prevStateTranslateType)
        ) {
            this.history.redo = this.currSessionTranslateData;
            this.history.undo = undefined;
            return;
        }

        // 更改 @人状态不触发翻译
        const editorAction: EditorAction[] = tr.getMeta(ACTION_KEY) || [];
        if (editorAction.includes(EditorAction.UPDATE_MENTION_VALID_TYPE)) {
            // const prevData = this.data[toStringSessionId(sessionId)];
            // this.data[toStringSessionId(sessionId)] = prevData === undefined ? prevData : {...prevData};
            return;
        }

        this.history = {undo: undefined, redo: undefined};

        const immediately = get(customData, 'translateImmediately');
        this.wantTranslate({targetLanguage, sessionId, editorHandles, immediately});
    };

    translate = async (params: {
        editorHandles: IEditorHandles;
        targetLanguage: TranslateLang;
        sessionId: SessionId;
        force?: boolean;
    }) => {
        // 发生请求时，清除延时请求。
        clearTimeout(this.translateReqTimer);
        // 发生请求时，清空历史。
        if (this.history.redo || this.history.undo) this.history = {undo: undefined, redo: undefined};
        const {targetLanguage, editorHandles, sessionId, force} = params;
        const sessionIdStr = toStringSessionId(sessionId);

        // 如果当前会话不是翻译目标会话，不进行翻译
        if (sessionIdStr !== this.currSessionStr) return;

        // 如果 targetLanguage 与当前目标语言不一致，不翻译
        if (targetLanguage !== this.targetLanguage) return;

        // 每次请求时，取消超时监听
        clearTimeout(this.translateTimeoutTimer);

        // 真正调用接口的时候再获取编辑器内容，避免不必要的计算
        const data = editorHandles.getMessageValue();
        const chat: TranslateChat = {
            chatType: SESSION_TYPE_TO_CHAT_TYPE[sessionId.type],
            chatId: sessionId.uid,
            channel: sessionId.channelId,
            sid: '',
            peerUid: ''
        };
        const content = JSON.stringify(data);
        const md5 = md5Hex(content);
        const prevReqData = this.data[sessionIdStr];
        const isSameReq =
            prevReqData?.status === TranslateReqStatus.REQUESTING &&
            prevReqData?.targetLanguage === targetLanguage &&
            prevReqData?.md5 === md5 &&
            !force;
        if (isSameReq) return;
        this.data[sessionIdStr] = {md5, targetLanguage, status: TranslateReqStatus.REQUESTING};
        const translateParams = {targetLanguage, chat, content, md5};
        report.web({
            nm: 'editor.translate.pv',
            val: {md5, targetLanguage, sessionId: toJSON(sessionId)}
        });
        const translateStartTime = performance.now();
        const [translateErr, res] = await to(translateValue(translateParams));
        const translateDuration = Math.round(performance.now() - translateStartTime);
        report.web({
            nm: 'editor.translateSuc.event',
            val: {md5, targetLanguage, sessionId: toJSON(sessionId), duration: translateDuration}
        });
        if (translateErr || !res) {
            // 翻译失败
            logger.error('translate error:', {md5, targetLanguage, sessionId: toJSON(sessionId), error: translateErr});
            report.web({
                nm: 'editor.translate.error',
                val: {md5, targetLanguage, sessionId: toJSON(sessionId)}
            });
            this.data[sessionIdStr] = {md5, targetLanguage, status: TranslateReqStatus.FAILED};
        }
        // 一分钟超时
        this.translateTimeoutTimer = setTimeout(
            () => this.onTranslateTimeout({targetLanguage, sessionId, md5}),
            ONE_MINUTE
        );
    };

    wantTranslate = (params: {
        editorHandles: IEditorHandles;
        targetLanguage: TranslateLang;
        sessionId: SessionId;
        force?: boolean;
        immediately?: boolean;
    }) => {
        if (!this.isTranslateEnable) return;

        const {editorHandles, targetLanguage, sessionId, force, immediately} = params;
        const sessionIdStr = toStringSessionId(sessionId);

        if (editorHandles.isEmpty({enableMultiEmptyLine: false})) {
            this.data[sessionIdStr] = undefined;
            return;
        }

        const shouldTranslate = force || hasTranslateNode(editorHandles);
        if (!shouldTranslate) {
            this.data[sessionIdStr] = buildTranslateData({
                sessionId,
                targetLanguage,
                richData: editorHandles.getMessageValue({filterSpaces: false, filterEmptyLines: false})
            });
            return;
        }

        if (immediately) {
            this.translate({editorHandles, targetLanguage, sessionId, force: true});
        } else {
            this.translateReqTimer = setTimeout(() => {
                this.translate({editorHandles, targetLanguage, sessionId, force});
            }, this.translateWait);
            this.data[sessionIdStr] = {targetLanguage, status: TranslateReqStatus.REQUESTING};
        }
    };

    /** 翻译超时 */
    onTranslateTimeout = (params: {targetLanguage: TranslateLang; sessionId: SessionId; md5: string}) => {
        const {targetLanguage, sessionId, md5} = params;
        const sessionIdStr = toStringSessionId(sessionId);
        // 超时时，不在当前回话，不处理
        if (sessionIdStr !== this.currSessionStr) return;

        // 如果当前没有请求，不处理
        const prevReqData = this.currSessionTranslateData;
        if (!prevReqData) return;

        // 如果不是正在请求中，不处理
        if (prevReqData.status !== TranslateReqStatus.REQUESTING) return;

        // 校验超时的请求与当前的请求是否一致
        if (prevReqData.md5 !== md5 || prevReqData.targetLanguage !== targetLanguage) return;

        // 把当前请求状态设置为失败
        this.data[sessionIdStr] = {...prevReqData, status: TranslateReqStatus.FAILED};
    };

    onSessionChange = async (sessionIdStr?: string) => {
        clearTimeout(this.translateReqTimer);
        clearTimeout(this.translateTimeoutTimer);
        const prevSessionIdStr = this.currSessionStr;
        // 删除上一个会话的翻译数据
        if (prevSessionIdStr) this.data[prevSessionIdStr] = undefined;
        // 删除当前会话的翻译数据
        if (sessionIdStr) this.data[sessionIdStr] = undefined;
        // 清除历史
        this.history = {undo: undefined, redo: undefined};
        this.currSessionStr = sessionIdStr;
        this.currSessionId = sessionIdStr ? parseStringSessionId(sessionIdStr) : undefined;

        const {inputTranslate = {}} = await imProxy.call('config.dxGetNestedCfgs', [
            {key: DoubleLevelConfigKeys.INPUT_TRANSLATE, nestedKeys: []}
        ]);
        const {inputTranslateOperation = {}} = await imProxy.call('config.dxGetNestedCfgs', [
            {key: DoubleLevelConfigKeys.INPUT_TRANSLATE_OPERATION, nestedKeys: []}
        ]);
        this.inputTranslateConfigs = {
            ...this.inputTranslateConfigs,
            ...(inputTranslate as Record<string, TranslateLang>)
        };
        this.inputTranslateOperationConfigs = {
            ...this.inputTranslateOperationConfigs,
            ...(inputTranslateOperation as Record<string, TranslateActionConfig>)
        };
        console.log('inputTranslate', inputTranslate, inputTranslateOperation);
    };
}
