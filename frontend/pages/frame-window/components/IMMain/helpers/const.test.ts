import {DoubleLevelConfigKeys} from '@xm/electron-imsdk-types';

import {SingleLevelConfigKeys, FRAME_WINDOW_CONFIGS, FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES} from './constants';

describe('ConfigStore constants', () => {
    describe('SingleLevelConfigKeys', () => {
        it('should contain expected keys', () => {
            // 测试一些关键配置项是否存在
            expect(SingleLevelConfigKeys.ALL_NOTIFY_PC).toBe('allNotifyPc');
            expect(SingleLevelConfigKeys.SEND_MESSAGE).toBe('send_message');
            expect(SingleLevelConfigKeys.IS_FREQUENTLY_REPLY_ENABLED).toBe('isFrequentlyReplyEnabled');
            expect(SingleLevelConfigKeys.PRIORITY_MSG).toBe('priorityMsg');
            expect(SingleLevelConfigKeys.LOGIN_INFO_TOKEN).toBe('login_info_rd');
        });
    });

    describe('FRAME_WINDOW_CONFIGS', () => {
        it('should contain expected config keys', () => {
            // 测试数组是否包含预期的配置项
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.ALL_NOTIFY_PC);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.ATALL_NOTIFY);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.SEND_MESSAGE);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.IS_FREQUENTLY_REPLY_ENABLED);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.LOGIN_INFO_TOKEN);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.GLOBAL_AUTO_TRANSLATE);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.GLOBAL_INPUT_TRANSLATE);
            expect(FRAME_WINDOW_CONFIGS).toContain(SingleLevelConfigKeys.GLOBAL_INPUT_TRANSLATE_OPERATION);
        });

        it('should have correct length', () => {
            expect(FRAME_WINDOW_CONFIGS).toHaveLength(15);
        });
    });

    describe('FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES', () => {
        it('should contain expected nested configs', () => {
            // 测试嵌套配置数组的结构
            expect(FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES).toHaveLength(5);

            // 测试第一个配置项
            expect(FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES[0]).toEqual({
                key: DoubleLevelConfigKeys.NOTIFY,
                nestedKeys: []
            });

            // 测试第二个配置项
            expect(FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES[1]).toEqual({
                key: DoubleLevelConfigKeys.ATALL_GROUP_NOTIFY,
                nestedKeys: []
            });

            // 测试第三个配置项
            expect(FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES[2]).toEqual({
                key: DoubleLevelConfigKeys.INPUT_TRANSLATE,
                nestedKeys: []
            });

            // 测试第四个配置项
            expect(FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES[3]).toEqual({
                key: DoubleLevelConfigKeys.INPUT_TRANSLATE_OPERATION,
                nestedKeys: []
            });

            expect(FRAME_WINDOW_DOUBLE_LEVEL_CONFIG_ENTRIES[4]).toEqual({
                key: DoubleLevelConfigKeys.SESSION_AUTO_TRANSLATE,
                nestedKeys: []
            });
        });
    });
});
