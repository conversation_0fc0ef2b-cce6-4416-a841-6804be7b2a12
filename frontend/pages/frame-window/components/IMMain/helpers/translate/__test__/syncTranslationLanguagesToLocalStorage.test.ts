import {describe, it, expect, vi, beforeEach} from 'vitest';
import {setLocalStorageSubItem, LocalStorageDoubleLevelKey} from '@helpers/local-storage';
import {supportedTranslationLanguages, type TranslateLang} from '@lib/apis/translate';
import {TranslateActionConfig} from '@xm/electron-imsdk-types';

import {
    syncTranslationLanguagesToLocalStorage,
    syncTranslationOperationToLocalStorage
} from '../syncTranslationLanguagesToLocalStorage';

// Mock dependencies
vi.mock('@helpers/local-storage');
vi.mock('./getDefaultLanguage');

describe('syncTranslationLanguagesToLocalStorage', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        vi.mocked(supportedTranslationLanguages);
    });

    it('should return a new object without mutating input', () => {
        // Arrange
        const inputConfig = {key1: 'en' as TranslateLang};

        // Act
        const result = syncTranslationLanguagesToLocalStorage(inputConfig);

        // Assert
        expect(result).toEqual(inputConfig);
        expect(result).not.toBe(inputConfig);
    });

    it('should skip empty language configurations', () => {
        // Arrange
        const inputConfig = {
            key1: undefined,
            key2: null,
            key3: 'en' as TranslateLang
        } as unknown as Record<string, TranslateLang>;

        // Act
        const result = syncTranslationLanguagesToLocalStorage(inputConfig);

        // Assert
        expect(setLocalStorageSubItem).toHaveBeenCalledTimes(1);
        expect(result.key3).toBe('en');
    });

    it('should replace unsupported languages with defaults', () => {
        // Arrange
        const inputConfig = {
            key1: 'fr' as TranslateLang,
            key2: 'en' as TranslateLang
        };

        // Act
        const result = syncTranslationLanguagesToLocalStorage(inputConfig);

        // Assert
        expect(result.key2).toBe('en');
    });

    it('should store supported languages in localStorage', () => {
        // Arrange
        const inputConfig = {
            key1: 'en' as TranslateLang,
            key2: 'zh' as TranslateLang
        };

        // Act
        const result = syncTranslationLanguagesToLocalStorage(inputConfig);

        // Assert
        expect(setLocalStorageSubItem).toHaveBeenCalledTimes(2);
        expect(setLocalStorageSubItem).toHaveBeenCalledWith(
            LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG,
            'key1',
            'en'
        );
        expect(setLocalStorageSubItem).toHaveBeenCalledWith(
            LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG,
            'key2',
            'zh'
        );
        expect(result).toEqual(inputConfig);
    });

    it('should handle mixed valid and invalid languages', () => {
        // Arrange
        const inputConfig = {
            validKey: 'en' as TranslateLang,
            invalidKey: 'fr' as TranslateLang,
            emptyKey: undefined
        } as unknown as Record<string, TranslateLang>;

        // Act
        const result = syncTranslationLanguagesToLocalStorage(inputConfig);

        // Assert
        expect(setLocalStorageSubItem).toHaveBeenCalledTimes(1);
        expect(result.validKey).toBe('en');
        expect('emptyKey' in result).toBe(true);
    });
});

describe('syncTranslationOperationToLocalStorage', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('should return a new object without mutating input', () => {
        // Arrange
        const inputConfig = {key1: 'translate' as TranslateActionConfig};

        // Act
        const result = syncTranslationOperationToLocalStorage(inputConfig);

        // Assert
        expect(result).toEqual({key1: TranslateActionConfig.REPLACE});
        expect(result).not.toBe(inputConfig);
    });

    it('should skip empty operation configurations', () => {
        // Arrange
        const inputConfig = {
            key1: undefined,
            key2: null,
            key3: TranslateActionConfig.REPLACE
        } as unknown as Record<string, TranslateActionConfig>;

        // Act
        const result = syncTranslationOperationToLocalStorage(inputConfig);

        // Assert
        expect(setLocalStorageSubItem).toHaveBeenCalledTimes(1);
        expect(result.key3).toBe(TranslateActionConfig.REPLACE);
    });

    it('should store valid operation configurations in localStorage', () => {
        // Arrange
        const inputConfig = {
            key1: TranslateActionConfig.REPLACE,
            key2: TranslateActionConfig.INSERT
        };

        // Act
        const result = syncTranslationOperationToLocalStorage(inputConfig);

        // Assert
        expect(setLocalStorageSubItem).toHaveBeenCalledTimes(2);
        expect(setLocalStorageSubItem).toHaveBeenCalledWith(
            LocalStorageDoubleLevelKey.TRANSLATE_OPERATION,
            'key1',
            TranslateActionConfig.REPLACE
        );
        expect(setLocalStorageSubItem).toHaveBeenCalledWith(
            LocalStorageDoubleLevelKey.TRANSLATE_OPERATION,
            'key2',
            TranslateActionConfig.INSERT
        );
        expect(result).toEqual(inputConfig);
    });

    it('should handle mixed valid and empty operations', () => {
        // Arrange
        const inputConfig = {
            validKey: TranslateActionConfig.REPLACE,
            emptyKey: undefined
        } as unknown as Record<string, TranslateActionConfig>;

        // Act
        const result = syncTranslationOperationToLocalStorage(inputConfig);

        // Assert
        expect(setLocalStorageSubItem).toHaveBeenCalledTimes(1);
        expect(result.validKey).toBe(TranslateActionConfig.REPLACE);
        expect('emptyKey' in result).toBe(true);
    });
});
