import {describe, it, expect, vi, beforeEach} from 'vitest';
import {i18n} from '@lib/i18n';
import {TranslateLang} from '@lib/apis/translate';
import {getLocalStorageSubItem, LocalStorageDoubleLevelKey} from '@helpers/local-storage';

import {getDefaultLanguage} from '../getDefaultLanguage';

// Mock dependencies
vi.mock('@helpers/local-storage');
vi.mock('@lib/i18n');

describe('getDefaultLanguage', () => {
    const mockKey = 'test-session-key';

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('should return cached language when available in localStorage', () => {
        // Arrange
        const mockCachedLang = TranslateLang.ZH;
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce(mockCachedLang);

        // Act
        const result = getDefaultLanguage(mockKey);

        // Assert
        expect(getLocalStorageSubItem).toHaveBeenCalledWith(LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG, mockKey);
        expect(result).toBe(mockCachedLang);
    });

    it('should return globalInputTranslate when no cache exists and globalInputTranslate is provided', () => {
        // Arrange
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce('');
        const globalInputTranslate = TranslateLang.ZH_HK;

        // Act
        const result = getDefaultLanguage(mockKey, globalInputTranslate);

        // Assert
        expect(result).toBe(globalInputTranslate);
        expect(i18n.getLanguage).not.toHaveBeenCalled();
    });

    it('should return ZH when i18n language is EN and no cache or globalInputTranslate exists', () => {
        // Arrange
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce('');
        vi.mocked(i18n.getLanguage).mockReturnValueOnce('en');

        // Act
        const result = getDefaultLanguage(mockKey);

        // Assert
        expect(result).toBe(TranslateLang.ZH);
    });

    it('should return EN when i18n language is not EN and no cache or globalInputTranslate exists', () => {
        // Arrange
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce('');
        vi.mocked(i18n.getLanguage).mockReturnValueOnce('zh'); // or any non-'en' language

        // Act
        const result = getDefaultLanguage(mockKey);

        // Assert
        expect(result).toBe(TranslateLang.EN);
    });

    it('should handle empty session key', () => {
        // Arrange
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce('');
        vi.mocked(i18n.getLanguage).mockReturnValueOnce('en');

        // Act
        const result = getDefaultLanguage('');

        // Assert
        expect(getLocalStorageSubItem).toHaveBeenCalledWith(LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG, '');
        expect(result).toBe(TranslateLang.ZH);
    });

    it('should prioritize cache over globalInputTranslate', () => {
        // Arrange
        const mockCachedLang = TranslateLang.PT_BR;
        const globalInputTranslate = TranslateLang.ZH_HK;
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce(mockCachedLang);

        // Act
        const result = getDefaultLanguage(mockKey, globalInputTranslate);

        // Assert
        expect(result).toBe(mockCachedLang);
        expect(result).not.toBe(globalInputTranslate);
    });

    it('should handle empty string cache values correctly', () => {
        // Arrange
        vi.mocked(getLocalStorageSubItem).mockReturnValueOnce('');
        const globalInputTranslate = TranslateLang.ZH_HK;

        // Act
        const result = getDefaultLanguage(mockKey, globalInputTranslate);

        // Assert
        expect(result).toBe(globalInputTranslate);
    });
});
