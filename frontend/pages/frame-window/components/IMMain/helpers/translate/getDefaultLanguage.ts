import {i18n} from '@lib/i18n';
import {TranslateLang} from '@lib/apis/translate';
import {getLocalStorageSubItem, LocalStorageDoubleLevelKey} from '@helpers/local-storage';

export const getDefaultLanguage = (sessionSubKey: string, globalInputTranslate?: TranslateLang) => {
    // 先判断缓存里面有没有，有就用缓存的
    const cacheLang = getLocalStorageSubItem(LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG, sessionSubKey);
    if (cacheLang) return cacheLang;
    if (globalInputTranslate) return globalInputTranslate;
    return i18n.getLanguage() === 'en' ? TranslateLang.ZH : TranslateLang.EN;
};
