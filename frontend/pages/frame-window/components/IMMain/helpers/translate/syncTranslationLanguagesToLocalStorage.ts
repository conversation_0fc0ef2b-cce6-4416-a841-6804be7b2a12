import {supportedTranslationLanguages, type TranslateLang} from '@lib/apis/translate';
import {setLocalStorageSubItem, LocalStorageDoubleLevelKey} from '@helpers/local-storage';
import {TranslateActionConfig} from '@xm/electron-imsdk-types';

import {getDefaultLanguage} from './getDefaultLanguage';

/**
 * 同步翻译语言配置到本地存储
 * @param languageConfig 语言配置对象,键为配置项,值为对应的翻译语言
 * @returns 更新后的语言配置对象
 */
export function syncTranslationLanguagesToLocalStorage(
    languageConfig: Record<string, TranslateLang>,
    globalInputTranslate?: TranslateLang
): Record<string, TranslateLang> {
    const updatedConfig = {...languageConfig};

    Object.entries(languageConfig).forEach(([configKey, currentLanguage]) => {
        // 如果当前语言配置为空,跳过处理
        if (!currentLanguage) return;

        // 如果是不支持的语言,使用默认语言替换
        if (!supportedTranslationLanguages.includes(currentLanguage)) {
            updatedConfig[configKey] = getDefaultLanguage(configKey, globalInputTranslate);
            return;
        }

        // 将有效的语言配置保存到本地存储
        setLocalStorageSubItem(LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG, configKey, currentLanguage);
    });

    return updatedConfig;
}

export function syncTranslationOperationToLocalStorage(
    operationConfig: Record<string, TranslateActionConfig>
): Record<string, TranslateActionConfig> {
    const updatedConfig = {...operationConfig};

    Object.entries(operationConfig).forEach(([configKey, currentOperation]) => {
        // 如果当前操作配置为空,跳过处理
        if (!currentOperation) return;

        // 如果是不支持的操作,使用默认操作替换
        if (!Object.values(TranslateActionConfig).includes(currentOperation)) {
            updatedConfig[configKey] = TranslateActionConfig.REPLACE;
            return;
        }

        // 将有效的操作配置保存到本地存储
        setLocalStorageSubItem(LocalStorageDoubleLevelKey.TRANSLATE_OPERATION, configKey, currentOperation);
    });

    return updatedConfig;
}
