import {observer} from 'mobx-react';
import {i18n} from '@lib/i18n';
import {DXIcon} from '@xm/elefanto';
import React, {useCallback} from 'react';
import {DXTooltip} from '@pages/frame-window/components/IMMain/components/DXTooltip';
import {useStores} from '@pages/frame-window/components/IMMain/stores';
import classNames from 'classnames';

import {mainWindow} from '#preload/internal';

import './index.scss';

interface EditorTranslationGuideTooltipProps {
    isShow?: boolean;
    children: React.ReactNode;
}

const EditorTranslationGuideTooltip = ({
    isShow = false,
    children
}: EditorTranslationGuideTooltipProps): React.ReactNode => {
    const {
        featureGuide
        // editPanelStore: {editorTool}
    } = useStores();

    const hideGuideTooltip = () => {
        featureGuide.hide('editor-translation', 'is-show-tooltip');
    };

    const goSettingTips = (() => {
        const text = i18n.$t(
            'editor_translation_guide_go_setting',
            '译文可直接插入输入框，若需全会话适用可前往{{target}}更换默认使用方式',
            {target: '|'}
        );
        const [leftPart, rightPart] = text.split('|');
        return (
            <span>
                {leftPart}&nbsp;
                <span
                    className="editor-translation-guide-go-setting"
                    onClick={() => {
                        hideGuideTooltip();
                        mainWindow.showSettingPanel('language');
                    }}
                >
                    {i18n.$t('setting_panel_setting_title', '设置')}
                </span>
                &nbsp;{rightPart}
            </span>
        );
    })();

    /** 阻止 mousedown 事件冒泡 */
    const handleMouseDown = useCallback((event: MouseEvent) => {
        event.stopPropagation();
    }, []);

    return (
        <DXTooltip
            visible={isShow}
            // placement={editorTool.isFullScreen ? 'bottomLeft' : 'topLeft'}
            placement="topLeft"
            className={classNames('editor-translation-guide-tooltip', {en: i18n.getLanguage() === 'en'})}
            trigger="click"
            onDocumentClick={() => {
                hideGuideTooltip();
            }}
            getContainer={() => document.querySelector('.editor-translation') as HTMLElement}
            message={
                <div
                    className="editor-translation-guide-message"
                    onMouseEnter={() => {
                        const tooltipEl = document.querySelector<HTMLDivElement>('.editor-translation-guide-tooltip');
                        tooltipEl?.addEventListener('mousedown', handleMouseDown);
                    }}
                    onMouseLeave={() => {
                        const tooltipEl = document.querySelector<HTMLDivElement>('.editor-translation-guide-tooltip');
                        tooltipEl?.removeEventListener('mousedown', handleMouseDown);
                    }}
                >
                    <div className="editor-translation-guide-text">{goSettingTips}</div>
                    <div className="close-icon-wrapper">
                        <DXIcon type="dxicon3-close-mini" onClick={() => hideGuideTooltip()} />
                    </div>
                </div>
            }
        >
            {children}
        </DXTooltip>
    );
};

export default observer(EditorTranslationGuideTooltip);
