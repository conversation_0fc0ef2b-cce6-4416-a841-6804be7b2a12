.editor-translation {
    position: relative;
    display: flex;
    justify-content: space-between;
    // min-height: 40px;
    max-height: 164px;
    line-height: 22px;
    box-sizing: border-box;
    border-top: 1px solid rgb(0 0 0 / 6%);
    background-color: #F4F4F4;

    &.has-quote-content {
        max-height: 104px;
    }

    &.full-screen {
        max-height: 164px;
        background-color: #F5F5F5;
        z-index: 3;

        .mtd-select-popup {
            top: 32px !important;
        }
    }

    .translation-lan {
        max-width: 68px;
        min-width: 35px;
        height: 22px;
        font-size: 12px;
        margin: 9px 8px 0 14px;
        cursor: pointer;

        &.max-width {
            width: 68px;
            flex-shrink: 0;
        }

        .mtd-select-filter {
            height: 22px;
            border: none;

            // 禁用输入
            .mtd-select-filter-input {
                max-width: 0;

                input {
                    height: 22px;
                    line-height: 22px;
                    padding: 0;
                }
            }

            .mtd-select-filter-label {
                padding: 0 15px 0 5px;
                line-height: 22px;
                background: rgb(17 25 37 / 10%);
                border-radius: 4px;
                color: rgb(17 25 37 / 65%);
            }

            .mtd-select-filter-icon {
                line-height: 23px;
                right: 4px;
            }

            .mtdicon-down-thick {
                font-size: 12px;
                color: rgb(17 25 37 / 65%);
                font-weight: 500;
            }

            .mtd-select-filter-delete {
                display: none;
            }

            // &.mtd-select-filter-focused .mtd-select-filter-label,
            &:hover .mtd-select-filter-label {
                background: rgb(17 25 37 / 15%);
                color: rgb(17 25 37 / 65%);
            }
        }

        // 英文 en 向上偏移一点，达到居中的效果
        &.en .mtd-select-filter .mtd-select-filter-label {
            line-height: 22px;
        }
    }

    .translation-content {
        position: relative;
        flex-grow: 1;
        box-sizing: border-box;
        overflow: auto;
        margin: 8px 0;
        padding-right: 10px;
    }

    .translation-actions {
        margin: 9px 8px 9px 2px;
        flex-shrink: 0;
        display: flex;
        height: 21px;
        align-items: center;
    }

    .replace-btn,
    .withdraw-btn {
        display: inline-block;
        line-height: 20px;
        margin-right: 8px;
        height: 24px;
        font-size: 12px;
        font-weight: 500;
        border-radius: 4px;
        cursor: pointer;
    }

    .replace-btn {
        padding: 2px 8px;
        min-width: 40px;
        background: rgb(17 25 37 / 5%);
        user-select: none;
    }

    .replace-btn-disable {
        cursor: not-allowed;
        color: rgb(17 25 37 / 30%);
        background: rgb(17 25 37 / 3%);
    }

    .withdraw-btn {
        padding: 2px 4px;
        min-width: 24px;
    }

    .withdraw-btn:hover {
        background: rgb(17 25 37 / 5%);
    }

    .close-btn {
        width: 24px;
        height: 24px;
        line-height: 24px;
        cursor: pointer;

        i {
            margin-left: 5px;
            font-size: 14px;
            color: rgb(17 25 37 / 65%);
        }
    }

    .close-btn:hover {
        border-radius: 4px;
        background: rgb(17 25 37 / 5%);
    }
}

// 下拉选择框
.translation-lan-popup {
    >ul {
        max-height: fit-content !important;
    }

    .lan-option-label {
        display: inline-block;
        height: 22px;
        border-radius: 4px;
        background: rgb(17 25 37 / 5%);
        padding: 0 5px;
        font-size: 12px;
        color: rgb(17 25 37 / 65%);
        line-height: 21px;
        margin-right: 8px;
    }

    // 英文 en 向上偏移一点，达到居中的效果
    .lan-option-label.en {
        line-height: 20px;
    }

    .mtd-select-item {
        padding: 6px 8px;

        &:hover {
            border-radius: 6px;
        }

        &::after {
            content: "";
            width: 16px;
            height: 16px;
            background-image: url("dxpc://img/option-checked.png");
            background-size: 16px 16px;
            margin-left: 24px;
            opacity: 0;
        }
    }

    .mtd-select-item-selected, .translate-select-operation {
        color: #111925 !important;
        align-items: center;

        &::after {
            opacity: 1;
        }
    }

    .mtd-select-item-focused {
        background-color: transparent !important;

        &:hover {
            background-color: rgb(0 0 0 / 4%) !important;
        }
    }

    .mtd-select-popup {
        top: -121px !important;
        padding: 4px;
        min-width: 190px;
        border: none;
    }

    .mtd-select-group-wrapper {
        &:first-child .mtd-select-group-title {
            border: none;
        }

        .mtd-select-group-title {
            padding: 8px 8px 4px;
            font-size: 12px;
            line-height: 20px;
        }
    }
}

.replace-btn-tooltips {
    .mtd-tooltip-inner {
        font-size: 12px;
        line-height: 18px;
        border-radius: 6px;
    }
}

.full-screen-replace-btn-tooltips {
    top: 78px !important;
}