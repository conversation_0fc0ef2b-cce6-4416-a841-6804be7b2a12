import React, {useEffect, useRef, useState} from 'react';
import {observer} from 'mobx-react';
import classNames from 'classnames';
import debounce from 'lodash/debounce';
import {Select, Tooltip} from '@ss/mtd-react';
import {DXIcon, to} from '@xm/elefanto';
import {i18n} from '@lib/i18n';
import {lxAnalytics} from '@lib/lxAnalytics';
import {TranslateLang} from '@lib/apis/translate';
import imEventEmitter from '@pages/frame-window/components/IMMain/domains/event/im';
import {useStores} from '@pages/frame-window/components/IMMain/stores';
import {
    TranslateActionType,
    TranslateReqStatus
} from '@pages/frame-window/components/IMMain/stores/editPanel/translator';
import {DoubleLevelConfigKeys, TranslateActionConfig} from '@xm/electron-imsdk-types';
import {showToast} from '@components/Toast';
import {handleEditorTranslateTurnOff} from '@pages/frame-window/components/IMMain/helpers/translate';
import {useRootStore} from '@pages/frame-window/components/IMMain/RootStoreContext';
import {insertNodesToEnd} from '@frontend/components/RichTextEditor/helpers/insertNodesToEnd';

import logger from '../../../domains/logger';
import featureGuide from '../../../stores/feature-guide';

import {hasRichTextStyle} from './helpers';
import ReplaceModal from './ReplaceModal';
import TranslationPanel from './TranslationPanel';
import EditorTranslationGuideTooltip from './EditorTranslationGuideTooltip';
import {replaceShortcutKey, translationLanguageMap, translationSelectGroups, withdrawShortcutKey} from './constants';

import './index.scss';

interface OptionValue {
    value: TranslateLang | TranslateActionConfig;
    label: string;
    originOption?: any;
    disabled?: boolean;
    selected?: boolean;
    translateOperation?: TranslateActionConfig;
}

const {Option, OptionGroup: Group} = Select;

const EditorTranslation = ({
    isFullScreenMode,
    hasQuoteContent
}: {
    isFullScreenMode: boolean;
    hasQuoteContent: boolean;
}) => {
    const rootStore = useRootStore();
    const {editPanelStore, sessions, configure} = useStores();
    const {
        currSessionTranslateData,
        targetLanguage,
        translateOperation,
        currentSessionSubKey,
        undoHistory,
        redoHistory,
        getEditorHandles,
        translate
    } = editPanelStore.translator;
    if (!targetLanguage) return null;

    const [showReplaceModal, setShowReplaceModal] = useState(false);
    const [toolTipVisible, setToolTipVisible] = useState(false);
    const [prevPosition, setPrevPosition] = useState({top: 0});
    const [selectValue, setSelectValue] = useState<OptionValue>({
        value: targetLanguage,
        label: translationLanguageMap[targetLanguage]?.label,
        originOption: translationLanguageMap[targetLanguage]?.label,
        translateOperation
    });
    const translationRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLDivElement>(null);

    let translateData = currSessionTranslateData;
    if ((undoHistory && !redoHistory) || (!undoHistory && redoHistory)) {
        translateData = redoHistory;
    }

    const {setValue, getMessageValue, withdrawTranslation, blur} = getEditorHandles() || {};
    let myUid = '';

    const translateDataRef = useRef({
        myUid,
        translateData,
        undoHistory,
        blur,
        setValue,
        getMessageValue,
        withdrawTranslation
    });
    const sessionId = sessions.currentSession?.dxSession?.sessionId;

    useEffect(() => {
        let newTranslateData = currSessionTranslateData;
        if ((undoHistory && !redoHistory) || (!undoHistory && redoHistory)) {
            newTranslateData = redoHistory;
        }
        const uid = myUid || translateDataRef.current.myUid;
        translateDataRef.current = {
            myUid: uid,
            translateData: newTranslateData,
            undoHistory,
            blur,
            setValue,
            getMessageValue,
            withdrawTranslation
        };
    }, [currSessionTranslateData, undoHistory, redoHistory]);

    useEffect(() => {
        if (!targetLanguage) return;
        setSelectValue({
            value: targetLanguage,
            label: translationLanguageMap[targetLanguage]?.label,
            originOption: translationLanguageMap[targetLanguage]?.label,
            translateOperation
        });
    }, [targetLanguage]);

    const replaceEditorContent = (isShortcutKeys?: boolean) => {
        // 已经替换后，快捷键不再触发替换
        if (isShortcutKeys && translateDataRef.current.undoHistory) return;

        let setValueFn = setValue;
        let replaceData = translateData;
        if (isShortcutKeys) {
            replaceData = translateDataRef.current.translateData;
            setValueFn = translateDataRef.current.setValue;
        }
        const {nodes, isRichText} = replaceData?.result?.result || {};

        if (!setValueFn || !nodes) return;
        if (translateOperation === TranslateActionConfig.INSERT) {
            insertNodesToEnd({
                view: getEditorHandles()?.getView(),
                nodes,
                isRichText: Boolean(isRichText),
                customData: {
                    translate: TranslateActionType.INSERT
                }
            });
            return;
        }
        setValueFn({
            nodes,
            isRichText: Boolean(isRichText),
            overwrite: true,
            customData: {
                translate: TranslateActionType.REPLACE
            }
        });
    };

    const closeReplaceModal = () => {
        setShowReplaceModal(false);
    };

    const replaceContent = (isShortcutKeys?: boolean) => {
        // 用译文替换编辑器内容
        replaceEditorContent(isShortcutKeys);
        showReplaceModal && setShowReplaceModal(false);
    };

    /**
     * 处理替换模态框的显示和内容替换逻辑
     * @param {boolean} [isShortcutKeys] - 是否通过快捷键触发
     */
    const handleReplaceModal = (isShortcutKeys?: boolean) => {
        const isInsertOperation = translateOperation === TranslateActionConfig.INSERT;
        lxAnalytics('moduleClick', isInsertOperation ? 'b_oa_p6zzvhan_mc' : 'b_oa_3yavpd95_mc', null, {
            isLeave: false,
            cid: 'c_oa_chat'
        });

        if (isInsertOperation) {
            replaceContent(isShortcutKeys);
            return;
        }

        const notDisplay = !!localStorage.getItem(
            `${myUid || translateDataRef.current.myUid}_not_display_editor_translate_replace_modal`
        );
        // 打开弹窗：内容中存在加粗、斜体、下换线 且 没有点击 「不再提醒」，出现弹窗
        const getMessageValueFn = isShortcutKeys ? translateDataRef.current.getMessageValue : getMessageValue;
        if (getMessageValueFn) {
            const nodes = getMessageValueFn();
            const isShowReplaceModal = nodes?.nodes && hasRichTextStyle(nodes?.nodes);
            if (!notDisplay && isShowReplaceModal) {
                setShowReplaceModal(isShowReplaceModal);
                if (isShortcutKeys) {
                    translateDataRef.current.blur?.();
                }
                return;
            }
        }

        // 用译文替换编辑器内容
        replaceContent(isShortcutKeys);
    };

    const updateTooltipVisibility = () => {
        const currentTop = buttonRef.current?.getBoundingClientRect()?.top;
        if (currentTop !== undefined && currentTop !== prevPosition.top) {
            // 如果位置变化，隐藏Tooltip
            setToolTipVisible(false);
            setPrevPosition({top: currentTop});
        }
    };

    const handleClick = () => {
        // 更新 Tooltip 可见性
        updateTooltipVisibility();

        if (undoHistory && withdrawTranslation) {
            withdrawTranslation();
            return;
        }
        if (translateData?.status === TranslateReqStatus.SUCCESS) {
            handleReplaceModal();
        }
    };

    useEffect(() => {
        myUid = configure.getMyUid()?.toString() || '';
        translateDataRef.current.myUid = myUid;
        imEventEmitter.on('editor-translate:replace', () => {
            if (translateDataRef.current.translateData?.status === TranslateReqStatus.SUCCESS) {
                handleReplaceModal(true);
            }
        });
        return () => {
            imEventEmitter.off('editor-translate:replace');
        };
    }, []);

    const handleSelectChange = async (optionValue: OptionValue) => {
        if (!currentSessionSubKey) return;
        if (!['replace', 'insert'].includes(optionValue.value)) {
            const [setError, setRes] = await to(
                rootStore.config.dxsdkConfig.setDoubleLevelUserConfig(
                    DoubleLevelConfigKeys.INPUT_TRANSLATE,
                    currentSessionSubKey,
                    optionValue.value
                )
            );
            if (setError || !setRes || !setRes.isSuccess) {
                showToast(i18n.$t('im_toggle_language_failed', '切换失败，请稍后重试'), {type: 'error'});
                logger.error('set_translate_sub_key', setRes, currentSessionSubKey);
                return;
            }
            setSelectValue({...selectValue, ...optionValue});
            return;
        }
        const [setError, setRes] = await to(
            rootStore.config.dxsdkConfig.setDoubleLevelUserConfig(
                DoubleLevelConfigKeys.INPUT_TRANSLATE_OPERATION,
                currentSessionSubKey,
                optionValue.value
            )
        );
        if (setError || !setRes || !setRes.isSuccess) {
            showToast(i18n.$t('im_toggle_language_failed', '切换失败，请稍后重试'), {type: 'error'});
            logger.error('set_translate_sub_key', 'operation', setRes, currentSessionSubKey);
            return;
        }
        lxAnalytics(
            'moduleClick',
            optionValue.value === TranslateActionConfig.INSERT ? 'b_oa_l25oftf8_mc' : 'b_oa_a1xbaxz8_mc',
            null,
            {
                isLeave: false,
                cid: 'c_oa_chat'
            }
        );
        setSelectValue({...selectValue, translateOperation: optionValue.value as TranslateActionConfig});
    };

    /**
     * 关闭边写边译功能
     */
    const handleTranslateClose = () => {
        if (!currentSessionSubKey) {
            showToast(i18n.$t('im_request_failed', '请求失败'), {type: 'error'});
            logger.error('editor_translate_toggle_fail', JSON.stringify(sessionId || ''));
            return;
        }
        handleEditorTranslateTurnOff(currentSessionSubKey, rootStore.config.dxsdkConfig.deleteDoubleLevelUserConfig);
        // 关闭边写边译聚焦编辑框
        imEventEmitter.emit('editor:focus');
    };

    const onRetry = debounce(
        () => {
            const editorHandles = getEditorHandles();
            if (editorHandles && sessionId && targetLanguage) {
                translate({
                    editorHandles,
                    targetLanguage,
                    sessionId
                });
            }
        },
        300,
        {leading: true, trailing: false}
    );

    const renderTranslateOperation = () => {
        if (undoHistory) {
            return <DXIcon type="dxicon3-undo-bjtp" />;
        }
        if (translateOperation === TranslateActionConfig.INSERT) {
            return i18n.$t('im_insert_translation_to_editor', '插入');
        }
        return i18n.$t('im_replace_translation_to_editor', '替换');
    };

    return (
        <div
            className={classNames('editor-translation', {
                'full-screen': isFullScreenMode,
                'has-quote-content': hasQuoteContent
            })}
            ref={translationRef}
        >
            <EditorTranslationGuideTooltip isShow={!!featureGuide.data['editor-translation']?.['is-show-tooltip']}>
                <Select
                    className={classNames('translation-lan', {
                        'max-width': selectValue.value === TranslateLang.ZH_HK,
                        [selectValue.value]: selectValue.value
                    })}
                    value={selectValue}
                    optionLabelProp="originOption"
                    onChange={handleSelectChange}
                    popLayer={{width: 'auto', getContainer: () => document.body, className: 'translation-lan-popup'}}
                    filterable={false}
                >
                    {translationSelectGroups.map((group, index) => (
                        <Group key={group.label} label={group.label}>
                            {group.options.map(({label, value, content}) => (
                                <Option
                                    key={label}
                                    value={value}
                                    originOption={label}
                                    className={classNames({
                                        'translate-select-operation': value === selectValue.translateOperation
                                    })}
                                >
                                    {index === 0 && <span className={`lan-option-label ${label}`}>{label}</span>}
                                    <span className="lan-option-content">{content}</span>
                                </Option>
                            ))}
                        </Group>
                    ))}
                </Select>
            </EditorTranslationGuideTooltip>
            <TranslationPanel translateData={translateData} targetLanguage={targetLanguage} onRetry={onRetry} />
            <div className="translation-actions">
                {showReplaceModal && (
                    <ReplaceModal
                        replaceContent={replaceContent}
                        closeReplaceModal={closeReplaceModal}
                        myUid={translateDataRef.current.myUid || myUid}
                    />
                )}

                <Tooltip
                    className={`replace-btn-tooltips ${isFullScreenMode ? 'full-screen-replace-btn-tooltips' : ''}`}
                    visible={toolTipVisible}
                    message={undoHistory ? withdrawShortcutKey : replaceShortcutKey}
                    autoDestory
                >
                    <div
                        ref={buttonRef}
                        className={
                            undoHistory
                                ? 'withdraw-btn'
                                : `replace-btn ${translateData?.status === TranslateReqStatus.SUCCESS ? '' : 'replace-btn-disable'}`
                        }
                        onClick={handleClick}
                        onMouseEnter={() => setToolTipVisible(true)}
                        onMouseLeave={() => setToolTipVisible(false)}
                    >
                        {renderTranslateOperation()}
                    </div>
                </Tooltip>
                <div className="close-btn" onClick={handleTranslateClose}>
                    <DXIcon type="dxicon3-close" />
                </div>
            </div>
        </div>
    );
};

export default observer(EditorTranslation);
