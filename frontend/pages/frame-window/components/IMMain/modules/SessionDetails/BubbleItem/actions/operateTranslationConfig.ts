import {i18n} from '@lib/i18n';
import {toJS} from 'mobx';
import {isTranslateMessage} from '@helpers/messages/check-type';
import {AI_CHANNEL, SECRET_CHAT_CHANNEL} from '@shared/common/constants';
import {canBeOperate, getRichNodesFromTranslation, getSubMenuKeys} from '@frontend/helpers/messages/translate';
import toJSON from '@helpers/toJSON';
import {GENERAL_MESSAGE_TYPE} from '@utils/message/constant';
import {type DxMessage, MESSAGE_TYPE, TranslateStatus} from '@xm/electron-imsdk-types';
import {lxAnalytics} from '@lib/lxAnalytics';
import configureSingleton from '@pages/frame-window/components/IMMain/stores/configure';
import {showForwardResultToast} from '@utils/forward';
import isMarkdownMessage from '@helpers/messages/text/isMarkdownMessage';
import im from '@pages/frame-window/components/IMMain/domains/im';
import autoTranslateMessageSingleton from '@pages/frame-window/components/IMMain/stores/messages/auto-translate';

import {popUp} from '#preload/internal';

import {type ActionConfig, ActionType} from './typings';

import type {DXMessageModel} from '@pages/frame-window/components/IMMain/stores/messages/dx-message-model';

import './operateTranslationConfig.scss';

export const packUpTranslation = (dxMessage: DxMessage) => {
    im.call('message.dxHandleTranslateShowStatus', [
        {
            show: false,
            msg: toJS(dxMessage.message),
            sessionId: toJS(dxMessage.sessionId)
        }
    ]);
    autoTranslateMessageSingleton.updateTranslationFoldedMsgs(dxMessage.message.mid);
    lxAnalytics(
        'moduleClick',
        'b_oa_dzpg5z9q_mc',
        {
            custom: {
                messageId: dxMessage.mid,
                messageType: dxMessage.message.type,
                uid: configureSingleton.getMyUid()?.toString(),
                sessionId: dxMessage.sessionId.uid,
                sessionType: dxMessage.sessionId.type
            }
        },
        {
            isLeave: false,
            cid: 'c_oa_chat'
        }
    );
};

const getOperationMenus = (dxMessageModel: DXMessageModel) => {
    const dxMessage = dxMessageModel.message;
    const {message} = dxMessage;
    const msgData = toJSON(toJS(message));
    const translation = dxMessageModel.message.translate;
    const invalidForwardMessageTypes = [
        MESSAGE_TYPE.MSG_TYPE_CUSTOM,
        MESSAGE_TYPE.MSG_TYPE_LINK,
        MESSAGE_TYPE.MSG_TYPE_MULTI_LINK
    ];
    const isMessageTypeForwardValid = !invalidForwardMessageTypes.includes(message.type);

    const menus = [
        {
            key: 'pack-up',
            className: 'dx-menu-item-operateTranslation',
            onClick: () => {
                packUpTranslation(dxMessage);
            },
            // icon: 'dxicon3-up',
            text: i18n.$t('im_pack_up', '收起'),
            role: ActionType.PackupTranslation
        }
    ];
    // 有翻译数据时才有转发
    if (isMessageTypeForwardValid && translation && translation.translateStatus === TranslateStatus.TRANSLATED) {
        menus.push({
            key: 'forward',
            className: 'dx-menu-item-operateTranslation',
            // icon: 'dxicon3-forward',
            text: i18n.$t('im_message_forward', '转发'),
            role: ActionType.ForwardTranslation,
            onClick: () => {
                lxAnalytics(
                    'moduleClick',
                    'b_oa_dzpg5z9q_mc',
                    {
                        custom: {
                            messageId: dxMessage.mid,
                            messageType: message.type,
                            uid: configureSingleton.getMyUid()?.toString(),
                            sessionId: dxMessage.sessionId.uid,
                            sessionType: dxMessage.sessionId.type
                        }
                    },
                    {
                        isLeave: false,
                        cid: 'c_oa_chat'
                    }
                );

                const result = getRichNodesFromTranslation(dxMessage);
                if (!result) return;
                let messageBody = msgData.body;
                if (result.length === 1 && (result[0] as {c: string; t: string}).t === 'text') {
                    messageBody = {text: (result[0] as {c: string; t: string}).c};
                    msgData.type = MESSAGE_TYPE.MSG_TYPE_TEXT;
                } else {
                    messageBody = {
                        data: JSON.stringify({nodes: result}),
                        type: GENERAL_MESSAGE_TYPE.TYPE_RICH,
                        summary: ''
                    };
                    msgData.type = MESSAGE_TYPE.MSG_TYPE_GENERAL;
                }
                popUp
                    .showPopUp('createForward', {forwardMsg: Object.assign(msgData, {body: messageBody})})
                    .then(res => {
                        showForwardResultToast(res);
                    });
            }
        });
    }

    return menus;
};

const operateTranslationConfig: ActionConfig = {
    condition: dxMessageModel => {
        if (isMarkdownMessage(dxMessageModel.message.message)) return false;
        const {message} = dxMessageModel.message;
        // 无痕会话和 AI 不翻译
        if (message.channelId === SECRET_CHAT_CHANNEL || message.channelId === AI_CHANNEL) {
            return false;
        }
        const subMenuKeys = getSubMenuKeys(dxMessageModel.message.translate, message.type);
        if (subMenuKeys.length === 1 && subMenuKeys[0] !== 'pack-up') return false;
        // 支持翻译的消息：纯文本、富文本、引用回复（纯文本、富文本）
        if (isTranslateMessage(message) && canBeOperate(dxMessageModel.message.translate)) return true;
        return false;
    },
    action: dxMessageModel => {
        const subMenuKeys = getSubMenuKeys(dxMessageModel.message.translate, dxMessageModel.message.message.type);
        if (subMenuKeys.length === 1 && subMenuKeys[0] === 'pack-up') {
            packUpTranslation(dxMessageModel.message);
        }
    },
    meta: dxMessageModel => {
        const {message} = dxMessageModel.message;
        const subMenuKeys = getSubMenuKeys(dxMessageModel.message.translate, message.type);
        const text = (() => {
            if (subMenuKeys.length === 1) {
                if (subMenuKeys[0] === 'pack-up') {
                    return i18n.$t('im_pack_translation', '收起译文');
                }
                return '';
            }
            return i18n.$t('im_translate_into_english', '对译文进行');
        })();

        return {
            type: ActionType.OperateTranslation,
            icon: 'dxicon3-fanyi',
            text,
            children: subMenuKeys.length > 1 ? getOperationMenus : undefined
        };
    }
};

export default operateTranslationConfig;
