import React, {useRef} from 'react';
import {observer} from 'mobx-react';
import {i18n} from '@lib/i18n';
import {Button} from '@ss/mtd-react';
import {DXIcon, to} from '@xm/elefanto';
import {lxAnalytics} from '@lib/lxAnalytics';
import {DXTooltip} from '@pages/frame-window/components/IMMain/components/DXTooltip';
import {useStores} from '@pages/frame-window/components/IMMain/stores';
import {DoubleLevelConfigKeys} from '@xm/electron-imsdk-types';
import {showToast} from '@components/Toast';
import logger from '@pages/frame-window/components/IMMain/domains/logger';
import {LocalStorageDoubleLevelKey, removeLocalStorageSubItem} from '@helpers/local-storage';
import {
    handleEditorTranslateTurnOff,
    getDefaultLanguage
} from '@pages/frame-window/components/IMMain/helpers/translate';
import imEventEmitter from '@pages/frame-window/components/IMMain/domains/event/im';
import {useRootStore} from '@pages/frame-window/components/IMMain/RootStoreContext';

import type {EditorTranslateToolOption} from '@pages/frame-window/components/IMMain/stores/editPanel/typings';

export const EditorTranslate = observer(({option}: {option: EditorTranslateToolOption}) => {
    const rootStore = useRootStore();
    const {dxsdkConfig} = rootStore.config;
    const {editPanelStore, sessions} = useStores();
    const {targetLanguage, currentSessionSubKey} = editPanelStore.translator;
    const toolTitle = targetLanguage
        ? i18n.$t('im_editor_translate_turn_off', '关闭边写边译')
        : i18n.$t('im_editor_translate_turn_on', '开启边写边译');

    const timer = useRef<NodeJS.Timeout | null>(null);

    const handleClick = async () => {
        //开启时，上报埋点
        if (!targetLanguage) {
            lxAnalytics(
                'moduleClick',
                'b_oa_d1s34hq8_mc',
                {
                    custom: {
                        sessionType: sessions.currentSession?.dxSession?.sessionId?.type
                    }
                },
                {
                    isLeave: false,
                    cid: 'c_oa_chat'
                }
            );
        }

        if (!currentSessionSubKey) {
            showToast(i18n.$t('im_request_failed', '请求失败'), {type: 'error'});
            logger.error('editor_translate_toggle_fail', sessions.currentSession?.dxSession.sessionId);
            return;
        }

        // 关闭
        if (targetLanguage) {
            handleEditorTranslateTurnOff(currentSessionSubKey, dxsdkConfig.deleteDoubleLevelUserConfig);
            // 关闭边写边译聚焦编辑框
            imEventEmitter.emit('editor:focus');
            return;
        }

        const defaultLang = getDefaultLanguage(currentSessionSubKey, dxsdkConfig.configs.globalInputTranslate);
        const [setError, setRes] = await to(
            dxsdkConfig.setDoubleLevelUserConfig(
                DoubleLevelConfigKeys.INPUT_TRANSLATE,
                currentSessionSubKey,
                defaultLang
            )
        );
        if (setError || !setRes || !setRes.isSuccess) {
            showToast(i18n.$t('im_turn_on_editor_translate_failed', '开启边写边译失败，请稍后重试'), {type: 'error'});
            logger.error('set_translate_sub_key', setRes, currentSessionSubKey);
            return;
        }
        removeLocalStorageSubItem(LocalStorageDoubleLevelKey.TRANSLATE_DEFAULT_LANG, currentSessionSubKey);
        // 打开边写边译聚焦编辑框
        imEventEmitter.emit('editor:focus');
    };

    const throttledClick = () => {
        if (timer.current) return;
        timer.current = setTimeout(() => {
            timer.current && clearTimeout(timer.current);
            timer.current = null;
        }, 300);
        handleClick();
    };

    return option.type === 'moreMenu' ? (
        <div className="editor-tool-more-menu-item" onClick={throttledClick}>
            <div className="left-side">
                <DXIcon type="dxicon3-bianxiebianyi3" />
                <span>{toolTitle}</span>
            </div>
        </div>
    ) : (
        <DXTooltip message={toolTitle}>
            <Button className="msg-panel-btn" shape="circle" hoverShape onClick={throttledClick}>
                <DXIcon type="dxicon3-bianxiebianyi3" />
            </Button>
        </DXTooltip>
    );
});
