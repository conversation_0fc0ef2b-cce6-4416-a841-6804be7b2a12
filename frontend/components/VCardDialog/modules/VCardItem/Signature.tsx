import React, {useEffect, useRef, useState} from 'react';
import classnames from 'classnames';
import {DXIcon} from '@xm/elefanto';
import ParseText from '@components/Message/tools/parseText';

import {getLines} from '../../helpers';
import DXTextMessageParser from '@frontend/components/DXTextParser/DXTextMessageParser';

export const Signature = (props: {needLimitRows: boolean; defaultDesc?: string; desc: string; className?: string}) => {
    const {needLimitRows, defaultDesc, desc, className} = props;
    const [overflow, setOverFlow] = useState(false);
    const [isFold, setIsFold] = useState(true);
    const descRef = useRef<HTMLDivElement>(null);
    const cls = classnames({
        signature: true,
        [className || '']: className,
        limitRow: needLimitRows
    });
    const descCls = classnames({
        'text-vcard': true,
        hidden: overflow && isFold
    });
    const arrowCls = classnames({
        arrow: true,
        up: !isFold
    });
    const setOverflowFlag = (flag: boolean) => {
        setOverFlow(flag);
        setIsFold(true);
    };
    useEffect(() => {
        if (needLimitRows && desc) {
            if (descRef.current) {
                const height = getLines(descRef?.current);
                const THREE_LINE_HEIGHT = 18 * 3;
                if (height > THREE_LINE_HEIGHT) {
                    setOverflowFlag(true);
                } else {
                    setOverflowFlag(false);
                }
            }
        }
    }, [desc]);
    const arrowClickHandler = () => {
        setIsFold(!isFold);
    };
    return (
        <div className={cls}>
            <div className={descCls} ref={descRef}>
                <DXTextMessageParser text={desc ? desc.replace(/(\n|\r)/g, '') : defaultDesc || ''} />
            </div>
            {overflow ? <DXIcon type="dxicon3-down-thick" className={arrowCls} onClick={arrowClickHandler} /> : null}
        </div>
    );
};
