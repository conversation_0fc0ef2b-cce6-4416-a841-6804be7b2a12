.simple-bubble-item {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    gap: 10px;

    &-avatar {
        // 其实这句没啥用，重复了，主要是为了过 style lint，不留空白 block
        justify-self: flex-start;
    }

    &-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
        width: 0;

        .content-header {
            display: flex;
            flex-direction: row;
            // height: 16px;
            margin-bottom: 3px;
            // text-overflow: ellipsis;
            // padding-right: 8px;
            font-size: 12px;
            line-height: 16px;
            white-space: pre;
            color: #596E8F;
        }

        .content-sender {
            line-height: normal;
            height: 16px;
            margin-bottom: 3px;
            max-width: 100%;
            display: flex;
            flex-wrap: nowrap;
            white-space: nowrap;

            &-name {
                padding-right: 8px;
                position: relative;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 12px;
                line-height: 16px;
                color: #596E8F;
            }

            &-role-name {
                border-radius: 2px;
                font-size: 10px;
                cursor: default;
                color: rgb(0 0 0 / 35%);
                letter-spacing: 0;
                text-align: center;
                line-height: 12px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding-bottom: 1px;
                vertical-align: middle;
            }

            &-time {
                font-size: 12px;
                color: rgb(0 0 0 / 36%);
                margin-left: 4px;
            }
        }

        .content-message {
            // 默认气泡样式
            box-sizing: border-box;
            // border: 1px solid #eee;
            border-radius: 8px;
            padding: 10px;
            background-color: #fff;
            word-break: break-word;
            max-width: 226px;

            // 文件类型
            &.file {
                border: 1px solid rgb(0 0 0 / 12%);
                border-radius: 10px;
                padding: 8px 8px 7px;
            }

            // 文本类型
            &.text,
            &.tt {
                // border: 1px solid #eee;
                border-radius: 8px;
                padding: 8px 11px;
            }

            &.image {
                padding: 4px;
                border-radius: 8px;
                line-height: 0;
                border: none;
            }

            &.link {
                border-radius: 10px;
                padding: 0;
                width: 206px;
                border: none;

                // TODO: UI 说去掉 border，但是我觉得对比太不明显了，先保留注释，防止又要加回去
                // .message-link {
                //     border: 1px solid #eee;
                // }
            }

            &.new-emotion {
                border-radius: 0;
                border: none;
                padding: 0;
            }

            &.quote {
                border-radius: 8px;
                padding: 10px;
                border: none;
            }

            &.card {
                border-radius: 8px;
                border: none;
                padding: 0;
            }

            &.voice {
                padding: 0;
            }
        }

    }

    &.left {
        padding: 11px 12px 6px 16px;
        flex-direction: row;

        .simple-bubble-item-content {
            align-items: start;

        }
    }

    &.right {
        padding: 11px 16px 6px 12px;
        flex-direction: row-reverse;

        .content-name {
            visibility: hidden;
        }

        .content-sender-name {
            padding-right: 0;
        }


        .simple-bubble-item-content {
            align-items: end;

            .content-message {
                // 消息于右侧用户发出时的气泡框样式

                // 文本类型
                &.text {
                    background-image: url("https://pc.neixin.cn/pan/pre/bgw/img/web?rgb=D0E7FF");
                    background-color: #D0E7FF;
                }

                &.quote {
                    background-image: url("https://pc.neixin.cn/pan/pre/bgw/img/web?rgb=D0E7FF");
                    background-color: #D0E7FF;
                }

                &.general {
                    background-image: url("https://pc.neixin.cn/pan/pre/bgw/img/web?rgb=D0E7FF");
                    background-color: #D0E7FF;
                }
            }
        }
    }

    .content-message {
        .translate-content {
            padding: 0;
        }

        .rich-text-translate {
            padding-top: 8px;
            margin-top: 10px;

            .translate-loading,
            .translate-tips-error {
                margin: 0;
            }

            .translate-feedback {
                padding-bottom: 0;
                margin-right: 0;
            }
        }

        &.link,
        &.multi-link,
        &.custom {
            background-color: transparent;
        }
    }
}

.simple-bubble-item.show-status .simple-bubble-item-content {
    flex: unset;
    width: fit-content;

    .content-message {
        max-width: 196px;
    }
}

/* stylelint-disable-next-line no-duplicate-selectors */
.simple-bubble-item-content {
    .rich-text-translate {
        .translate-footer {
            padding: 0;
        }
    }
}
