import {i18n} from '@lib/i18n';
import {toJS} from 'mobx';
import {isTranslateMessage} from '@helpers/messages/check-type';
import {AI_CHANNEL, SECRET_CHAT_CHANNEL} from '@shared/common/constants';
import {
    canBeOperate,
    getRichNodesFromTranslation,
    type translateOptions,
    getSubMenuKeys
} from '@frontend/helpers/messages/translate';
import toJSON from '@helpers/toJSON';
import {GENERAL_MESSAGE_TYPE} from '@utils/message/constant';
import {type DxMessage, type Message, MESSAGE_TYPE} from '@xm/electron-imsdk-types';
import {showForwardResultToast} from '@utils/forward';
import isMarkdownMessage from '@helpers/messages/text/isMarkdownMessage';

import {imsdk, popUp} from '#preload/internal';

import {type ActionConfig, ActionType} from './typings';

import type {MessageOptions} from '@frontend/components/Message/type';

export const packUpTranslation = (message: Message, translateOptions?: translateOptions) => {
    const {fromSessionId, updateTranslationFoldedMsgs} = translateOptions || {};
    const sessionId = {
        channelId: message.channelId,
        type: message.sessionType,
        uid: message.belongTo,
        sid: message.sid
    };

    imsdk.getClient().then(client => {
        client.call('message.dxHandleTranslateShowStatus', [
            {
                show: false,
                msg: toJS(message),
                sessionId: fromSessionId || sessionId
            }
        ]);
    });
    updateTranslationFoldedMsgs?.(message.mid);
};

const getOperationMenus = (message: Message, options: MessageOptions | undefined) => {
    const translation = options?.translateOptions?.translation;
    const subMenuKeys = getSubMenuKeys(translation, message.type);
    const msgData = toJSON(toJS(message));

    return subMenuKeys.map(key => {
        if (key === 'pack-up') {
            return {
                className: 'dx-menu-item-operateTranslation',
                onClick: () => {
                    packUpTranslation(message, options?.translateOptions);
                },
                text: i18n.$t('im_pack_up', '收起'),
                role: ActionType.PackupTranslation
            };
        }

        if (key === 'forward') {
            return {
                className: 'dx-menu-item-operateTranslation',
                // icon: 'dxicon3-forward',
                text: i18n.$t('im_message_forward', '转发'),
                role: ActionType.ForwardTranslation,
                onClick: () => {
                    const result = getRichNodesFromTranslation({message, translate: translation} as DxMessage);
                    if (!result) return;
                    let messageBody = msgData.body;
                    if (result.length === 1 && (result[0] as {c: string; t: string}).t === 'text') {
                        messageBody = {text: (result[0] as {c: string; t: string}).c};
                        msgData.type = MESSAGE_TYPE.MSG_TYPE_TEXT;
                    } else {
                        messageBody = {
                            data: JSON.stringify({nodes: result}),
                            type: GENERAL_MESSAGE_TYPE.TYPE_RICH,
                            summary: ''
                        };
                        msgData.type = MESSAGE_TYPE.MSG_TYPE_GENERAL;
                    }
                    popUp
                        .showPopUp('createForward', {forwardMsg: Object.assign(msgData, {body: messageBody})})
                        .then(res => {
                            showForwardResultToast(res);
                        });
                }
            };
        }

        return null;
    });
};

const operateTranslationConfig: ActionConfig = {
    condition: (message, actions, {messageOptions}) => {
        if (!actions?.includes(ActionType.OperateTranslation)) return false;
        if (isMarkdownMessage(message)) return false;

        // 无痕会话和 AI 不翻译
        if (message.channelId === SECRET_CHAT_CHANNEL || message.channelId === AI_CHANNEL) {
            return false;
        }

        const translation = messageOptions?.translateOptions?.translation;
        // 支持翻译的消息：纯文本、富文本、引用回复（纯文本、富文本）
        if (isTranslateMessage(message) && canBeOperate(translation)) return true;

        const subMenuKeys = getSubMenuKeys(translation, message.type);
        if (subMenuKeys.length === 1 && subMenuKeys[0] !== 'pack-up') return false;
        return false;
    },
    action: (message, options) => {
        const subMenuKeys = getSubMenuKeys(options?.messageOptions?.translateOptions?.translation, message.type);

        if (subMenuKeys.length === 1 && subMenuKeys[0] === 'pack-up') {
            packUpTranslation(message, options?.messageOptions?.translateOptions);
        }
    },
    meta: (message, options) => {
        const subMenuKeys = getSubMenuKeys(options?.translateOptions?.translation, message.type);
        const text = (() => {
            if (subMenuKeys.length === 1) {
                if (subMenuKeys[0] === 'pack-up') {
                    return i18n.$t('im_pack_translation', '收起译文');
                }
                return '';
            }
            return i18n.$t('im_translate_into_english', '对译文进行');
        })();

        return {
            type: ActionType.OperateTranslation,
            icon: 'dxicon3-fanyi',
            text,
            children: subMenuKeys.length > 1 ? getOperationMenus : undefined
        };
    }
};

export default operateTranslationConfig;
