/* eslint-disable @typescript-eslint/no-use-before-define */
import React, {useRef, useEffect, forwardRef, useImperativeHandle, useCallback} from 'react';
import {DOMParser} from 'prosemirror-model';
import {Providers} from '@it/parker/es/core';
import {i18n} from '@lib/i18n';
import {useLocalObservable} from 'mobx-react';
import {AllSelection, Selection, TextSelection} from 'prosemirror-state';
import {__endComposition, type EditorView} from 'prosemirror-view';
import {owlService} from '@frontend/utils/services/owl';

import IMEditor, {type EditorConfig} from './IMEditor';
import {getDefaultMessageData, msg2html, pm2draft, pm2msg, pm2markdownText} from './helpers/transformData';
import {isEmptyDoc} from './helpers/common';
import {isMentionNode, isPMImageNode} from './helpers/type-check';
import {getReplaceParams} from './helpers/replace';
import {findNodes} from './helpers/find';
import {CUSTOM_KEY} from './helpers/constants';

import type {PMNode} from './typings';
import type {EmojiAttrs} from './schema/nodes/emoji';
import type {getInsertImageCommand} from './extensions/image-extension/helper';
import type {getInsertLinkCommand} from './extensions/link-extension/helpers/insert';
import type {RTMNode, RichTextMessageBodyData} from 'typings/RichTextMessage';
import type {IUserDraftStorage} from '@shared/types';
import type {AllValidUids} from '@pages/frame-window/components/IMMain/stores/editPanel/EditorMessageItem';
import type {PMMentionNodeAttrs} from './schema/nodes/mention';

import './index.scss';

interface IEditorProps extends EditorConfig {
    disabledFeat?: string[];
    initProviders?: () => typeof Providers;
    onBlur?: () => void;
    onFocus?: () => void;
    onEditorReady?: (editorIns: IMEditor) => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLDivElement>) => void;
    onCopy?: (e: Event) => void;
    onCut?: (e: Event) => void;
    /** 受控 placeholder */
    placeholder?: string;
}

type InsertImageParams = Parameters<typeof getInsertImageCommand>[0];
type InsertLinkParams = Parameters<typeof getInsertLinkCommand>[0];
interface InsertNodeParamsMap {
    image: InsertImageParams;
    link: InsertLinkParams;
    mention: PMMentionNodeAttrs;
    emoji: EmojiAttrs;
}
type InsertNodeType = keyof InsertNodeParamsMap;

export type InsertNode<Type extends InsertNodeType = InsertNodeType> = (
    type: Type,
    attrs: InsertNodeParamsMap[Type]
) => void;

export type SetValueParams =
    | {
          nodes: RTMNode[];
          isRichText: boolean;
          overwrite?: boolean;
          customData?: Record<string, any>;
      }
    | {
          htmlValue: string;
      };

export type GetMessageValueOptions = {
    filterSpaces?: boolean;
    filterEmptyLines?: boolean;
};

export interface IEditorHandles {
    getMessageValue: (options?: GetMessageValueOptions) => RichTextMessageBodyData;
    getTextValue: () => string;
    isEmpty: (option?: {enableMultiEmptyLine?: boolean}) => boolean;
    setValue: (params: SetValueParams) => void;
    withdrawTranslation: () => void;
    clearContent: () => void;
    insertNode: InsertNode;
    insertText: (str: string, isReplaceAll?: boolean) => boolean;
    getCommands: () => Record<string, any>;
    focus: () => void;
    blur: () => void;
    getDraft: () => IUserDraftStorage;
    getImageLength?: () => number;
    updateMentionNodeStatus: () => void;
    getView: () => EditorView | undefined;
    getAllValidUids: () => AllValidUids;
    /** 光标滚动到视口 */
    scrollIntoView: () => void;
}

/** 编辑框默认 placeholder */
const DEFAULT_PLACEHOLDER = i18n.$t('editor_placeholder', '说点什么...');

/**
 * 富文本编辑框组件。
 * @returns
 */
export const RichTextEditor = forwardRef<IEditorHandles, IEditorProps>((props, ref) => {
    const editor = useRef<HTMLDivElement>(null);
    const editorIns: React.MutableRefObject<any> = useRef(null);
    const propsRef = useRef(props);

    const localStore = useLocalObservable(() => ({
        isEditorReady: false,

        setIsEditorReady(isReady: boolean) {
            localStore.isEditorReady = isReady;
        }
    }));

    // 这里的写法很 hack，原因是 editorIns 的初始化只能执行一次，所以初始化的时候传入的函数都是旧函数。
    useEffect(() => {
        propsRef.current = props;
    });

    const getMessageValue = (options?: GetMessageValueOptions) => {
        if (!editorIns.current) return getDefaultMessageData();
        return pm2msg(editorIns.current.manager.editorView.state.doc, options);
    };

    const getTextValue = () => {
        return pm2markdownText(editorIns.current.manager.editorView.state.doc);
    };

    const getDraft = (): IUserDraftStorage => {
        return pm2draft(editorIns.current.manager.editorView.state.doc);
    };

    const isEmpty = (option?: {enableMultiEmptyLine?: boolean}) => {
        return isEmptyDoc(editorIns.current.manager.editorView.state.doc, option);
    };

    const clearContent = () => {
        if (!editorIns.current) return;
        const view: EditorView = editorIns.current.manager.editorView;
        const {doc} = view.state;
        const tr = view.state.tr
            .replaceWith(0, doc.content.size, view.state.schema.nodes.paragraph.create())
            .scrollIntoView();
        tr.setSelection(new TextSelection(tr.doc.resolve(1)));
        view.dispatch(tr);
    };

    const insertNode: InsertNode = (type, attrs) => {
        editorIns.current?.manager.commands?.[type]?.insert(attrs);
    };

    const insertText = (str: string, isReplaceAll = false) => {
        if (editorIns.current) {
            const view = editorIns.current.manager.editorView;
            if (isReplaceAll) {
                // 替换已有全部内容
                view.dispatch(view.state.tr.insertText(str, 0, view.state.doc.content.size));
            } else {
                view.dispatch(view.state.tr.insertText(str));
            }
            view.focus();
            return true;
        }
        return false;
    };

    const getImageLength = () => {
        if (!editorIns.current) return 0;
        const {editorView} = editorIns.current.manager;
        const {doc} = editorView.state;
        let imageLength = 0;
        doc.descendants((node: PMNode) => {
            if (!isPMImageNode(node)) return true;
            imageLength++;
            return true;
        });
        return imageLength;
    };

    const updateMentionNodeStatus = () => {
        editorIns.current?.manager?.commands?.mention?.updateMentionNodeStatus?.();
    };

    const setValue = useCallback(
        ({
            nodes,
            isRichText,
            htmlValue,
            overwrite,
            customData
        }: {
            nodes?: RTMNode[];
            isRichText?: boolean;
            htmlValue?: string;
            overwrite?: boolean;
            customData?: Record<string, any>;
        }) => {
            if (!editorIns.current) return;
            let html = '';
            if (nodes && isRichText !== undefined) {
                html = msg2html(nodes, isRichText);
            } else if (htmlValue) {
                html = htmlValue;
            }

            const tmpNode = document.createElement('div');
            tmpNode.innerHTML = html;

            const view: EditorView = editorIns.current.manager.editorView;
            const parser = DOMParser.fromSchema(view.state.schema);
            const insertNodes = parser.parse(tmpNode);

            const replaceSelection = overwrite ? new AllSelection(view.state.doc) : view.state.selection;

            const {from, to, slice} = getReplaceParams({
                view,
                replaceSelection,
                fragment: insertNodes.content
            });

            const tr = view.state.tr.replace(from, to, slice);
            const sliceLastContentNode = slice.content.lastChild;
            if (!sliceLastContentNode) return;
            const mappedToRes = tr.doc.resolve(tr.mapping.map(to));
            const mappedSelection = Selection.near(mappedToRes, -1);
            tr.setSelection(mappedSelection);
            if (customData) tr.setMeta(CUSTOM_KEY, customData);

            view.dispatch(tr);
            view.focus();
            view.dispatch(view.state.tr.scrollIntoView());

            editorIns.current.manager?.commands?.mention?.updateMentionNodeStatus?.();
            __endComposition(view);
            return;
        },
        []
    );

    const withdrawTranslation = useCallback(() => {
        if (!editorIns.current) return;
        editorIns.current.manager?.commands?.history?.undo?.();
        const view: EditorView = editorIns.current.manager.editorView;
        view.focus();
        view.dispatch(view.state.tr.scrollIntoView());
    }, []);

    /** 光标滚动到视口 */
    const scrollIntoView = useCallback(() => {
        if (!editorIns.current?.manager?.editorView) {
            return;
        }
        const view: EditorView = editorIns.current.manager.editorView;
        view.dispatch(view.state.tr.scrollIntoView());
    }, []);

    useImperativeHandle(ref, () => {
        return {
            getMessageValue,
            getTextValue,
            isEmpty,
            setValue,
            withdrawTranslation,
            clearContent,
            insertNode,
            insertText,
            getCommands: () => editorIns.current?.manager?.commands || {},
            focus,
            blur,
            getDraft,
            getImageLength,
            updateMentionNodeStatus,
            getView: () => editorIns.current?.manager?.editorView as EditorView | undefined,
            getAllValidUids: (): AllValidUids => {
                const view: EditorView = editorIns.current?.manager?.editorView;
                const mentionNodes = findNodes(view.state.doc, isMentionNode);
                const allValidUidsSet = mentionNodes.reduce(
                    (res, findNodeRes) => {
                        const {node} = findNodeRes;
                        if (!isMentionNode(node)) return res;
                        if (node.attrs.valid) res[node.attrs.type].add(node.attrs.uid);
                        return res;
                    },
                    {
                        user: new Set<string>(),
                        bot: new Set<string>(),
                        all: new Set<string>()
                    }
                );
                return {
                    user: Array.from(allValidUidsSet.user.values()),
                    bot: Array.from(allValidUidsSet.bot.values()),
                    all: Array.from(allValidUidsSet.all.values())
                };
            },
            scrollIntoView
        };
    }, []);

    useEffect(() => {
        if (!editor.current) return;
        try {
            const providers = props.initProviders?.() || new Providers();
            const imEditor = new IMEditor(editor.current, {
                placeholder: DEFAULT_PLACEHOLDER,
                disabledFeat: props.disabledFeat || [],
                providers,
                callbackFns: {
                    ...propsRef.current
                }
            });
            imEditor.init(() => onEditorReady(imEditor));
        } catch (e: any) {
            owlService.addError(
                {
                    name: `RichTextEditor 初始化失败`,
                    msg: e.stack
                },
                {
                    level: 'error',
                    tags: {
                        error: e.message
                    }
                }
            );
            // TODO: 旧埋点逻辑不符合规范，本次暂时只修复类型错误，下一个迭代重新写埋点逻辑。
            // report.withErrorLog({
            //     nm: '编辑器初始化失败',
            //     val: {
            //         error: editorInitErr
            //     }
            // });
        }

        return () => {
            if (editorIns.current) {
                editorIns.current.destroyEditor();
                editorIns.current.dom.remove();
            }
        };
    }, []);

    const focus = () => {
        const view: EditorView | undefined = editorIns?.current?.manager?.editorView;
        if (!view || view.hasFocus()) return;
        editorIns?.current?.manager?.editorView?.focus?.();
    };

    const blur = () => {
        const view: EditorView | undefined = editorIns?.current?.manager?.editorView;
        if (!view) return;
        if (view.hasFocus()) {
            editorIns?.current?.manager?.editorView?.dom?.blur?.();
        }
    };

    const handleBlur = () => {
        props.onBlur?.();
    };

    const handleFocus = () => {
        props.onFocus?.();
    };

    const onKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
        props.onKeyDown?.(e);
    };

    const onEditorReady = (imEditor: IMEditor) => {
        editorIns.current = imEditor;
        // TODO: 整合到 debug 对象中
        // 目前 TS 检查有点问题，全局的类型声明检测不到，先这么写
        Object.assign(window, {editorIns: imEditor, TextSelection});
        propsRef.current.onEditorReady?.(imEditor);
        localStore.setIsEditorReady(true);
    };

    useEffect(() => {
        if (!editorIns.current) return;
        if (!localStore.isEditorReady) return;
        const {editorView} = editorIns.current.manager;
        props.onCopy && editorView.dom.addEventListener('copy', props.onCopy);
        props.onCut && editorView.dom.addEventListener('cut', props.onCut);

        return () => {
            props.onCopy && editorView.dom.removeEventListener('copy', props.onCopy);
            props.onCut && editorView.dom.removeEventListener('cut', props.onCut);
        };
    }, [props.onCopy, props.onCut, localStore.isEditorReady]);

    useEffect(() => {
        if (!localStore.isEditorReady) return;
        updateMentionNodeStatus();
    }, [localStore.isEditorReady]);

    // 更新 placeholder
    useEffect(() => {
        if (!editorIns.current) {
            return;
        }
        const {commands} = editorIns.current.manager;
        commands.placeholder?.updatePlaceholder(props.placeholder || DEFAULT_PLACEHOLDER);
    }, [props.placeholder]);

    return (
        <div className="rich-text-editor-container">
            <div
                id="editor"
                ref={editor}
                onBlur={() => handleBlur()}
                onFocus={() => handleFocus()}
                onKeyDown={onKeyDown}
            />
        </div>
    );
});

export default RichTextEditor;
