import {describe, it, expect, vi, beforeEach} from 'vitest';

import {insertNodesToEnd} from './insertNodesToEnd';

// Mocks
vi.mock('@components/RichTextEditor/helpers/transformData', () => ({
    msg2html: vi.fn(() => '<p>hello</p>')
}));

// Minimal ProseMirror-like objects
class MockNode {
    content: {size: number; lastChild?: any};
    constructor(size: number) {
        this.content = {size};
    }
}

class MockSchema {}

class MockTr {
    doc: any;
    mapping: {map: (pos: number) => number};
    selection: any = null;
    private _meta: Map<any, any> = new Map();
    constructor(docSize: number) {
        this.doc = {content: {size: docSize}, resolve: (pos: number) => ({pos})};
        this.mapping = {map: (pos: number) => pos};
    }
    insert(_: number, content: any) {
        // simulate insertion increases size by content.size or 1
        const delta = content?.size ?? content?.content?.size ?? 1;
        this.doc.content.size += delta;
        return this;
    }
    setSelection(sel: any) {
        this.selection = sel;
        return this;
    }
    setMeta(key: any, val: any) {
        this._meta.set(key, val);
        return this;
    }
    scrollIntoView() {
        return this;
    }
}

class MockState {
    schema: any;
    doc: any;
    tr: any;
    constructor(docSize: number) {
        this.schema = new MockSchema();
        this.doc = new MockNode(docSize);
        this.tr = new MockTr(docSize);
    }
}

class MockView {
    state: any;
    dispatch = vi.fn();
    focus = vi.fn();
    dom = {} as any;
    constructor(docSize = 1) {
        this.state = new MockState(docSize);
    }
}

// Mock Selection.near
vi.mock('prosemirror-state', async () => {
    const mod = await vi.importActual<any>('prosemirror-state');
    return {
        ...mod,
        Selection: {
            near: vi.fn((res: any) => ({pos: res.pos}))
        }
    };
});

// Mock DOMParser.fromSchema and parse
const parseSpy = vi.fn(() => ({content: {size: 3}}));
vi.mock('prosemirror-model', async () => {
    const mod = await vi.importActual<any>('prosemirror-model');
    return {
        ...mod,
        DOMParser: {
            fromSchema: vi.fn(() => ({parse: parseSpy}))
        }
    };
});

// jsdom already provides document, but we ensure createElement returns a usable node
const createElSpy = vi.spyOn(document, 'createElement');

describe('insertNodesToEnd', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('should do nothing when view is undefined', () => {
        insertNodesToEnd({view: undefined, nodes: [], isRichText: false});
        expect(createElSpy).not.toHaveBeenCalled();
    });

    it('inserts parsed nodes at end, moves cursor, focuses and scrolls', () => {
        const view = new MockView(5);
        insertNodesToEnd({view: view as any, nodes: [{type: 'text', text: 'hello'}] as any, isRichText: true});

        // html -> tmp div created
        expect(createElSpy).toHaveBeenCalledWith('div');
        // DOMParser.parse called
        expect(parseSpy).toHaveBeenCalled();
        // dispatch called twice (insert + scroll)
        expect(view.dispatch).toHaveBeenCalledTimes(2);
        // focus called once
        expect(view.focus).toHaveBeenCalledTimes(1);
        // doc size increased by parsed content size (3)
        expect(view.state.tr.doc.content.size).toBe(5 + 3);
    });

    it('sets custom meta when provided', () => {
        const view = new MockView(2);
        // capture tr passed to dispatch first time
        let firstTr: any;
        view.dispatch = vi.fn((tr: any) => {
            if (!firstTr) firstTr = tr;
        });
        const customData = {a: 1};
        insertNodesToEnd({view: view as any, nodes: [] as any, isRichText: false, customData});
        // CUSTOM_KEY meta set on first tr
        expect(firstTr).toBeTruthy();
        expect(firstTr).toHaveProperty('_meta');
    });

    it('does not throw when no customData', () => {
        const view = new MockView(1);
        expect(() => insertNodesToEnd({view: view as any, nodes: [] as any, isRichText: false})).not.toThrow();
    });
});
