import {msg2html} from '@components/RichTextEditor/helpers/transformData';
import {DOMParser} from 'prosemirror-model';
import {Selection} from 'prosemirror-state';

import {CUSTOM_KEY} from './constants';

import type {RTMNode} from '@frontend/typings/RichTextMessage';
import type {EditorView} from 'prosemirror-view';

// 将节点直接换行插入到编辑器末尾
export const insertNodesToEnd = ({
    view,
    nodes,
    isRichText,
    customData
}: {
    view: EditorView | undefined;
    nodes: RTMNode[];
    isRichText: boolean;
    customData?: Record<string, any>;
}) => {
    if (!view) return;

    // 将 RTMNode 转换为 HTML
    const html = msg2html(nodes, isRichText);

    // 创建临时 DOM 节点并解析 HTML
    const tmpNode = document.createElement('div');
    tmpNode.innerHTML = html;

    // 使用 ProseMirror 的 DOMParser 解析节点
    const parser = DOMParser.fromSchema(view.state.schema);
    const insertNodes = parser.parse(tmpNode);

    // 获取文档末尾位置
    const {doc} = view.state;
    const endPos = doc.content.size;

    const tr = view.state.tr.insert(endPos, insertNodes.content); // 插入新内容

    // 设置光标到新插入内容的末尾
    const newEndPos = tr.doc.content.size;
    const mappedSelection = Selection.near(tr.doc.resolve(newEndPos), -1);
    tr.setSelection(mappedSelection);

    // 添加自定义数据
    if (customData) {
        tr.setMeta(CUSTOM_KEY, customData);
    }

    view.dispatch(tr);
    view.focus();
    view.dispatch(view.state.tr.scrollIntoView());
};
